
export const echartsResize=(val,initWidth=1920)=>{
    return val * (document.documentElement.clientWidth/initWidth);
}
const baseColor = '#fff'
export const setting = {
    echartsLength:10000,//echarts显示条数 默认10===1000  100===10000
    echartsResize:(val,initWidth=1920)=>{
        return val * (document.documentElement.clientWidth/initWidth);
    },
    baseColor,
    legend:{
        padding:echartsResize(5),
        itemGap:echartsResize(10),
        itemWidth:echartsResize(25),
        itemHeight:echartsResize(14),
        textStyle:{
            fontSize:echartsResize(12),
            color:baseColor,
        },
    },
    tooltip:{
        trigger: 'axis',
        padding:echartsResize(5),
        textStyle:{
            fontSize:echartsResize(12),
        },
    },
    xAxis:{
        type: 'category',
        nameGap:echartsResize(15),
        nameTextStyle:{
            fontSize:echartsResize(10),
            color:baseColor
        },
        axisLabel:{
            color:baseColor,
            fontSize:echartsResize(12),
        },
        axisPointer:{
            type:"shadow"
        },
        axisLine: {
            show:true,
            // symbol:['none', 'arrow'],
            lineStyle:{
                color:baseColor
            }
        },
        axisTick:{
            show:false
        },
    },
    yAxis:{
        type: 'value',
        nameTextStyle:{
            fontSize:echartsResize(10),
            color:baseColor
        },
        nameGap:echartsResize(15),
        splitLine :{ //网格线
            show:false //隐藏或显示
        },
        axisLine:{
            show:true,
            lineStyle:{
                color:'transparent'
            },
        },
        axisLabel:{
            color:baseColor,
            fontSize:echartsResize(12),
        }
    },
    dataZoom:{
        height:echartsResize(20),
        textStyle:{
            color:baseColor
        },
        dataBackground:{
            areaStyle:{
                // color:"transparent"
            }
        },
        handleStyle:{
            color:"#0042c6"
        },
    },

}
