import {setting} from "../config/setting.config"
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom,baseColor}  = setting
import * as echarts from 'echarts'
import dayjs from 'dayjs';
import { fixInteger,downloadTable,generateAndDownloadEChart } from "./common"
export const lineConfig = {
    "load": {
        "name": "全网负荷",
        "color": "#D56D39",
        "sequence": 99,
        "type": "line",
    },
    "feedin": {
        "name": "区外来电",
        "color": "#C1ABD2",
        "sequence": 9,
        "type": "area",
    },
    "stogen_energy_storage": {
        "name": "储能",
        "color": "#00C3A8",
        "sequence": 8,
        "type": "area",
    },
    "stogen_pump_hydro": {
        "name": "抽蓄",
        "color": "rgb(34,94,145)",
        "sequence": 4,
        "type": "area",
    },
    "gen_self": {
        "name": "地方电厂",
        "color": "#7CB01F",
        "sequence": 5,
        "type": "area",
    },
    "gen_hydro": {
        "name": "水电",
        "color": "#B4EDFF",
        "sequence": 2,
        "type": "area",
    },
    "gen_gas": {
        "name": "燃气",
        "color": "#C15A73",
        "sequence": 1,
        "type": "area",
    },
    "wind": {
        "name": "风电",
        "color": "#99E897",
        "sequence": 6,
        "type": "area",
    },
    "solar": {
        "name": "光伏",
        "color": "#FFC451",
        "sequence": 7,
        "type": "area",
    },
    // "gen_heat": {
    //     "name": "热电",
    //     "color": "#F09F40",
    //     "sequence": 1,
    //     "type": "area",
    // },
    "gen_coal": {
        "name": "燃煤",
        "color": "#F96C59",
        "sequence": 0,
        "type": "area",
    },
}
export const getRadarSeries = (indicatorsValue,indicatorsData,partition) => {
    const radarObj=[
        '供电裕度','正备用容量','负备用容量','无惯量电源渗透率','外来电依存度','爬坡能力'
    ]
    const radarObjCopy=[
        '供电裕度','正备用容量','负备用容量','无惯量电源渗透率','本地电源自给率','爬坡能力'
    ]
    let option={
        legend:Object.assign({...legend},
            {
                top:echartsResize(0),
            }
        ),
        radar: [
            {
                indicator: [
                    { name: '供电裕度', max:1},
                    { name: '正备用容量', max:1},
                    { name: '负备用容量', max:1},
                    { name: '无惯量电源渗透率', max:1},
                    { name: '外来电依存度', max:1},
                    { name: '爬坡能力', max:1}
                ],
                radius: "73%",
                splitNumber:4,
                scale:true,
                // nameGap:echartsResize(22),
                axisName: {
                    show:false,
                    // formatter: '{value}',
                    // color: '#FFF',
                    // fontSize:echartsResize(14)
                },
                splitArea: {
                    areaStyle: {
                        color: ['rgb(0, 43, 56)','rgba(3, 107, 130, 0.5)'],
                    }
                },
                axisLine: {
                    show: true,
                    symbol: ['none', 'arrow'],
                    symbolOffset: [0, echartsResize(7)],
                    symbolSize:[echartsResize(8),echartsResize(6)],
                    lineStyle: {
                        color: 'rgb(34, 193, 217)',
                    }
                    
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgb(3, 98, 126)',
                        width:echartsResize(1),
                    }
                }
            }
        ],
        animationDuration:100,
        tooltip:{
        },
        color:'rgb(34, 193, 217)',
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //         
                //     },
                // },
                saveAsImage : {
                    show: true,
                    type: 'png',
                    pixelRatio: 5,
                    backgroundColor:'transparent',
                    title: "保存为图片",
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                },
            }
        },
        series: [
            {
                type: 'radar',
                data: [
                    {
                        value: [indicatorsValue.power_supply_margin, indicatorsValue.reserve_low, indicatorsValue.peak_shaving, indicatorsValue.non_inertia_penetration, partition=='全省'? indicatorsValue.feedin_dependence:(1-indicatorsData.feedin_dependence), indicatorsValue.ramp_cap_upward],
                        values: [indicatorsData.power_supply_margin, indicatorsData.reserve_low, indicatorsData.peak_shaving, indicatorsData.non_inertia_penetration, partition=='全省'? indicatorsData.feedin_dependence:(1-indicatorsData.feedin_dependence), indicatorsData.ramp_cap_upward],
                        symbolSize: echartsResize(5),
                        symbol:'none',
                        label: {
                            show: false,
                        },
                        tooltip:{
                            formatter:(params)=>{
                                let str = ''
                                let obj = partition=='全省'?radarObj:radarObjCopy
                                params.data.values.forEach((item,index1)=>{
                                    str += obj[index1] +' : '+ (index1==3||index1==4? (item*100).toFixed(0)+'%':item.toFixed(0)) +'<br>'
                                })
                                return str
                            }
                        },
                        itemStyle:{
                        },
                        areaStyle:{
                            opacity:0.5
                        },
                        lineStyle:{
                            width:echartsResize(1),
                        },
                    },
                ],
                z:3,
            },
        ]
    }
    return option
}
export const getLineNewSeries = (data,time) => {
    let option={
        grid: {
            x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(45),
            y2: echartsResize(35),
        },
        tooltip: Object.assign({...tooltip},{
            valueFormatter:(value)=>value.toFixed(2)+' 万千瓦',
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),

            }
        ),
        color:[new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgb(159, 81, 255)'
        }, {
            offset: 1,
            color: 'rgba(124, 59, 205, 0)'
        }]),new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgb(59, 205, 190)'
        }, {
            offset: 1,
            color: 'rgba(59, 205, 190, 0)'
        }]), 
        "#7CB01F","#F09F40"],
        xAxis: Object.assign({ ...xAxis }, {
            data:time.map(item=>item.slice(10, 16)),
            axisPointer:{
                type:'line'
            },
            axisLine:{
                lineStyle:{
                  color:"rgba(255,255,255,0.5)",
                }
            },
        }),
        yAxis:Object.assign({...yAxis},{
            name: '功率(万千瓦)',
        }),
        series:  data.map((item,index)=>{
            return{
                symbol: 'circle',
                type: 'line',
                showSymbol: false,
                animation: false,
                sampling: 'lttb',
                smooth:true,
                emphasis: {
                    disabled: true
                },
                name:item.name,
                stack: item.stack,
                lineStyle: {
                    width: item.stack?0:1
                },
                data: item.data,
                areaStyle: item.stack?{
                    
                }:undefined,
            }
        })
    }
    return option
}
export const getLineSeries = (chart,charts_data,time,showDataZoom,start=0,end=5,showAll=false) => {
    const lineConfigs = Object.keys(charts_data).filter(items =>lineConfig[items]).map(item => Object.assign({ lineName: item },lineConfig[item])).sort((a,b)=>a.sequence-b.sequence)
    const legendData = lineConfigs.map(item => item.name)
	const legendColor = lineConfigs.map(item => item.color)
    const legendSelected = {}
    legendData.forEach((item, index) => {
        legendSelected[item] =  true 
    })
	const option = {
        color: legendColor,
        tooltip:Object.assign({...tooltip},{
            formatter: (params) => {
                let tooltipContent = params[0].name + '<br/>';
                params.forEach(item => {
                    tooltipContent +=item.marker+ item.seriesName + ' : ' + fixInteger(item.data)+'万千瓦' + '<br/>'; 
                });
                if(showAll) tooltipContent += '总值 : '+fixInteger(params.reduce((a, b) => a + b.data, 0))+'万千瓦' ; 
                return tooltipContent;
            },
        }),
		legend: Object.assign({...legend},{
			data: legendData,
            top: 0,
            width:!showDataZoom?echartsResize(360):'',
            itemGap:echartsResize(0)
		}),
		grid: {
			x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(45),
            y2: echartsResize(35),
		},
		xAxis:Object.assign({ ...xAxis }, {
            data:time,
            axisPointer:{
                type:'line'
            },
        }),
		yAxis:Object.assign({...yAxis},{
            name: '功率(万千瓦)',
        }),
        dataZoom: !showDataZoom ? [] : [
            Object.assign({ ...dataZoom }, {
                bottom: echartsResize(10),
                start,
                end,
                realtime: false,
            })
        ],
        toolbox: {
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                mySelectAll: {
                    show: true,
                    title: "全部选中",
                    icon: "image://"+new URL(`@/assets/select_all.png`, import.meta.url).href,
                    onclick: () => {
                        chart.dispatchAction({
                            type: 'legendAllSelect'
                        })
                    },
                },
                myClearSelect: {
                    show: true,
                    title: "全部取消选中",
                    icon: "image://"+new URL(`@/assets/clear_select.png`, import.meta.url).href,
                    onclick: () => {
                        let opt = chart.getOption();
                        let obj={}
                        opt.series.forEach(item=>{
                            obj[item.name]=false
                        })
                        opt.legend[0].selected=obj
                        chart.setOption(opt);
                    },
                },
                myDownload: {
                    show: true,
                    title: '下载数据',
                    icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                    onclick: () => {
                        downloadTable(charts_data,legendData)
                    },
                },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
		series: lineConfigs.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',
				showSymbol: false,
				color: item.color,
				data: charts_data[item.lineName],
				stack: item.type == 'area' ? '总量' : item.name
			}, item.type == 'area' ? {
				areaStyle: {
					opacity: 1
				},
				lineStyle: {
					width: 0
				}
			} : {})
		}).filter(item => item.data).sort((a, b) => a.sequence - b.sequence)
	}
	return option
}
export const getBarOption = (chart,type,data)=>{
    const count = data.reduce((a, b) => a + b, 0)
	let option = {
		tooltip: Object.assign({...tooltip},{
			trigger: 'item',
			valueFormatter:(value)=>value+ (type=='line'?' 条':' 台')
        }),
		xAxis:Object.assign({...xAxis},{
			data:['0-30%','30%-50%','50%-80%','80%-100%','100%以上'],
            axisLabel: {
                color:baseColor,
				lineHeight: echartsResize(16),
				interval :0
			}
		}),
		grid:{
			x: echartsResize(50),
			y: echartsResize(45),
			x2: echartsResize(25),
			y2: echartsResize(35),
		},
		yAxis:Object.assign({...yAxis},{
			splitNumber:3,
		}),
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //         
                //     },
                // },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
		series:[
			{
				type: 'bar',
				// name:'供电裕度',
				data:data,
				barWidth:echartsResize(25),
				label:{
					show:true,
					position:'top',
					color:baseColor,
					fontSize:echartsResize(12),
					formatter:(params)=>{
                        if(params.data==0) return 0
						return params.data+(type=='line'?'条':type=='trafo'?'台':'个')+'/'+fixInteger(100*params.data/count)+'%'
					}
				},
				itemStyle: {
					color: function(params) {
						// 定义一个颜色数组colorList
						var colorList = ['#11cbd7', '#55e9bc', '#fff5a5','#ffaa64','#ff6464']
						return colorList[params.dataIndex]
					},
					// color:type=='line'?new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
					//     offset: 0,
					//     color: 'rgba(0, 211, 231, 1)' // 渐变起始颜色
					// }, {
					//     offset: 1,
					//     color: 'rgba(6, 43, 87, 1)' // 渐变结束颜色
					// }]):new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
					//     offset: 0,
					//     color: 'rgba(0, 231, 207, 1)' // 渐变起始颜色
					// }, {
					//     offset: 1,
					//     color: 'rgba(6, 65, 87, 1)' // 渐变结束颜色
					// }])
				},
			}
		],
	}
	return option
}