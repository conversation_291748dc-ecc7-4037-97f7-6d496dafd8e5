import {setting} from "../config/setting.config"
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom}  = setting
import * as echarts from 'echarts'
import { downloadTable ,generateAndDownloadEChart } from "./common"
import dayjs from 'dayjs';
export const lineConfig = {
    "load": {
        "name": "全网负荷",
        "color": "#D56D39",
    },
    "solar": {
        "name": "光伏",
        "color": "rgb(210, 179, 48)",
    },
    "wind": {
        "name": "风电",
        "color": "rgb(84, 178, 208)",
    },
    "new_energy": {
        "name": "新能源",
        "color": "rgb(118, 178, 117)",
    },
}
export const getLineSeries = (chart,data,name) => {
    const lineConfigs = Object.keys(data).filter(items =>lineConfig[items]).map(item => Object.assign({ lineName: item },lineConfig[item])).concat({
        name,
        color:'rgb(248, 237, 155)',
        lineName:'value'
    })
    const legendData = lineConfigs.map(item => item.name)
	const legendColor = lineConfigs.map(item => item.color)
    let option={
        grid: {
            x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(45),
            y2: echartsResize(35),
        },
        tooltip: Object.assign({...tooltip},{
            valueFormatter:(value)=>value.toFixed(2)+' 万千瓦',
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
                data:legendData
            }
        ),
        color: legendColor,
        toolbox: {
            show : true,
            top:0,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                myDownload: {
                    show: true,
                    title: '下载数据',
                    icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                    onclick: () => {
                        downloadTable(data,legendData)
                    },
                },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
        xAxis: Object.assign({ ...xAxis }, {
            data:data.time_range.map(item=>item.slice(5, 16)),
            axisPointer:{
                type:'line'
            },
            axisLine:{
                lineStyle:{
                  color:"rgba(255,255,255,0.5)",
                }
            },
        }),
        yAxis:[Object.assign({...yAxis},{
            name:'发电/受入',
        }),Object.assign({...yAxis},{
            name:'负荷',
            position:'right',
        })],
        series:  lineConfigs.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',
				showSymbol: false,
				color: item.color,
				data: data[item.lineName],
                yAxisIndex: item.lineName === 'load' ? 1 : 0,
			})
		})
    }
    return option
}