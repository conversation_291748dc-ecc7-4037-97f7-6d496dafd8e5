import { setting } from "../config/setting.config"
const { echartsResize, echartsLength, legend, tooltip, xAxis, yAxis, dataZoom } = setting
import * as echarts from 'echarts'
import dayjs from 'dayjs';
import {splitLineData} from '@/utils/constants'
import { fixInteger,downloadTableSimple,generateAndDownloadEChart } from "./common"
export const getBarOption = (chart,{ showLegend, xAxisName,yAxisName, data, xAxisData, unit ,linearGradient,showXAxisSymbol,showYAxisLine,showToolBoox=true}) => {
    let option = {
        grid: {
            x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(55),
            y2: echartsResize(35),
        },
        tooltip: Object.assign({ ...tooltip }, {
            valueFormatter: (value) => value.toFixed(2) + unit,
        }),
        legend: Object.assign({ ...legend },
            {
                top: echartsResize(10),
                show: showLegend,
            }
        ),
        toolbox: {
            show : showToolBoox,
            top:30,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //
                //     },
                // },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
        color:!linearGradient?data.map(item=>item.color): data.map(item => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: item.color
            }, {
                offset: 1,
                color: 'rgba(0,0,0,0)'
            }])
        }),
        xAxis: Object.assign({ ...xAxis }, {
            name: xAxisName,
            data: xAxisData,
        },showXAxisSymbol?{
            axisLine:{
                symbol:['none', 'arrow'],
                lineStyle:{
                    color:'#fff'
                }
            },
        }:{
            axisLine:{
                lineStyle:{
                    color:'#fff'
                }
            },
        }),
        yAxis: Object.assign({ ...yAxis }, {
            name: yAxisName,
            axisLine:{
                lineStyle:{
                    color:'#fff'
                },
                show:showYAxisLine
            }
        }),
        series: data.map(item => {
            return {
                name: item.name,
                data: item.data,
                type: 'bar',
                barWidth: data.length>1?echartsResize(25) : xAxisData.length&&xAxisData.length<=10? echartsResize(45):undefined,
                stack:item.stack?item.stack:undefined
            }
        })
    }
    return option
}
export const getLineOption = (chart,{ yAxisName, data, xAxisData, unit, showDataZoom,showSum,showToolBoox=true ,legendType='plain'}) => {
    const option = {
        tooltip: Object.assign({ ...tooltip },!showSum? {
            valueFormatter: (value) => value.toFixed(2) + unit,
        }:{
            formatter: function (params) {
                let str = params.reduce((a, b) => {
                    return a+ `<p>${b.marker} ${b.seriesName} <span class='p_span'>${fixInteger(b.data,2)}</span> ${unit}</p>`
                },'')+`<p>总计 ${fixInteger(params.reduce((a, b)=>a+b.data,0),2)}  ${unit}</p>`
                return `<div class='echarts-tooltip'>
                    <p>${ params[0].axisValue}</p>
                    <div>${str}</div>
                </div>`
            },
        }),
        legend: Object.assign({ ...legend }, {
            top: 0,
            type:legendType,
            pageIconColor:'#fff',
            pageTextStyle:{
                color:'#fff'
            }
        }),
        toolbox: {
            show : showToolBoox,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                mySelectAll: {
                    show: true,
                    title: "全部选中",
                    icon: "image://"+new URL(`@/assets/select_all.png`, import.meta.url).href,
                    onclick: () => {
                        chart.dispatchAction({
                            type: 'legendAllSelect'
                        })
                    },
                },
                myClearSelect: {
                    show: true,
                    title: "全部取消选中",
                    icon: "image://"+new URL(`@/assets/clear_select.png`, import.meta.url).href,
                    onclick: () => {
                        let opt = chart.getOption();
                        let obj={}
                        opt.series.forEach(item=>{
                            obj[item.name]=false
                        })
                        opt.legend[0].selected=obj
                        chart.setOption(opt);
                    },
                },
                myDownload: {
                    show: true,
                    title: '下载数据',
                    icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                    onclick: () => {
                        downloadTableSimple(data,xAxisData)
                    },
                },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
        color: data.map(item => item.color),
        grid: {
            x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(45),
            y2: echartsResize(35),
        },
        xAxis: Object.assign({ ...xAxis }, {
            data: xAxisData,
            axisPointer: {
                type: 'line'
            }
        }),
        yAxis: Object.assign({ ...yAxis }, {
            name: yAxisName,
        }),
        dataZoom: !showDataZoom ? [] : [
            Object.assign({ ...dataZoom }, {
                bottom: echartsResize(10),
                start: 0,
                end: 10,
                realtime: false,
                show:true
            })
        ],
        series: data.map(item => {
            return {
                type: 'line',
                name: item.name,
                data: item.data,
                stack: item.stack,
                // symbol:'rect',
                showSymbol: false,
                lineStyle: {
                    width: item.hiddenLine ? 0 : 1
                },
                areaStyle: item.showArea ? {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: item.areaColor
                    }, {
                        offset: 1,
                        color: 'rgba(0,0,0,0)'
                    }]),
                } : undefined
            }
        })
    }
    return option
}
export const getMapSeries = (type,partition, data, station, limitData) => {
    const visualMap = [
        {value: 'stogen_pump_hydro', label: '抽蓄', color: '#266EB0',type:[0,1,2]}, 
        {value: 'gen_hydro', label: '水电', color: '#B4EDFF',type:[0,1,2]}, 
        {value: 'gen_coal', label: '燃煤', color: '#F96C59',type:[0,1,2]},  
        {value: 'gen_gas', label: '燃气', color: '#C15A73',type:[0,1,2]},  
        {value: 'wind', label: '风电', color: '#99E897',type:[1,2]}, 
        {value: 'solar', label: '光伏', color: '#FFC451',type:[1,2]},
        {value: 'stogen_energy_storage', label: '储能', color: '#4693FF',type:[0,1,2]}
    ].filter(item=>item.type.includes(type)&&station.find(items=>items.value[2]===item.value))
    let option = {
        geo: {
            map: partition,
            zoom: partition == '全省' ? 1.2 : 1,
            silent: true,
            animation: false,
            // center:null,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 0,
                shadowOffsetY: 10,
                shadowBlur: 2
            },
            scaleLimit: {
                min: partition == '全省' ? 1 : 1,
            },
            emphasis: {
                disabled: true,
            },
            show: true,
            roam: true,
        },
        animation: false,
        tooltip: {
            padding: 0,
            backgroundColor: 'transprate',
            borderWidth:0,
            formatter: (params) => {
                if (params.componentSubType == 'map') {
                    if (params.data == undefined) return
                    let str = Object.keys(params.data.values).map(item => {
                        return {
                            name: item,
                            value: params.data.values[item]
                        }
                    }).filter(items => {
                        if (limitData) {
                            return limitData.includes(items.name)
                        } else {
                            return items
                        }
                    }).reduce((a, b) => {
                        return a + `<p>${b.name} :<span>${b.value}</span></p>`
                    }, '')

                    return `<div class='echarts_map_tooltip''>
                            <p>${ params.data.name}</p>
                            <div>
                                ${str}
                            </div>
                    </div>`
                }else if(params.componentSubType=='scatter'){
                    return `<div class='echarts_map_tooltip''>
                            <p>${ params.data.name}</p>
                            <div>
                                <p>装机容量 :<span>${params.data.capacity}</span></p>
                                <p>电压等级 :<span>${params.data.vlevel}</span></p>
                            </div>
                    </div>`
                }
            },
        },
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //         
                //     },
                // },
                saveAsImage : {
                    show: true,
                    type: 'png',
                    pixelRatio: 5,
                    backgroundColor:'transparent',
                    title: "保存为图片",
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                },
            }
        },
        visualMap: [
            {
                show: partition=='全省',
                seriesIndex: [1],
                zlevel:3,
                type:"piecewise",
                itemGap:echartsResize(10),
                itemWidth:echartsResize(20), 
                itemHeight:echartsResize(14),
                textGap:echartsResize(10),
                pieces:visualMap,
                left:echartsResize(15),
                bottom:echartsResize(15),
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            }
        ],
        series: [
            // 0 地图
            {
                type: 'map',
                zlevel: 1,
                map: partition,
                // center:null,
                zoom: partition == '全省' ? 1.2 : 1,
                roam: true,
                scaleLimit: {
                    min: partition == '全省' ? 1 : 1,
                },
                label: {
                    show: true,
                    fontSize: partition == '全省' ? echartsResize(10) : echartsResize(20),
                    
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc',
                    areaColor: 'rgb(44,82,118)',
                },
                data: data.map(item=>{
                    return Object.assign({...item},{
                        label:{
                            color: "#fff"
                        },
                        itemStyle:{
                            
                        }
                    })
                }),
                emphasis: {
                    disabled: true,
                },
            },
            // 1 全省场站
            {
                animation:false,
                zlevel: 2,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                    return  echartsResize(5) * option.geo.zoom;
                },
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                label: {
                    show: false,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize: echartsResize(10),
                    position: 'top',
                    color: '#fff',
                },
                data:partition=='全省'?station:[]
            },
            // 2 分区场站
            {
                animation:false,
                zlevel: 2,
                // show:partition=='全省'?false:true,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                    return  echartsResize(20) * option.geo.zoom;
                },
                symbol: (value, params) => {
                    if(params.data.type=='wind'){
                        return "image://"+new URL(`@/assets/icon/风电.png`, import.meta.url).href
                    }else if(params.data.type=='gen_hydro'){
                        return "image://"+new URL(`@/assets/icon/抽蓄.png`, import.meta.url).href
                    }else if(params.data.type=='solar'){
                        return "image://"+new URL(`@/assets/icon/光伏.png`, import.meta.url).href
                    }else{
                        return "image://"+new URL(`@/assets/icon/火电.png`, import.meta.url).href
                    }
					
				},
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize: echartsResize(10),
                    position: 'top',
                    color: '#fff',
                },
                data:partition=='全省'?[]:station
            },
            // 3 边界
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                polyline:true,
                label:{
                    show:false,
                },
                lineStyle: {
                    width: echartsResize(2.5), 
                    opacity: 1, 
                    color:'gray'
                },
                data:partition=='全省'?Object.keys(splitLineData).map(item=>{
                    return {
                        coords:splitLineData[item]
                    }
                }):[],
            },
        ],
    }
    return option
}
export const getPieOption = ({ data, nameList, color }) => {
    const optiton = {
        type: 'pie',
        radius: ['40%', '65%'],
        selectedOffset: echartsResize(20),
        selectedMode: 'false',
        label: {
            show: true,
            formatter: (params) => {
                return params.percent.toFixed(0) + '%'
            },
            color: '#fff',
        },
        labelLine: {
            length: 10,
            length2: 10,
            lineStyle: {
                width: 1,
            }
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }
    return {
        legend: Object.assign({ ...legend },
            {
                bottom: 0,
            }
        ),
        tooltip: Object.assign({ ...tooltip }, {
            trigger: 'item',
            // formatter: (params) => {
            //     return params.marker + ' ' + params.name+ ' ' + params.value + (params.componentIndex==0 ? ' 万千瓦': ' 亿千瓦时')
            // }
            // valueFormatter:(value)=>value.toFixed(2)+(params.componentIndex==0 ==1?' 万千瓦时':' 亿千瓦时'),
        }),
        color,
        series: [
            {
                ...optiton,
                data: data.map((item, index) => {
                    return {
                        name: nameList[index],
                        value: item,
                    }
                })
            },
        ]
    }
}
