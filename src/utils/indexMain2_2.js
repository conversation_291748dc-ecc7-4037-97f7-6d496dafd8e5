import {setting} from "../config/setting.config"
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom}  = setting
import { lineConfig } from './indexMain1'
import { generateAndDownloadEChart } from '@/utils/common'
import * as echarts from 'echarts'
import dayjs from 'dayjs';
export const getPieSeries = (chart,data1,data2,obj={
    gen_coal:'',
    gen_heat:'',
    solar:'',
    wind:'',
    gen_gas:'',
    gen_hydro:'',
    stogen_pump_hydro:'',
}) => {
    const colorList =Object.keys(obj).filter(items =>lineConfig[items]).map(item => lineConfig[item]).sort((a,b)=>a.sequence-b.sequence)
    const optitons = {
        type: 'pie',
        radius: ['40%','65%'],
        selectedOffset:echartsResize(20),
        selectedMode: 'false',
        color: colorList.map(item=>item.color),
        label: {
            show: true,
            formatter:(params)=>{
                return params.value+'/'+params.percent.toFixed(0)+'%'
            },
            color:'#fff',
        },
        labelLine: {
            length: 5,
            length2: 5,
            lineStyle: {
                width: 1,
            }
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }
    let option = {
        legend:Object.assign({...legend},
            {
                top:'center',
                orient:'vertical'
            }
        ),
        tooltip: Object.assign({...tooltip},{
            trigger: 'item',
            formatter: (params) => {
                return params.marker + ' ' + params.name+ ' ' + params.value + (params.componentIndex==0 ? ' 万千瓦': ' 亿千瓦时')
            }
            // valueFormatter:(value)=>value.toFixed(2)+(params.componentIndex==0 ==1?' 万千瓦时':' 亿千瓦时'),
        }),
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //
                //     },
                // },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
        // color:['#91cc75','#73c0de','#5470c6','#fac858','#ee6666'],
        series: [
            {
                ...optitons,
                center: ['25%', '45%'],
                data:data1.map((item,index)=>{
                    return {
                        value: item,
                        name: colorList.map(item=>item.name)[index]
                    }
                }).filter(item=>item.name)
            },
            {
                ...optitons,
                center: ['75%', '45%'],
                data:data2.map((item,index)=>{
                    return {
                        value: item,
                        name: colorList.map(item=>item.name)[index]
                    }
                }).filter(item=>item.name)
            }
        ]
    }
    return option
}
