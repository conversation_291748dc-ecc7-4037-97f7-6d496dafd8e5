import {setting} from "../config/setting.config"
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom}  = setting
import * as echarts from 'echarts'
import {getTangentialPoint,pointOnLine,centerOnLine,getRotate,getPointOnCubicBezier,fixInteger } from './common'
import { partitionLinesCoordinate,partitionScale } from "./constants"
import { getGisStyle,getSymbolPath } from "./indexMain"
export const getGradientColor=(startColor, endColor, percent)=> {
    // 将十六进制颜色转换为RGB
    const hexToRgb = hex =>
        hex.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,
                (m, r, g, b) => '#' + r + r + g + g + b + b)
        .substring(1).match(/.{2}/g)
        .map(x => parseInt(x, 16));

    const rgbStart = hexToRgb(startColor);
    const rgbEnd = hexToRgb(endColor);

    // 计算中间色的RGB值
    const rgbPercent = rgbStart.map((start, index) => {
        const end = rgbEnd[index];
        const diff = end - start;
        return Math.round(start + diff * percent);
    });

    // 将RGB值转换回十六进制颜色
    const rgbToHex = rgb =>
        "#" + rgb.map(x =>
        x.toString(16).padStart(2, '0')
        ).join('');

    return rgbToHex(rgbPercent);
}
export function getRandomColor() {
  // 生成随机整数并转换为十六进制字符串
  const randomColor = Math.floor(Math.random() * 16777215).toString(16);
  // 补零，确保颜色字符串长度为6
  return `#${randomColor.padStart(6, '0')}`;
}
export function rgbToHex(r, g, b) {
    // 将一个数值转换为十六进制字符串，确保结果是两位数
    const toHex = (c) => {
        const hex = c.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
    };

    // 转换每个颜色分量，然后合并为完整的十六进制颜色码
    return "#" + toHex(r) + toHex(g) + toHex(b);
}
export const getFeedinLine = (data,area,number)=>{
    let data1=[]
    let data2=[]
    if(area!='全省'){

    }else{
        data1=[
        {
            lineName:'天中直流',
            value:data.feedin1,
            coords:[[114.0142, 34.7273],  [113.71691493597375, 35.327651913303924],[111.3128646285729,  35.327651913303924]].reverse()
        },
        {
            lineName:'灵宝直流',
            value:data.feedin2,
            coords:[[110.5324, 34.3629],[110.069218, 34.3629]].reverse()
        },
        {
            lineName:'青豫直流',
            value:data.feedin3,
            coords:[[114.56222, 33.250043], [114.19672517364144, 33.359359762805626],[110.68808223481628, 33.359359762805626]].reverse()
        },
        {
            lineName:'青豫直流',
            value:data.feedin3,
            coords:[[114.717797, 33.08905], [114.19672517364144, 33.359359762805626],[110.68808223481628, 33.359359762805626]].reverse()
        },
        {
            lineName:'豫武特高压',
            value:data.feedin6,
            coords:data.feedin6<0?[[114.09,30.43],[114.7177972,33.0890495]].reverse().map(item=>{
              return [item[0]-0.025,item[1]]
            }):[[114.09,30.43],[114.7177972,33.0890495]].map(item=>{
              return [item[0]-0.025,item[1]]
            })
        },
        {
            lineName:'豫武特高压',
            value:data.feedin6,
            coords:data.feedin6<0?[[114.09,30.43],[114.7177972,33.0890495]].reverse().map(item=>{
              return [item[0]+0.025,item[1]]
            }):[[114.09,30.43],[114.7177972,33.0890495]].map(item=>{
              return [item[0]+0.025,item[1]]
            })
        },
        {
            lineName:'南荆特高压',
            value:data.feedin5,
            coords:data.feedin5<0?[[112.199265,31.035423],[112.8612633,33.40713271]].reverse().map(item=>{
              return [item[0]-0.025,item[1]]
            }):[[112.199265,31.035423],[112.8612633,33.40713271]].map(item=>{
              return [item[0]-0.025,item[1]]
            })
        },
        {
            lineName:'南荆特高压',
            value:data.feedin5,
            coords:data.feedin5<0?[[112.199265,31.035423],[112.8612633,33.40713271]].reverse().map(item=>{
              return [item[0]+0.025,item[1]]
            }):[[112.199265,31.035423],[112.8612633,33.40713271]].map(item=>{
              return [item[0]+0.025,item[1]]
            })
        },
        {
            lineName:'长南特高压',
            value:data.feedin4,
            coords:data.feedin4<0?[[113.1162,36.1953],[112.8612633,33.40713271]].reverse():[[113.1162,36.1953],[112.8612633,33.40713271]]
        },
        ]
        data2 = [
        {
            name:'天中直流',
            value:[111.3128646285729, 35.327651913303924,data.feedin1]
        },
        {
            name:'灵宝直流',
            value:[110.069218, 34.3629,data.feedin2]
        },
        {
            name:'青豫直流',
            value:[110.68808223481628, 33.359359762805626,data.feedin3]
        },
        {
            name:'豫武特高压',
            value:[114.09,31.33,data.feedin6]
        },
        {
            name:'南荆特高压',
            value:[112.199265,31.33,data.feedin5]
        },
        {
            name:'长南特高压',
            value:[113.1162,36.1953,data.feedin4]
        },
        ]
    }
    return{
        data1,
        data2
    }
}
export const getLoadOption = (index,data)=>{
    const start = index-index%24
    const end = start + 23
    let datas = data.map(items=>items.toFixed(0)).filter((item,index)=>index>=start&&index<=end)
    let option = {
        title: {
                
        },
        grid: {
            x: echartsResize(45),
            y: echartsResize(30),
            x2: echartsResize(5),
            y2: echartsResize(20),
        },
        tooltip: Object.assign({...tooltip},{
            formatter:(params)=>{
                let str = "<span style='display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:red;'></span>" + ' ' +params[0].name+'时 '+ params[0].seriesName+' ' + params[0].value +'万千瓦' 
                return str
            }
        }),
        xAxis: Object.assign({...xAxis},{
            data:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
            axisPointer:{
                type:'line'
            },
        }),
        yAxis:Object.assign({...yAxis},{
            name:"万千瓦",
            min: function (value) {
                return (Math.floor((value.min)/1000))*1000;
            },
            splitNumber:3,
        }),
        color:['#00FFB6'],
        series:[
            {
                animation:false,
                sampling: 'average',
                smooth:true,
                symbolSize:echartsResize(0),
                name: '用电负荷',
                type: 'line',
                markPoint:{
                    symbol:'circle',
                    symbolSize:echartsResize(7),
                    itemStyle:{
                        color:'#fff',
                        borderColor:'red',
                        borderWidth:echartsResize(2)
                    },
                    data:[
                        {
                            coord: [index%24, datas[index%24]]
                        }
                    ]
                },
                data:datas,
                areaStyle:{
                    color:new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#145fff' },
                        { offset: 1, color: 'rgba(0,255,255,0)' }
                    ])
                } 
            }
        ],
    }
    return option
}
export const geCalLineData = (data,flag,datas)=>{
    if(flag){
        return data.map((item,index)=>{
            return Object.assign({ ...item }, {
                change:item.p_rate-datas[index].p_rate,
            })
        }).sort((a,b)=>b.p_rate-a.p_rate)
    }else{
        return data.map(item=>{
            return Object.assign({ ...item }, {
                change:0
            })
        }).sort((a,b)=>b.p_rate-a.p_rate)
    }
}
export const getCalInterfaceData = (data,flag,datas)=>{
    if(flag){
        return data.map((item,index)=>{
            return Object.assign({ ...item }, {
                change:item.power-datas[index].power,
            })
        })
    }else{
        return data.map(item=>{
            return Object.assign({ ...item }, {
                change:0
            })
        })
    }
}
export const getContrastPartitionData = (data,flag,datas)=>{
    if(flag){
        return data.map((item,index)=>{
            return Object.assign({ ...item }, {
                change:item.values-datas[index].values,
            })
        })
    }else{
        return data.map(item=>{
            return Object.assign({ ...item }, {
                change:0
            })
        })
    }
}
export const getContrastPartitionList = (data1,data2,flag)=>{
    if(flag){
        return data1.map(item=>{
        let items = data2.find(item1=>item1.name==item.name)
            return Object.assign({...item},{
                valueLists:items.valueList,
                value1s:items.value1,
                value2s:items.value2,
                value3s:items.value3,
                change1:item.value1- items.value1,
                change2:item.value1- items.value2,
                change3:item.value3- items.value3,
            })
        })
    }else{
        return data2.map(item=>{
            return Object.assign({...item},{
                valueLists:item.valueList,
                value1s:item.value1,
                value2s:item.value2,
                value3s:item.value3,
                change1:0,
                change2:0,
                change3:0,
            })
        })
    }
}
export const getCalMapOption = (data,datas, partition, {point_data, line_data,line_data_n1,point_data_n1 },BoundaryCondition, isShowFeedin,flag1, mapType, flag3, equipmentIds) => {
    const { data1, data2, data3 } = getFeedinLine(BoundaryCondition, partition, 0)
    let lineData = []
    let lineDatas = []
    let mapData = []
    let markPointData = []
    if(mapType==1){
        mapData = data
    } else {
        mapData = datas
    }
    if(flag1){
        line_data.concat(line_data_n1?line_data_n1:[]).forEach(item=>{
            let arr = Object.keys(item.info)
            if(arr.length==1){
                let obj = Object.assign({...item},{
                    id:item.info[arr[0]],
                    name:arr[0],
                    coord:item.coords,
                })
                lineData.push(obj)
            }else if(arr.length==2||arr.length==3||arr.length==4){
                let coords
                if(arr.length==2){
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.003*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale,item.coords[1][0],item.coords[1][1],0.003*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                    })
                    let line2 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                    })
                    lineData = lineData.concat([line1,line2])
                }else{
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.005*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale,item.coords[1][0],item.coords[1][1],0.005*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                    })
                    let line2 = Object.assign({...item},{
                        id:arr.length==3?item.info[arr[2]]:item.info[arr[3]],
                        name:arr.length==3?arr[2]:arr[3],
                        coord:item.coords,
                        coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                    })
                    lineData = lineData.concat([line1,line2])
                    if(arr.length==3){
                        let obj = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                        })
                        lineData.push(obj)
                    }else if(arr.length==4){
                        let coords
                        coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.002*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale,item.coords[1][0],item.coords[1][1],0.002*partitionLinesCoordinate['漯河'].scale/partitionLinesCoordinate[partition].scale)
                        let line3 = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                            coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                        })
                        let line4 = Object.assign({...item},{
                            id:item.info[arr[2]],
                            name:arr[2],
                            coord:item.coords,
                            coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                        })
                        lineData = lineData.concat([line3,line4])
                    }
                }
            }
        })
        lineDatas = lineData.filter(item=>equipmentIds.includes(item.id))
        lineData = lineData.filter(item => !equipmentIds.includes(item.id))
        // lineData.forEach(item=>{
        //   if(phase_shifter_arg.line_index&&phase_shifter_arg.line_index.includes(item.id)){
        //     let value
        //     let rotate = getRotate(item.coords[0],item.coords[1])
        //     if(item.fromName==phase_shifter_arg.point_name){
        //       value = pointOnLine(item.coords[0],item.coords[1],0.1)
        //     }else{
        //       value = pointOnLine(item.coords[1],item.coords[0],0.1)
        //     }
        //   phaserData.push(Object.assign({...item},{...phase_shifter_arg},{value, symbol:"image://"+new URL('@/assets/svg/移相器.svg', import.meta.url).href,type:'phaser', symbolRotate:rotate}))
        //   }
        //   if(add_reactance_arg.line_index&&add_reactance_arg.line_index.includes(item.id)){
        //     let value
        //     let rotate = getRotate(item.coords[0],item.coords[1])
        //     value = centerOnLine(item.coords[0],item.coords[1])
        //   phaserData.push(Object.assign({...item},{...add_reactance_arg},{value,symbol:"image://"+new URL('@/assets/svg/串抗.svg', import.meta.url).href,type:'reactance',symbolRotate:rotate}))
        //   }
        // })
    }
    if(!flag3){
        markPointData = lineData.map(item=>{
            return{
                value: pointOnLine(item.coords[0],item.coords[1],0.5).concat(item.value),
                symbolRotate:getRotate(item.coords[0],item.coords[1])-90,
            }
        }).concat(lineDatas.map(item=>{
            return{
                value: pointOnLine(item.coords[0],item.coords[1],0.5).concat(item.value),
                symbolRotate:getRotate(item.coords[0],item.coords[1])-90,
                symbol:  "image://"+new URL('@/assets/icon/cha.png', import.meta.url).href,
                symbolSize: echartsResize(15) 
            }
        }))
        if(partition=='全省'&&isShowFeedin&&flag1){
            markPointData=markPointData.concat(data3)
        }
    }
    let option={
        geo: {
            map: partition,
            zoom: partition=='全省'?1.22 : 1,
            silent: true,
            animation:false,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 8,
                shadowOffsetY: 4,
            },
            scaleLimit:{
                min:partition=='全省'?1.22 : 1,
            },
            emphasis: {
                disabled:true,
            },
            show:true,
            roam:true,
        },
        animation:false,
        tooltip:{
            formatter: (params)=>{
                if(params.componentSubType=='lines'){
                    if([7].includes(params.componentIndex)) return
                    if(params.data.lineName){
                        return params.data.lineName+' : '+params.value+' 万千瓦'
                    }else{
                        return params.data.name+':'+fixInteger(Math.abs(params.value)*100)+'%' 
                    }
                } else if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
                    return params.name+':'+fixInteger(100*params.value[2])+'%'
                    // if(params.data.powerflow_detail.length==0){
                    //   return params.name+':'+params.value[2]+'%'
                    // }  
                    // let str = ''
                    // params.data.powerflow_detail.forEach((item,index)=>{
                    //   str =str+( +((100*item).toFixed(2))==0?0:(100*item).toFixed(2))+ (index==params.data.powerflow_detail.length-1 ? '%' : '%/')
                    // })
                    // return params.name+':'+str
                } else if (params.componentSubType == 'map') {
                    if(params.data.values==undefined) return
                    if (mapType == 1) {
                        let v = (params.data.values).toFixed(2)
                        let c = params.data.value
                        let color = c ==2 ? '#4ec48c' : c==1 ? '#fbb03b':'#bf6c66'
                        let name = params.data.name
                        return `
                        <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                        <span style="font-size: 12px;color: #000000;letter-spacing: 0;">供电裕度</span>
                        <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}万千瓦</span>
                        `
                    } else {
                        let v = fixInteger(100*params.data.value)
                        let name = params.data.name
                        let color = getGradientColor(rgbToHex(233,193,67),rgbToHex(7,192,96),((v-95)/5)>1?1:((v-95)/5))
                        return `
                        <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                        <span style="font-size: 12px;color: #000000;letter-spacing: 0;">新能源消纳率</span>
                        <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}%</span>
                        `
                    }
                }
            },
        },
        visualMap: [
            {
                show: flag1,
                seriesIndex: [1,2,9,13],
                realtime: false,
                type:'piecewise',
                left:echartsResize(10),
                bottom:echartsResize(7),
                itemGap:echartsResize(10),
                itemHeight:echartsResize(14),
                itemWidth:echartsResize(20), 
                textGap:echartsResize(10),
                textStyle:{
                    color:'#fff',
                    fontSize:echartsResize(12),
                },
                pieces: true?[
                    {gt: 1,label: '100%以上', color: '#3D0713'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#AE0000'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#f24a64'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#ff8934'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#FFE175'}                 // (-Infinity, 5)
                    ]:[
                    {gt: 1,label: '100%以上', color: '#ff6464'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#ffaa64'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#fff5a5'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#55e9bc'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#11cbd7'}                 // (-Infinity, 5)
                ]
            },
            {
                show: mapType==1?true:false,
                seriesIndex: mapType==1?[0]:[],
                type:"piecewise",
                itemGap:echartsResize(10),
                itemWidth:flag1?echartsResize(20):echartsResize(30), 
                itemHeight:echartsResize(14),
                textGap:echartsResize(10),
                pieces: flag1?[
                    // {lt: 0, label: '不足', color: '#511e78'},  // [123, 123]
                    // {gt: 0, lte: 20,label: '紧张', color: '#8b2f97'},  // [123, 123]
                    // {gt: 20, label: '充裕', color: '#cf56a1'},  // [123, 123]
                    {value: 0, label: '不足（供电裕度<0）', color: '#253b6e'},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#1f5f8b'},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#1891ac'},  // [123, 123]
                ]:[
                    {value: 0, label: '不足（供电裕度<0）', color: '#bf6c66',symbol:"image://"+new URL(`@/assets/icon/mapIcon1.png`, import.meta.url).href},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#fbb03b',symbol:"image://"+new URL(`@/assets/icon/mapIcon2.png`, import.meta.url).href},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#4ec48c',symbol:"image://"+new URL(`@/assets/icon/mapIcon3.png`, import.meta.url).href},  // [123, 123]
                ],
                inRange: {  //对应范围内颜色
                    color: ['#bf6c66', '#fbb03b', '#4ec48c']
                },
                outOfRange:{
                    color:['rgb(44,82,118)']
                },
                left:echartsResize(105),
                bottom:echartsResize(56),
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            },
            {
                show: mapType==2?true:false,
                seriesIndex: mapType==2?[0]:[],
                min:0.95,
                max:1,
                realtime: false,
                calculable: true,
                color:['rgb(7,192,96)','rgb(233,193,67)'],
                outOfRange:{
                    color:['rgb(0,27,72)']
                },
                // color:['rgb(0,168,252)','rgb(0,35,212)'],
                left:echartsResize(105),
                bottom:echartsResize(25),
                itemHeight:echartsResize(80),
                itemWidth:echartsResize(20),
                formatter: function (value) {
                    return value*100+'%'; // 范围标签显示内容。
                },
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            }
        ],
        series: [
            // 地图 0
            {
                type: 'map',
                zlevel: 1,
                map:partition,
                zoom: partition=='全省'?1.22 : 1,
                roam:true,
                scaleLimit:{
                    min:partition=='全省'?1.22 : 1,
                },
                label: {
                    show: !flag1?true:false,
                    fontSize:partition=='全省'?echartsResize(10):echartsResize(20),
                    color:"#fff"
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc', 
                    areaColor: 'rgb(44,82,118)',
                    shadowColor: 'rgba(40,139,252,0)',
                    shadowOffsetX: 8,
                    shadowOffsetY: 4,
                },
                data: mapData,
                emphasis: {
                    disabled:true,
                },
            },
            // 线路 1
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                effect: {
                    show: flag3,
                    period: 5, 
                    trailLength: 0,
                    symbol: 'arrow',
                    symbolSize: 5 
                },
                labelLayout:{
                // hideOverlap:true,
                // moveOverlap:true,
                },
                label:{
                    show:partition=='全省'?false:false,
                    fontSize:echartsResize(12),
                    formatter: params => {
                        if(params.data.value1){
                        return (params.value==0?0:(100*params.value).toFixed(2))+'% '+'{up|}'+'{up_text|'+(100*params.data.value1).toFixed(0)+'%'+'}';
                        }else if(params.data.value2){
                        return (params.value==0?0:(100*params.value).toFixed(2))+'% '+'{down|}'+'{down_text|'+(100*params.data.value2).toFixed(0)+'%'+'}';
                        }else{
                        return (params.value==0?0:(100*params.value).toFixed(2))+'%';
                        }
                    },
                    rich:{
                        up: {
                            backgroundColor: {
                                image:new URL('@/assets/icon/up.png', import.meta.url).href, // 这是vue写法，不是的按原来的写就行
                            },
                            width: echartsResize(10),
                            height: echartsResize(15),
                        },
                        up_text: {
                            color: '#CE2C1B',
                            fontSize: echartsResize(14),
                        },
                        down: {
                            backgroundColor: {
                                image: new URL('@/assets/icon/down.png', import.meta.url).href, // 这是vue写法，不是的按原来的写就行
                            },
                            width: echartsResize(10),
                            height: echartsResize(15),
                        },
                        down_text: {
                            color: '#05e0bb',
                            fontSize: echartsResize(14),
                        },
                    },
                    position: 'middle',
                    color: '#fff',
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    // curveness: partition=='全省'?0.25:0
                },
                data: flag1 && partition == '全省' ? lineData.filter(item => item.vn_kv > 500).map(items => {
                return Object.assign({
                    ...items
                    },{
                        lineStyle:{
                        // width:items.value>0.8?2.5:2,
                        width:2,
                        // curveness:items.coords[0][0]>items.coords[1][0]?-0.25:0.25
                        }
                    })
                })
                : lineData,
            },
            Object.assign({

            },
            // 500kV scatter 2
            partition=='全省'?
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                symbol: (val,data) => {
                    return data.data.type=='station'?"image://"+new URL('@/assets/svg/变电站.svg', import.meta.url).href:"image://"+new URL('@/assets/svg/中州换.svg', import.meta.url).href
                },
                coordinateSystem: 'geo',
                zlevel: 3,
                itemStyle:{
                    opacity:1
                },
                symbolSize: (val) =>{
                    return  echartsResize(10) * option.geo.zoom
                },
                emphasis: {
                    disabled:true,
                },
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:echartsResize(14),
                    position: 'bottom',
                    color: '#fff',
                },
                data: flag1 && point_data ? point_data.filter(item => item.vn_kv > 500)
                : []
            }
            :
            {
                animation:false,
                zlevel: 3,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                    return  echartsResize(25)* option.geo.zoom;
                },
                symbol:"image://"+new URL('@/assets/svg/变电站.svg', import.meta.url).href,
                // symbol:"image://"+new URL('@/assets/svg/330kV以上变电站.svg', import.meta.url).href,
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:partition!='全省'? echartsResize(15):echartsResize(10),
                    position: 'top',
                    color: '#fff',
                },
                data:flag1&&point_data?point_data.filter(item=>item.vn_kv>500):[]
            }
            ),
            // 330kV下 scatter 3
            {
                animation:false,
                zlevel: 3,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                    return  partition=='全省'?  8 * option.geo.zoom  :15 * option.geo.zoom;
                },
                symbol:"image://"+new URL('@/assets/svg/330kV以下变电站.svg', import.meta.url).href,
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize: echartsResize(10),
                    position: 'top',
                    color: '#fff',
                },
                data:flag1&&partition!='全省'?point_data.filter(item=>item.vn_kv<500):[]
            },
            //n-1主变 4
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'effectScatter',
                showEffectOn:'emphasis',
                coordinateSystem: 'geo',
                zlevel: 3,
                symbolSize: (val) =>{
                    return (partition=='全省'?16: 20 * option.geo.zoom)
                },
                symbol:"image://"+new URL('@/assets/icon/station.png', import.meta.url).href,
                rippleEffect: {
                    period: 4, 
                    brushType: 'stroke'
                },
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize: echartsResize(15),
                    position: 'top',
                    color: '#fff',
                },
                data:flag1&&point_data_n1?point_data_n1:[]
            },
            // 移相器 scatter 5
            {
                animation:false,
                zlevel: 4,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                    return [15 * option.geo.zoom,8 * option.geo.zoom];
                },
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                label: {
                    show: true,
                    fontSize: echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    formatter: params => {
                        return params.data.type=='phaser'? params.data.shift_degree+'°' : params.data.added_reactance_ohm+'Ω';
                    },
                },
                data:[],
            },
            // n-1线路 6
            {
                type: 'lines', 
                animation:false,
                zlevel: 1,
                effect: {
                    show: flag3,
                    period: 5, 
                    trailLength: 0, 
                    symbol:  "image://"+new URL('@/assets/icon/cha.png', import.meta.url).href,
                    symbolSize: echartsResize(15) 
                },
                label:{
                    show:false,
                    position: 'middle',
                    color: '#fff',
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    curveness: 0,
                    type: [5, 10],
                    dashOffset: 10
                },
                data:flag1?lineDatas:[]
            },
            // 区外直流 7 lines
            {
                type: 'lines',
                animation:false,
                zlevel: 1,
                polyline:true,
                effect: {
                    show: flag3,
                    period: 5, 
                    // constantSpeed :50,
                    trailLength: 0, 
                    symbol: 'arrow',
                    symbolSize: 10
                },
                lineStyle: {
                    width: 5, 
                    color: '#8BF1FE',
                    opacity: 1, 
                },
                data:isShowFeedin&&flag1?data1:[],
            },
            // 区外直流名称 scatter 8
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                // symbol:"none",
                symbolSize:0,
                label: {
                    show: true,
                    formatter: params => {
                        // return params.name
                        if (params.value[2] == 0) {
                            return params.name+'  '+'{fline|'+0+'}'+'    '
                        }else if(String(params.value[2]).length<2){
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'+'    '
                        }else if(String(params.value[2]).length<3){
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'+'  '
                        }else{
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'
                        }
                    },
                    fontSize: echartsResize(16),
                    lineHeight:echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    rich:{
                        fline: {
                            color: '#1890ff',
                            fontWeight:'bold',
                            fontSize: echartsResize(16),
                        },
                    },
                },
                data:isShowFeedin&&flag1?data2:[],
            }, 
            // 静态箭头 scatter 9
            {
                animation:false,
                zlevel: 1,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbol:'arrow',
                symbolSize:5,
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                data:markPointData,
            },
        ],
    }
    return option
}
export const getCalMapServies = (data,datas, partition, {point_data, line_data,line_data_n1,point_data_n1 },BoundaryCondition, isShowFeedin,flag1, mapType, flag3, equipmentIds) => {
    const fontSize = 9
    const { data1, data2, data3 } = getFeedinLine(BoundaryCondition, partition, 0)
    let lineData = []
    let lineDatas = []
    let mapData = []
    let markPointData = []
    if(mapType==1){
        mapData = data
    } else {
        mapData = datas
    }
    if(flag1){
        line_data.concat(line_data_n1?line_data_n1:[]).forEach(item=>{
            let arr = Object.keys(item.info)
            if(arr.length==1){
                let obj = Object.assign({...item},{
                    id:item.info[arr[0]],
                    name:arr[0],
                    coord:item.coords,
                    value:item.pRateDetail[0],
                    values:item.pRateDetail[0]*item.limitDetail[0],
                    label:{
                        distance:echartsResize(-fontSize/2),
                    }
                })
                lineData.push(obj)
            }else if(arr.length==2||arr.length==3||arr.length==4){
                let coords
                if(arr.length==2){
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.003/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.003/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                }else{
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.0065/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.0065/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:arr.length==3?item.info[arr[2]]:item.info[arr[3]],
                        name:arr.length==3?arr[2]:arr[3],
                        coord:item.coords,
                        coords:coords[1],
                        value:arr.length==3?item.pRateDetail[2]:item.pRateDetail[3],
                        values:arr.length==3?item.pRateDetail[2]*item.limitDetail[2]:item.pRateDetail[3]*item.limitDetail[3],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                    if(arr.length==3){
                        let obj = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                            value:item.pRateDetail[1],
                            values:item.pRateDetail[1]*item.limitDetail[1],
                            label:{
                                distance:echartsResize(-fontSize/2),
                            }
                        })
                        lineData.push(obj)
                    }else if(arr.length==4){
                    let coords
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.002/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.002/partitionScale[partition].scale)
                    let line3 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line4 = Object.assign({...item},{
                        id:item.info[arr[2]],
                        name:arr[2],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[2],
                        values:item.pRateDetail[2]*item.limitDetail[2],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line3,line4])
                    }
                }
            }
        })
        lineDatas = lineData.filter(item=>equipmentIds.includes(item.id))
        lineData = lineData.filter(item => !equipmentIds.includes(item.id))
        // lineData.forEach(item=>{
        //   if(phase_shifter_arg.line_index&&phase_shifter_arg.line_index.includes(item.id)){
        //     let value
        //     let rotate = getRotate(item.coords[0],item.coords[1])
        //     if(item.fromName==phase_shifter_arg.point_name){
        //       value = pointOnLine(item.coords[0],item.coords[1],0.1)
        //     }else{
        //       value = pointOnLine(item.coords[1],item.coords[0],0.1)
        //     }
        //   phaserData.push(Object.assign({...item},{...phase_shifter_arg},{value, symbol:"image://"+new URL('@/assets/svg/移相器.svg', import.meta.url).href,type:'phaser', symbolRotate:rotate}))
        //   }
        //   if(add_reactance_arg.line_index&&add_reactance_arg.line_index.includes(item.id)){
        //     let value
        //     let rotate = getRotate(item.coords[0],item.coords[1])
        //     value = centerOnLine(item.coords[0],item.coords[1])
        //   phaserData.push(Object.assign({...item},{...add_reactance_arg},{value,symbol:"image://"+new URL('@/assets/svg/串抗.svg', import.meta.url).href,type:'reactance',symbolRotate:rotate}))
        //   }
        // })
    }
    if(!flag3){
        markPointData = lineData.map(item=>{
            return{
                value: pointOnLine(item.coords[0],item.coords[1],0.5).concat(item.value),
                symbolRotate:getRotate(item.coords[0],item.coords[1])-90,
            }
        }).concat(lineDatas.map(item=>{
            return{
                value: pointOnLine(item.coords[0],item.coords[1],0.5).concat(item.value),
                symbolRotate:getRotate(item.coords[0],item.coords[1])-90,
                symbol:  "image://"+new URL('@/assets/icon/cha.png', import.meta.url).href,
                symbolSize: echartsResize(15) 
            }
        }))
        if(partition=='全省'&&isShowFeedin&&flag1){
            markPointData=markPointData.concat(data3)
        }
    }
    let option={
        geo: {
            map: partition,
            zoom: 1.22,
            silent: true,
            animation:false,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 8,
                shadowOffsetY: 4,
            },
            scaleLimit:{
                min:1.22,
            },
            emphasis: {
                disabled:true,
            },
            show:true,
            roam:true,
        },
        animation:false,
        tooltip:{
            formatter: (params)=>{
                if(params.componentSubType=='lines'){
                    if([6].includes(params.componentIndex)) return
                    if(params.data.lineName){
                        return params.data.lineName+' : '+params.value+' 万千瓦'
                    }else{
                        return params.data.name+':'+fixInteger(Math.abs(params.value)*100)+'%' 
                    }
                } else if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
                    if(params.data.powerflow_detail.length!=0){
                        let str = Object.keys(params.data.info).reduce((a, b,index) => {
                            return a+ `<span style="font-size: 14px;color: #000000;">&nbsp;${b +':'+fixInteger(100*Math.abs(params.data.powerflow_detail[index]))+' %'}</span><br/>`
                        },'')
                        return `
                            <span style="font-size: 16px;color: #000000;">${params.data.name +':'+fixInteger(100*Math.abs(params.value[2]))+' %'}</span><br/>
                        `+ str
                    }
                    // return params.name+':'+fixInteger(100*params.value[2])+'%'
                } else if (params.componentSubType == 'map') {
                if(params.data.values==undefined) return
                    if (mapType == 1) {
                        let v = (params.data.values).toFixed(2)
                        let c = params.data.value
                        let color = c ==2 ? '#4ec48c' : c==1 ? '#fbb03b':'#bf6c66'
                        let name = params.data.name
                        return `
                            <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                            <span style="font-size: 12px;color: #000000;letter-spacing: 0;">供电裕度</span>
                            <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}万千瓦</span>
                        `
                    } else {
                        let v = fixInteger(100*params.data.value)
                        let name = params.data.name
                        let color = getGradientColor(rgbToHex(233,193,67),rgbToHex(7,192,96),((v-95)/5)>1?1:((v-95)/5))
                        return `
                            <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                            <span style="font-size: 12px;color: #000000;letter-spacing: 0;">新能源消纳率</span>
                            <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}%</span>
                        `
                    }
                }
            },
        },
        visualMap: [
            {
                show: flag1,
                seriesIndex: [1,8],
                realtime: false,
                type:'piecewise',
                left:echartsResize(10),
                bottom:echartsResize(7),
                itemGap:echartsResize(10),
                itemHeight:echartsResize(14),
                itemWidth:echartsResize(20), 
                textGap:echartsResize(10),
                textStyle:{
                    color:'#fff',
                    fontSize:echartsResize(12),
                },
                pieces: true?[
                    {gt: 1,label: '100%以上', color: '#3D0713'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#AE0000'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#f24a64'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#ff8934'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#FFE175'}                 // (-Infinity, 5)
                    ]:[
                    {gt: 1,label: '100%以上', color: '#ff6464'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#ffaa64'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#fff5a5'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#55e9bc'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#11cbd7'}                 // (-Infinity, 5)
                ]
            },
            {
                show: mapType==1?true:false,
                seriesIndex: mapType==1?[0]:[],
                type:"piecewise",
                itemGap:echartsResize(10),
                itemWidth:flag1?echartsResize(20):echartsResize(30), 
                itemHeight:echartsResize(14),
                textGap:echartsResize(10),
                pieces: flag1?[
                    // {lt: 0, label: '不足', color: '#511e78'},  // [123, 123]
                    // {gt: 0, lte: 20,label: '紧张', color: '#8b2f97'},  // [123, 123]
                    // {gt: 20, label: '充裕', color: '#cf56a1'},  // [123, 123]
                    {value: 0, label: '不足（供电裕度<0）', color: '#253b6e'},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#1f5f8b'},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#1891ac'},  // [123, 123]
                ]:[
                    {value: 0, label: '不足（供电裕度<0）', color: '#bf6c66',symbol:"image://"+new URL(`@/assets/icon/mapIcon1.png`, import.meta.url).href},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#fbb03b',symbol:"image://"+new URL(`@/assets/icon/mapIcon2.png`, import.meta.url).href},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#4ec48c',symbol:"image://"+new URL(`@/assets/icon/mapIcon3.png`, import.meta.url).href},  // [123, 123]
                ],
                inRange: {  //对应范围内颜色
                    color: ['#bf6c66', '#fbb03b', '#4ec48c']
                },
                outOfRange:{
                    color:['rgb(44,82,118)']
                },
                left:echartsResize(105),
                bottom:echartsResize(56),
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            },
            {
                show: mapType==2?true:false,
                seriesIndex: mapType==2?[0]:[],
                min:0.95,
                max:1,
                realtime: false,
                calculable: true,
                color:['rgb(7,192,96)','rgb(233,193,67)'],
                outOfRange:{
                    color:['rgb(0,27,72)']
                },
                // color:['rgb(0,168,252)','rgb(0,35,212)'],
                left:echartsResize(105),
                bottom:echartsResize(25),
                itemHeight:echartsResize(80),
                itemWidth:echartsResize(20),
                formatter: function (value) {
                    return value*100+'%'; // 范围标签显示内容。
                },
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            }
        ],
        series: [
            // 地图 0
            {
                type: 'map',
                zlevel: 1,
                map:partition,
                zoom: 1.22,
                roam:true,
                scaleLimit:{
                    min:1.22,
                },
                label: {
                    show: !flag1?true:false,
                    fontSize:partition=='全省'?echartsResize(10):echartsResize(20),
                    color:"#fff"
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc', 
                    areaColor: 'rgb(44,82,118)',
                    shadowColor: 'rgba(40,139,252,0)',
                    shadowOffsetX: 8,
                    shadowOffsetY: 4,
                },
                data: mapData,
                emphasis: {
                    disabled:true,
                },
            },
            // 线路 1
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                effect: {
                    show: flag3,
                    period: 5, 
                    trailLength: 0,
                    symbol: 'arrow',
                    symbolSize: 5 
                },
                labelLayout:{
                // hideOverlap:true,
                // moveOverlap:true,
                },
                label:{
                    show:true,
                    color: '#fff',
                    fontSize:echartsResize(fontSize),
                    position:'middle',
                    formatter: params => {
                        if(params.data.values){
                            return fixInteger(params.data.values,0)+''
                        }else{
                            return params.data.values+''
                        }
                    },
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                },
                data: flag1 && partition == '全省' ? lineData.filter(item => item.vn_kv > 500).map(items => {
                return Object.assign({
                    ...items
                    },{
                        lineStyle:{
                        // width:items.value>0.8?2.5:2,
                        width:2,
                        // curveness:items.coords[0][0]>items.coords[1][0]?-0.25:0.25
                        }
                    })
                })
                : lineData,
            },
            // 2 变电站
            {
                animation:false,
                name: '起始涟漪城市',
                coordinateSystem: 'geo',
                zlevel: 3,
                type: 'scatter',
                symbolSize: (value, params) => {
                    return echartsResize(getGisStyle(params.data).width * (['全省'].includes(partition)?4:4))* option.geo.zoom
                },
                itemStyle: {
                    opacity: 1,
                    color: '#fff',
                    borderWidth: 1
                },
                type: 'scatter',
                symbol: (value, params) => {
                    return 'path://' + getSymbolPath(params.data)
                },
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:echartsResize(10),
                    position: 'top', 
                    color: '#fff',
                },
                data:flag1?point_data.map(item=>{
                    return {
                        ...item,
                        itemStyle: {
                            borderColor: getGisStyle({ vn_kv: item.vn_kv }).color// item.color || getGisStyle(item).color
                        }
                    }
                }):[],
            },
            // 3 n-1主变 
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'effectScatter',
                showEffectOn:'emphasis',
                coordinateSystem: 'geo',
                zlevel: 3,
                symbolSize: (val,params) =>{
                    return echartsResize(getGisStyle(params.data).width * (['全省'].includes(partition)?4:4))* option.geo.zoom
                },
                symbol:"image://"+new URL('@/assets/icon/station.png', import.meta.url).href,
                rippleEffect: {
                    period: 4, 
                    brushType: 'stroke'
                },
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize: echartsResize(15),
                    position: 'top',
                    color: '#fff',
                },
                data:flag1&&point_data_n1?point_data_n1:[]
            },
            // 移相器 scatter 4
            {
                animation:false,
                zlevel: 4,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbolSize: (val) =>{
                return [15 * option.geo.zoom,8 * option.geo.zoom];
                },
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                hideOverlap: true,
                },
                emphasis: {
                disabled:true,
                },
                label: {
                    show: true,
                    fontSize: echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    formatter: params => {
                    return params.data.type=='phaser'? params.data.shift_degree+'°' : params.data.added_reactance_ohm+'Ω';
                    },
                },
                data:[],
            },
            // n-1线路 5
            {
                type: 'lines', 
                animation:false,
                zlevel: 1,
                effect: {
                    show: flag3,
                    period: 5, 
                    trailLength: 0, 
                    symbol:  "image://"+new URL('@/assets/icon/cha.png', import.meta.url).href,
                    symbolSize: echartsResize(15) 
                },
                label:{
                    show:false,
                    position: 'middle',
                    color: '#fff',
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    curveness: 0,
                    type: [5, 10],
                    dashOffset: 10
                },
                data:flag1?lineDatas:[]
            },
            // 区外直流 6 lines
            {
                type: 'lines',
                animation:false,
                zlevel: 1,
                polyline:true,
                effect: {
                    show: flag3,
                    period: 5, 
                    // constantSpeed :50,
                    trailLength: 0, 
                    symbol: 'arrow',
                    symbolSize: 10
                },
                lineStyle: {
                    width: 5, 
                    color: '#8BF1FE',
                    opacity: 1, 
                },
                data:isShowFeedin&&flag1?data1:[],
            },
            // 区外直流名称 scatter 7
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                // symbol:"none",
                symbolSize:0,
                label: {
                    show: true,
                    formatter: params => {
                        // return params.name
                        if (params.value[2] == 0) {
                            return params.name+'  '+'{fline|'+0+'}'+'    '
                        }else if(String(params.value[2]).length<2){
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'+'    '
                        }else if(String(params.value[2]).length<3){
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'+'  '
                        }else{
                            return params.name+'  '+'{fline|'+Math.abs(fixInteger(params.value[2],0))+'}'
                        }
                    },
                    fontSize: echartsResize(16),
                    lineHeight:echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    rich:{
                        fline: {
                            color: '#1890ff',
                            fontWeight:'bold',
                            fontSize: echartsResize(16),
                        },
                    },
                },
                data:isShowFeedin&&flag1?data2:[],
            }, 
            // 静态箭头 scatter 8
            {
                animation:false,
                zlevel: 1,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbol:'arrow',
                symbolSize:5,
                itemStyle:{
                    opacity:1
                },
                labelLayout: {
                    hideOverlap: true,
                },
                emphasis: {
                    disabled:true,
                },
                data:markPointData,
            },
        ],
    }
    return option
}
export const getCalRadarOption = (data,color,partition)=>{
    const radarObj=[
        '供电裕度','正备用容量','负备用容量','无惯量电源渗透率','外来电依存度','爬坡能力'
    ]
    const radarObjCopy=[
        '供电裕度','正备用容量','负备用容量','无惯量电源渗透率','本地电源自给率','爬坡能力'
    ]
    const colorLists = ['#66D9E7','#F3C74C','lawngreen']
    let option={
        legend:Object.assign({...legend},
            {
                top:echartsResize(0),
                left: echartsResize(10),
                orient: 'vertical',
                data: data.length == 1 ? [{
                    name:'基准场景',
                    itemStyle:{
                        color:'#66D9E7'
                    },
                }]
                :[
                    {
                        name:'基准场景',
                        itemStyle:{
                        color:'#66D9E7'
                        },
                    },
                    {
                        name:'对比场景',
                        itemStyle:{
                        color:'#F3C74C'
                        },
                    },
                ],
            }
        ),
        radar: [
            {
                indicator: [
                    { name: '供电裕度', max:1},
                    { name: '正备用容量', max:1},
                    { name: '负备用容量', max:1},
                    { name: '无惯量电源渗透率', max:1},
                    { name: '外来电依存度', max:1},
                    { name: '爬坡能力', max:1}
                ],
                radius: "73%",
                splitNumber:1,
                scale:true,
                // nameGap:echartsResize(22),
                axisName: {
                    show:false,
                    // formatter: '{value}',
                    // color: '#FFF',
                    // fontSize:echartsResize(14)
                },
                splitArea: {
                    areaStyle: {
                        color: ['transparent'],
                    }
                },
                axisLine: {
                    show:false
                },
                splitLine: {
                    lineStyle: {
                        color: '#3e6b5f',
                        width:echartsResize(2),
                    }
                }
            }
        ],
        animationDuration:100,
        tooltip:{
        },
        series: [
            {
                type:'radar',
                data:[
                    {
                        values: [color.power_supply_margin[2], color.reserve_low[2], color.peak_shaving[2], color.non_inertia_penetration[2],partition=='全省'?color.feedin_dependence[2]:0.4, color.ramp_cap_upward[2]],
                        value: [color.power_supply_margin[5]/100, color.reserve_low[5]/100, color.peak_shaving[5]/100, color.non_inertia_penetration[5]/100, color.feedin_dependence[5]/100, color.ramp_cap_upward[5]/100],
                        symbol:"none",
                        lineStyle:{
                            color:"#c0952d",
                            width:echartsResize(2),
                        },
                        tooltip:{
                            formatter:(params)=>{
                            //   let str = params.name+'<br>'
                                let obj = partition=='全省'?radarObj:radarObjCopy
                                let str =''
                                params.data.values.forEach((item,index)=>{
                                str =str+ '  <span style="background:#c0952d;height:10px;width:10px;display:inline-block;border-radius:100%"></span>  '+obj[index] +' : '+ (index==3||index==4? (item*100).toFixed(0)+'%':item.toFixed(0)) +'<br>'
    
                                })
                                return str
                            }
                        },
                    },
                ],
                z:1,
            },
            {
                type:'radar',
                data:[
                    {
                        values: [color.power_supply_margin[1], color.reserve_low[1], color.peak_shaving[1], color.non_inertia_penetration[1],partition=='全省'?color.feedin_dependence[1]:0.2, color.ramp_cap_upward[1]],
                        value: [color.power_supply_margin[4]/100, color.reserve_low[4]/100, color.peak_shaving[4]/100, color.non_inertia_penetration[4]/100, color.feedin_dependence[4]/100, color.ramp_cap_upward[4]/100],
                        symbol:"none",
                        lineStyle:{
                            color:"#c1214f",
                            width:echartsResize(2),
                        },
                        tooltip:{
                            formatter:(params)=>{
                            //   let str = params.name+'<br>'
                                let str =''
                                let obj = partition=='全省'?radarObj:radarObjCopy
                                params.data.values.forEach((item,index)=>{
                                str =str+ '  <span style="background:#c1214f;height:10px;width:10px;display:inline-block;border-radius:100%"></span>  '+ obj[index] +' : '+ (index==3||index==4? (item*100).toFixed(0)+'%':item.toFixed(0)) +'<br>'
                                })
                                return str
                            }
                        },
                    }
                ],
                z:2,
            }
        ]
    }
    data.forEach((item,index)=>{
        option.series.push(
            {
                type: 'radar',
                name:index==0?'基准场景':'对比场景',
                data: [
                    {
                        value: [item.canonicalValue.power_supply_margin, item.canonicalValue.reserve_low, item.canonicalValue.peak_shaving, item.canonicalValue.non_inertia_penetration, partition=='全省'? item.canonicalValue.feedin_dependence:(1-item.standardValue.feedin_dependence)/0.6, item.canonicalValue.ramp_cap_upward],
                        values: [item.standardValue.power_supply_margin, item.standardValue.reserve_low, item.standardValue.peak_shaving, item.standardValue.non_inertia_penetration, partition=='全省'? item.standardValue.feedin_dependence:(1-item.standardValue.feedin_dependence), item.standardValue.ramp_cap_upward],
                        symbolSize:echartsResize(5),
                        label: {
                            color:"rgba(255,255,255,0.8)",
                            show: false,
                            distance:1,
                            fontSize:echartsResize(14),
                            formatter:function(params) {
                                if(params.dimensionIndex==4||params.dimensionIndex==3){
                                    return fixInteger(100*params.data.values[params.dimensionIndex],1)+'%';
                                }else{
                                    return (params.data.values[params.dimensionIndex]).toFixed(0);
                                }
                            }
                        },
                        tooltip:{
                            formatter:(params)=>{
                            //   let str = params.name+'<br>'
                                let str =`${index==0?'基准场景':'对比场景'}<br>`
                                let obj = partition=='全省'?radarObj:radarObjCopy
                                params.data.values.forEach((item,index1)=>{
                                str =str+ ` <span style="background:${colorLists[index]};height:10px;width:10px;display:inline-block;border-radius:100%"></span>  `+ obj[index1] +' : '+ (index1==3||index1==4? (item*100).toFixed(0)+'%':item.toFixed(0)) +'<br>'
                                })
                                return str
                            }
                        },
                        itemStyle:{
                            color:colorLists[index],
                        },
                        areaStyle:{
                            color:colorLists[index],
                            opacity:index==0?0.8:0.5
                        },
                        lineStyle:{
                            width:echartsResize(1),
                            // color:"#fff"
                            color:colorLists[index]
                        },
                    },
                ],
                z:index+3,
            },
        )
    })
    return option
}
export const handleData = (data) => {
    return data.name.map((item,index)=>{
        return {
            name:item,
            power:+data.power[index],
            p_rate:+data.p_rate[index]*100,
            limit:+data.limit[index],
        }
    })
}
export const getPartitionBar = (data1,data2)=>{
    let data = data1.filter(item=>item)
    let option = {
        tooltip: Object.assign({...tooltip},{
            trigger: 'item',
            valueFormatter:(value)=>fixInteger(value,0)+'万千瓦'
        }),
        xAxis: [Object.assign({...xAxis},{
            gridIndex: 0,
            data:data.length==2?['默认场景','对比场景']:['默认场景'],
            nameTextStyle:{
                color:"rgba(255,255,255,1)",
                fontSize:echartsResize(12),
            },
        }),
        Object.assign({...xAxis},{
            gridIndex: 1,
            data:data.length==2?['默认场景','对比场景']:['默认场景'],
            nameTextStyle:{
                color:"rgba(255,255,255,1)",
                fontSize:echartsResize(12),
            },
        })],
        grid: [
            {
                // show: true,
                left: '1%',
                bottom:'5%',
                top:'10%',
                containLabel: true,
                width: '47%'
            },
            {
                // show: true,
                right: '1%',
                width: '47%',
                bottom:'5%',
                top:'10%',
                containLabel: true,
            },
        ],
        yAxis:[Object.assign({...yAxis},{
            gridIndex: 0,
            splitNumber:3,
        }),
        Object.assign({...yAxis},{
            gridIndex: 1,
            splitNumber:3,
        })],
        series:[
            {
                type: 'bar',
                name:'供电裕度',
                data:data1,
                barWidth:echartsResize(30),
                xAxisIndex: 0,
                yAxisIndex: 0,
                label:{
                    show:true,
                    position:'top',
                    color:'#fff',
                    fontSize:echartsResize(12),
                    formatter:(params)=>{
                        return fixInteger(params.data,0)
                    }
                },
                itemStyle: {
                    color: function(params) {
                        var colorList = ['#2BF3ED', '#1CB3F5', '#0F79FD'];
                        return colorList[params.dataIndex]
                    },
                }
            },
            {
                type:'bar',
                name:'主变受电能力',
                data:data2,
                barWidth:echartsResize(30),
                xAxisIndex: 1,
                yAxisIndex: 1,
                label:{
                    show:true,
                    position:'top',
                    color:'#fff',
                    fontSize:echartsResize(12),
                    formatter:(params)=>{
                        return fixInteger(params.data,0)
                    }
                },
                itemStyle: {
                    color: function(params) {
                        var colorList = ['#2BF3ED', '#1CB3F5', '#0F79FD'];
                        return colorList[params.dataIndex]
                    },
                }
            }
        ],
    }
    return option
}