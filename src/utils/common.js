import axios from 'axios';
import * as echarts from 'echarts'
import { read, utils,write,writeFile } from "xlsx";
import dayjs from 'dayjs';
import { echartsResize } from '../config/setting.config';
import { saveAs } from 'file-saver'
import { message } from 'ant-design-vue'
export const downloadApiFile = ({ data, headers }) => {
	if (!headers['content-disposition']) {
		// 使用 FileReader 来读取 Blob 数据
		const reader = new FileReader()
		// 定义当读取完成时的回调函数
		reader.onload = function(event) {
			const jsonString = event.target.result // 获取读取的字符串数据
			const jsonData = JSON.parse(jsonString) // 将字符串解析为 JSON 对象
			message.error(jsonData.message)
		}
		reader.readAsText(data)
		return
	}
	// const fileName = decodeURIComponentInit(headers['content-disposition'].split(';')[1].split('=')[1])
	saveAs(data, 'interfaceData_'+dayjs(new Date()).format('YYYYMMDD_HHmmss')+'.xlsx')
}

export function getPrevDays(n = 3) {
    const arr = Array.from({length: n}, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - n + i);
        return date.toISOString().slice(0, 10);
    });
    return  [arr[0],arr[arr.length-1]]
}

export function sortByKey(arr, order,key) {
    return arr.sort((a, b) => {
        const indexA = order.indexOf(a[key]);
        const indexB = order.indexOf(b[key]);
        return indexA - indexB;
    })
}
/**
 * 生成不重复的随机十六进制颜色数组
 * @param {number} n - 需要生成的颜色数量
 * @returns {string[]} 包含n个不重复十六进制颜色代码的数组
 */
export function generateUniqueHexColors(n) {
  const colors = new Set();
  
  while (colors.size < n) {
    // 生成随机颜色（简写3位或完整6位格式）
    const color = '#' + Math.floor(Math.random() * 0xFFFFFF)
      .toString(16)
      .padStart(6, '0')
      .toUpperCase();
    
    // 确保颜色有效且不重复
    if (/^#[0-9A-F]{6}$/i.test(color)) {
      colors.add(color);
    }
  }

  return Array.from(colors);
}
function darkenColor(color, factor = 0.6) {
    // 参数验证
    if (factor < 0 || factor > 1) {
        throw new Error('Factor must be between 0 and 1');
    }
    // HEX格式处理
    if (color.startsWith('#')) {
        let hex = color.trim().replace('#', '');
        if (!/^([0-9A-F]{3,4}|[0-9A-F]{6}|[0-9A-F]{8})$/i.test(hex)) {
            throw new Error('Invalid HEX color format');
        }
        // 处理缩写格式
        if (hex.length === 3 || hex.length === 4) {
            hex = hex.replace(/./g, '$&$&');
        }
        // 解析颜色分量
        const components = hex.match(/.{2}/g);
        const [r, g, b] = components.map(c => parseInt(c, 16));
        const a = components[3] || null;
        // 应用暗化
        const darken = c => Math.max(0, Math.min(255, Math.floor(c * factor)));
        const darkened = [darken(r), darken(g), darken(b)];
        // 重组为HEX
        const toHex = c => c.toString(16).padStart(2, '0');
        let result = `#${darkened.map(toHex).join('')}`;
        if (a) result += toHex(parseInt(a, 16));
        return result;
    }
    // RGB/RGBA格式处理
    if (color.startsWith('rgb')) {
        const match = color.match(/rgba?\((\s*\d+\s*,\s*){2}\d+\s*(,\s*\d*\.?\d+\s*)?\)/i);
        if (!match) throw new Error('Invalid RGB/RGBA format');
        const components = color.match(/\d+(\.\d+)?/g).map(Number);
        const [r, g, b] = components;
        const a = components[3] || null;
        // 应用暗化
        const darken = c => Math.max(0, Math.min(255, Math.floor(c * factor)));
        const darkened = [darken(r), darken(g), darken(b)];
        // 重组为RGB/RGBA
        return a === null 
            ? `rgb(${darkened.join(', ')})`
            : `rgba(${darkened.join(', ')}, ${a})`;
    }
    throw new Error('Unsupported color format');
}
function modifyNestedValues(obj, firstLevelKeys, nestedKeys, compareValue, newValue) {
  // 遍历第一层key
  for (const key in obj) {
    if (firstLevelKeys.includes(key)) {
      // 处理第一层key下的对象
      const nestedObj = obj[key];
      if (typeof nestedObj === 'object' && nestedObj !== null) {
        // 递归处理嵌套对象
        processNestedObject(nestedObj, nestedKeys, compareValue, newValue);
      }
    }
  }
  return obj;
}
function processNestedObject(obj, keysToCheck, compareValue, newValue) {
  for (const key in obj) {
    if (keysToCheck.includes(key)) {
      // 检查当前key的值是否需要修改
      if (obj[key] !== compareValue) {
        obj[key] = newValue;
      }
    }
    // 递归处理子对象
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      processNestedObject(obj[key], keysToCheck, compareValue, newValue);
    }
  }
}
export function generateAndDownloadEChart(chart) {
    const option = chart.getOption()
    const tempDiv = document.createElement('div');
    tempDiv.style.width = chart.getWidth() + 'px';
    tempDiv.style.height = chart.getHeight() + 'px';
    tempDiv.style.position = 'absolute';
    tempDiv.style.opacity = 0
    tempDiv.style.zIndex = -1
    document.body.appendChild(tempDiv);
    const charts = echarts.init(tempDiv);
    const optionNew = modifyNestedValues(option, ['xAxis', 'yAxis','legend'], ['color'], 'inherit','#000')
    if(optionNew.legend&&optionNew.legend[0]){
        optionNew.legend[0].bottom = 0
        optionNew.legend[0].top = undefined
        if(!optionNew.legend[0].data){
            if(optionNew.series.find(item=>item.type=='pie')){
                optionNew.legend[0].data = optionNew.series[0].data.map(item=>item.name)
            }else{
                optionNew.legend[0].data = optionNew.series.map(item=>item.name)
            }
        }
        optionNew.legend[0].data=optionNew.legend[0].data.filter(item=>optionNew.legend[0].selected[item]!==false)
    }
    optionNew.animation = false
    if(optionNew.dataZoom&&optionNew.dataZoom.length>0){
        optionNew.dataZoom[0].show = false
    }
    if(optionNew.grid&&optionNew.grid[0]){
        optionNew.grid[0].y = optionNew.grid[0].y2
        optionNew.grid[0].y2 = echartsResize(50)
        optionNew.grid[0].top = optionNew.grid[0].y
        optionNew.grid[0].bottom = echartsResize(50)
    }
    optionNew.series.forEach((item => {
        if (item.type == 'pie'||item.type == 'bar') {
            item.label.color = '#000'
        }
    }))
    optionNew.toolbox = {}
    charts.setOption(optionNew);
    setTimeout(() => {
        const imgUrl = charts.getDataURL({
            type: 'png',
            pixelRatio: 5,
            backgroundColor: 'transparent'
        });
        const link = document.createElement('a');
        link.href = imgUrl;
        link.download = '图片-' + new Date().getTime() + '.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        charts.dispose();
        document.body.removeChild(tempDiv);
    });
}
export const power_balance_line_chart_name = {
    '全网负荷':'load',
    '区外来电':'feedin',
    '储能':'stogen_energy_storage',
    '抽蓄':'stogen_pump_hydro',
    '地方电厂':'gen_self',
    '水电':'gen_hydro',
    '燃气':'gen_gas',
    '风电':'wind',
    '光伏':'solar',
    '热电':'gen_heat',
    '燃煤':'gen_coal',
    '新能源':'new_energy',
    '受入总加':'value',
}
function autoWidthFun (ws, data) {
    const colWidth = data.map(row =>
        row.map(val => {
        if (val == null) {
            return { wch: 10 };
        } else if (val.toString().charCodeAt(0) > 255) {
            // if chinese
            return { wch: val.toString().length * 2+2 };
        } else {
            return { wch: val.toString().length+2 };
        }
        })
    );
    const result = colWidth[0];
    for (let i = 1; i < colWidth.length; i++) {
        for (let j = 0; j < colWidth[i].length; j++) {
            if (result[j].wch < colWidth[i][j].wch) {
                result[j].wch = colWidth[i][j].wch;
            }
        }
    }
    ws['!cols'] = result;
}
export const downloadTable = (charts_data,legendData)=>{
    exportExcelTable(charts_data,legendData,''+dayjs(new Date()).format("YYYYMMDD_HHmmss")+'.xlsx')
}
export const downloadTableSimple = (data,time)=>{
    exportExcelTableSimple(time,data,''+dayjs(new Date()).format("YYYYMMDD_HHmmss")+'.xlsx')
}
export const exportExcels = (data, fileName) => {
	const wb = utils.book_new()
	data.forEach(item => {
		const tableData = item.data
		tableData.unshift(item.title)
		const ws = utils.aoa_to_sheet(tableData)
		autoWidthFun(ws, tableData)
		utils.book_append_sheet(wb, ws, item.sheetName)
	})
	writeFile(wb, fileName)
}
export const exportExcelTable = (charts_data,legendData,fileName)=>{
    const timeData = charts_data['time_range']
    const tableData = timeData.map((item,index)=>{
        return [item].concat(legendData.map(item=>charts_data[power_balance_line_chart_name[item]][index]))
    })
    const title = ['时间'].concat(legendData)

    let wb = utils.book_new()
    tableData.unshift(title)
    const ws = utils.aoa_to_sheet(tableData)
    autoWidthFun(ws,tableData)
    utils.book_append_sheet(wb, ws, 'Sheet1')
    writeFile(wb,fileName)
}
export const exportExcelTableSimple = (timeData,data,fileName)=>{
    const tableData = timeData.map((item,index)=>{
        return [item].concat(data.map(items=>items.data[index]))
    })
    const title = ['时间'].concat(data.map(item=>item.name))
    let wb = utils.book_new()
    tableData.unshift(title)
    const ws = utils.aoa_to_sheet(tableData)
    autoWidthFun(ws,tableData)
    utils.book_append_sheet(wb, ws, 'Sheet1')
    writeFile(wb,fileName)
}
export const getTangentialPoint=(startX, startY, r1, endX, endY, r2)=>{
    const dx = endX - startX;
    const dy = endY - startY;
    const d = Math.sqrt(dx * dx + dy * dy);
    const theta = Math.atan2(dy, dx); 
    const alpha = Math.acos((r1 - r2) / d);
    const dir1 = {
        x: Math.cos(theta + alpha),
        y: Math.sin(theta + alpha)
    };
    const dir2 = {
        x: Math.cos(theta - alpha),
        y: Math.sin(theta - alpha)
    };
    const p1 = [ startX + r1 * dir1.x,startY + r1 * dir1.y ]
    const p2 = [ endX + r2 * dir1.x,endY + r2 * dir1.y ]
    const p3 = [ startX + r1 * dir2.x,startY + r1 * dir2.y ]
    const p4 = [ endX + r2 * dir2.x,endY + r2 * dir2.y ]
    return [[p1,p2],[p3,p4]]
} 
export const registerMap = async(partition)=>{
    await axios.get('./json/'+partition+'.geojson').then(res=>{
        echarts.registerMap(partition, res.data)
    })
}
export const registerMapCopy = async(partition)=>{
    await axios.get('./jsons/'+partition+'.geojson').then(res=>{
        echarts.registerMap(partition, res.data)
    })
}
export const registerMapData = async(partition)=>{
  return (await axios.get('./jsons/' + partition + '.geojson')).data
}
export const getMapScaleByBbox = (
	geoData, width = 500, height = 500, aspectScale = 0.75
) => {
	let bbox
	if (geoData.features.every(item => item.bbox)) {
		geoData.features.forEach((item, index) => {
			if (index == 0) {
				bbox = item.bbox
			} else {
				if (bbox[0] > item.bbox[0]) {
					bbox[0] = item.bbox[0]
				}
				if (bbox[1] > item.bbox[1]) {
					bbox[1] = item.bbox[1]
				}
				if (bbox[2] < item.bbox[2]) {
					bbox[2] = item.bbox[2]
				}
				if (bbox[3] < item.bbox[3]) {
					bbox[3] = item.bbox[3]
				}
			}
		})
	} else {
		let N = -90; let S = 90; let W = 180; let E = -180
		geoData.features.forEach(item => {
			item.geometry.coordinates.forEach(area => {
				(item.geometry.type === 'Polygon' ? area : area[0]).forEach(elem => {
					if (elem[0] < W) {
						W = elem[0]
					}
					if (elem[0] > E) {
						E = elem[0]
					}
					if (elem[1] > N) {
						N = elem[1]
					}
					if (elem[1] < S) {
						S = elem[1]
					}
				})
			})
		})
		bbox = [W, S, E, N]
	}
	return {
		scale: Math.min(width / (bbox[2] - bbox[0]), height / (bbox[3] - bbox[1]) * aspectScale),
		center: [
			(bbox[2] + bbox[0]) / 2,
			(bbox[3] + bbox[1]) / 2
		]
	}
}
export function calculateCentroid(coords) {
    var n = coords.length;
    var area = 0;
    var centroidX = 0;
    var centroidY = 0;
    for (var i = 0; i < n; i++) {
        var x0 = coords[i][0];
        var y0 = coords[i][1];
        var x1 = coords[(i + 1) % n][0];
        var y1 = coords[(i + 1) % n][1];
        var partialArea = (x0 * y1 - x1 * y0);
        area += partialArea;
        centroidX += (x0 + x1) * partialArea;
        centroidY += (y0 + y1) * partialArea;
    }
    area *= 0.5;
    centroidX /= (6 * area);
    centroidY /= (6 * area);
    return [centroidX, centroidY];
}
export const getMapCenter = (
    geoData,width=993,height=1096,aspectScale=0.75
  ) => {
    let x,y,s
    let N = -90, S = 90, W = 180, E = -180
    geoData.features.forEach(item => {  
      // 将MultiPolygon和Polygon格式的地图处理成统一数据格式
      if (item.geometry.type === 'Polygon') {
          item.geometry.coordinates = [item.geometry.coordinates]
      }
      // 取四个方向的极值
      item.geometry.coordinates.forEach(area => {
          area[0].forEach(elem => {
              if (elem[0] < W) {
                  W = elem[0]
              }
              if (elem[0] > E) {
                  E = elem[0]
              }
              if (elem[1] > N) {
                  N = elem[1]
              }
              if (elem[1] < S) {
                  S = elem[1]
              }
          })
      })
    })
    x = (E + W) / 2
    y = (N + S) / 2
    s = (width / Math.abs(E - W))> (height / Math.abs(N - S))*aspectScale? (height / Math.abs(N - S)*aspectScale):(width / Math.abs(E - W))
    return{
      x,
      y,
      s
    }
  }
export const deepClone = (obj)=>{
    let newObj = Array.isArray(obj) ? [] : {};
    for (let key in obj) {  
        let val = obj[key];  
        newObj[key] = typeof val === 'object' ? deepClone(val): val;  
    }  
    return newObj;  
}
export const pointOnLine=(start, end, percent)=> {
    const dx = end[0] - start[0];
    const dy = end[1] - start[1];
    const distance = Math.sqrt(dx * dx + dy * dy);
    const targetDistance = distance * percent;
    const targetX = start[0] + (dx / distance) * targetDistance;
    const targetY = start[1] + (dy / distance) * targetDistance;
    return [targetX,targetY]
}
export const centerOnLine=(start, end)=> {
    var targetPoint = [(start[0] + end[0])/2 , (start[1] + end[1])/2];
    return targetPoint;
}
export const getRotate=(start, end,aspectScale=0.75)=> {
    const angle = Math.atan2((end[1] - start[1])/aspectScale, (end[0] - start[0]))* (180 / Math.PI)
    return angle;
}
export const getPointOnCubicBezier = (curveness, startX, startY, endX, endY, percentage,aspectScale=0.75)=> {
    const controlX = (startX + endX) / 2 - (endY/aspectScale - startY/aspectScale) * curveness;
    const controlY =  (startY/aspectScale + endY/aspectScale) / 2 + (endX - startX) * curveness; 
    const t = percentage/100;
    const x = Math.pow(1 - t, 2) * startX + 2 * t * (1 - t) * controlX + t * t * endX
    const y =( Math.pow(1 - t, 2) * startY/aspectScale + 2 * t * (1 - t) * controlY + t * t * endY/aspectScale)*aspectScale
    const dx = 2 * (1 - t) * (controlX - startX) + 2 * t * (endX - controlX);
    const dy =( 2 * (1 - t) * (controlY - startY/aspectScale) + 2 * t * (endY/aspectScale - controlY))
    const angle = Math.atan2(dy, dx)* (180 / Math.PI)
    return { x, y, angle };
}
export const fixInteger = (data,unit=2) => {
    return data? Number.isInteger(data)?data:data.toFixed(unit) :0
}
export function getUniqueMonths([startDate, endDate]) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const uniqueMonths = new Set(); // 使用 Set 确保唯一性

    // 使用循环遍历每个月
    for (let date = new Date(start); date <= end; date.setMonth(date.getMonth() + 1)) {
        uniqueMonths.add(date.getMonth() + 1); // 将月份（1-12）添加到 Set 中
    }

    // 将 Set 转换为数组并排序
    return Array.from(uniqueMonths).sort((a, b) => a - b);
}