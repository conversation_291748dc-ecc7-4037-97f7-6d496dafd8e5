import {setting} from "../config/setting.config"
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom,baseColor}  = setting
import * as echarts from 'echarts'
import { generateAndDownloadEChart } from '@/utils/common'
import dayjs from 'dayjs';
export const getBarSeries = (chart,{days,hours}) => {
    let option={
        grid: {
            x: echartsResize(65),
            y: echartsResize(45),
            x2: echartsResize(55),
            y2: echartsResize(35),
        },
        tooltip: Object.assign({...tooltip},{
            // valueFormatter:(value)=>value.toFixed(2)+(type==1?' 万千瓦时':' 万千瓦'),
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
                icon: 'rect',
            }
        ),
        color:['#B0C4DE','#98FB98'],
        // color:[new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        //     offset: 0,
        //     color: 'rgb(85, 233, 188)'
        // }, {
        //     offset: 1,
        //     color: 'rgba(85, 233, 188, 0)'
        // }]),
        // new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        //     offset: 0,
        //     color: 'rgb(249, 161, 23)'
        // }, {
        //     offset: 1,
        //     color: 'rgba(249, 161, 23, 0)'
        // }])],
        xAxis: Object.assign({ ...xAxis }, {
            data:['持续时间'],
        }),
        yAxis:Object.assign({...yAxis},{
            name:'时间',
        }),
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //
                //     },
                // },
                mySave: {
                    show: true,
                    title: '保存为图片',
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    onclick: () => {
                        generateAndDownloadEChart(chart)
                    },
                }
                // saveAsImage : {
                //     show: true,
                //     type: 'png',
                //     pixelRatio: 5,
                //     backgroundColor:'transparent',
                //     title: "保存为图片",
                //     icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                //     name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                // },
            }
        },
        series: [
            {
                type: 'bar',
                name:'持续天数',
                data:[days],
                barWidth:echartsResize(50),
                label:{
                    show:true,
                    position: 'insideBottom',
                    color:baseColor,
                    fontSize:echartsResize(14),
                },
                itemStyle:{
                }
            },
            {
                type: 'bar',
                name:'持续小时',
                data:[hours],
                barWidth:echartsResize(50),
                label:{
                    show:true,
                    position: 'insideBottom',
                    color: baseColor,
                    fontSize:echartsResize(14),
                },
                itemStyle:{
                }
            },
        ],
    }
    return option
}