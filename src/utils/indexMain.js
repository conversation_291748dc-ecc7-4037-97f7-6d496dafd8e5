import {setting} from "../config/setting.config"
import {fixInteger, getTangentialPoint,generateAndDownloadEChart} from './common'
const {echartsResize,echartsLength,legend,tooltip,xAxis,yAxis,dataZoom,baseColor}  = setting
import { partitionLinesCoordinate,partitionScale} from "./constants"
import * as echarts from 'echarts'
import { getFeedinLine,getGradientColor,rgbToHex } from "./calculate"
import dayjs from 'dayjs';
export const getMapOption = ({partition,partitionData,mapData:{line_data,point_data},showArea=true,showLines=true,showFeedin=true,visualMap=[],geoScale=1,zoom=1})=>{
    let lineData = []
    let data1 = []
    let data2 = []
    if(showFeedin){
        data1 = getFeedinLine({}, partition, 0).data1
        data2 = getFeedinLine({}, partition, 0).data2
    }
    if(showLines){
        line_data.forEach(item=>{
            let arr = Object.keys(item.info)
            if(arr.length==1){
                let obj = Object.assign({...item},{
                    id:item.info[arr[0]],
                    name:arr[0],
                    coord:item.coords,
                })
                lineData.push(obj)
            }else if(arr.length==2||arr.length==3||arr.length==4){
                let coords
                if(arr.length==2){
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],5*geoScale,item.coords[1][0],item.coords[1][1],5*geoScale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                    })
                    let line2 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                    })
                    lineData = lineData.concat([line1,line2])
                }else{
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],8*geoScale,item.coords[1][0],item.coords[1][1],8*geoScale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                    })
                    let line2 = Object.assign({...item},{
                        id:arr.length==3?item.info[arr[2]]:item.info[arr[3]],
                        name:arr.length==3?arr[2]:arr[3],
                        coord:item.coords,
                        coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                    })
                    lineData = lineData.concat([line1,line2])
                    if(arr.length==3){
                        let obj = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                        })
                        lineData.push(obj)
                    }else if(arr.length==4){
                        let coords
                        coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],3*geoScale,item.coords[1][0],item.coords[1][1],3*geoScale)
                        let line3 = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                            coords:coords[0][0][1]>coords[1][0][1]?coords[0]:coords[1],
                        })
                        let line4 = Object.assign({...item},{
                            id:item.info[arr[2]],
                            name:arr[2],
                            coord:item.coords,
                            coords:coords[0][0][1]<coords[1][0][1]?coords[0]:coords[1],
                        })
                        lineData = lineData.concat([line3,line4])
                    }
                }
            }
        })
    }
    let option={
        geo: {
            map: partition,
            zoom: zoom,
            animation:false,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 0,
                shadowOffsetY: 10,
                shadowBlur:2
            },
            scaleLimit:{
                min:zoom,
            },
            emphasis: {
                disabled:true,
            },
            show:true,
            roam:true,
        },
        animation: false,
        tooltip:{
            formatter: (params) => {
                if (params.componentSubType == 'lines') {
                    return params.data.name+':'+(Math.abs(params.value)*100).toFixed(2)+'%' 
                } else if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
                    if(params.data.powerflow_detail.length!=0){
                        let str = Object.keys(params.data.info).reduce((a, b,index) => {
                            return a+ `<span style="font-size: 14px;color: #000000;">&nbsp;${b +':'+fixInteger(100*Math.abs(params.data.powerflow_detail[index]))+' %'}</span><br/>`
                        },'')
                        return `
                        <span style="font-size: 16px;color: #000000;">${params.data.name +':'+fixInteger(100*Math.abs(params.value[2]))+' %'}</span><br/>
                        `+ str
                    }
                } 
            },
        },
        visualMap,
        series: [
            // 0 地图
            {
                type: 'map',
                zlevel: 1,
                map:partition,
                zoom: zoom,
                roam:true,
                scaleLimit:{
                    min:zoom,
                },
                label: {
                    show:true,
                    fontSize:partition=='全省'?echartsResize(10):echartsResize(20),
                    color:"#fff"
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc',
                    areaColor: 'rgb(44,82,118)',
                },
                data: showArea?partitionData:[],
                emphasis: {
                    disabled:true,
                },
            },
            //1  线路
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                label:{
                    show:false,
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    curveness: 0
                },
                data: lineData,
            },
            //2 500kV
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
                coordinateSystem: 'geo',
                zlevel: 3,
                itemStyle: {
					opacity: 1,
					color: '#fff',
					borderWidth: 1
				},
                symbolSize: (value, params) => {
					return echartsResize(getGisStyle(params.data).width * (['全省'].includes(partition)?4:4))* option.geo.zoom
				},
                emphasis: {
                    disabled:true,
                },
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:echartsResize(14),
                    position: 'bottom',
                    color: '#fff',
                },
                data: showLines && point_data ?point_data.map(item=>{
                    return {
                        ...item,
                        itemStyle: {
                            borderColor: getGisStyle({ vn_kv: item.vn_kv }).color// item.color || getGisStyle(item).color
                        }
                    }
                })
                : []
            },
            // 区外直流 3
            {
                type: 'lines',
                animation:false,
                zlevel: 1,
                polyline:true,
                lineStyle: {
                    width: 3, 
                    color: '#8BF1FE',
                    opacity: 1, 
                },
                data:data1,
            },
            // 区外直流名称 4
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                symbolSize:0,
                label: {
                    show: true,
                    formatter: params => {
                        return params.name
                    },
                    fontSize: echartsResize(16),
                    lineHeight:echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    rich:{
                        fline: {
                            color: '#1890ff',
                            fontWeight:'bold',
                            fontSize: echartsResize(16),
                        },
                    },
                },
                data:data2,
            }
        ],
    }
    return option
}
export const getGisStyle = ({ vn_kv}) => {
	let width,color
	if (vn_kv >= 1000) {
		color = '#000000'
		width = 5
	} else if (vn_kv >= 500) {
		color = '#FF0000'
		width = 3
	} else if (vn_kv >= 200) {
		color = color || '#000000'
		width = 2
	} else {
		width = 1
		if (vn_kv >= 110) {
			color = '#0000FF'
		} else if (vn_kv >= 35) {
			color = '#00ff00'
		} else {
			color = '#408080'
		}
	}
	return {
		color,
		width,
	}
}
export const getSymbolPath = ({ type, vn_kv }) => {
	let path
	if (type == 'station') {
		if (vn_kv > 330) {
			path = ` 
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 10,50                         
			A 40,40 0 1,1 90,50              
			A 40,40 0 1,1 10,50
			M 20,50                         
			A 30,30 0 1,1 80,50              
			A 30,30 0 1,1 20,50
			`
		} else {
			path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 15,50                         
			A 35,35 0 1,1 85,50              
			A 35,35 0 1,1 15,50    
			`
		}
	}else{
		path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 14.644660940672622,85.35533905932738
			L 85.35533905932738,14.644660940672622
			M 20,30
			L 45,30
			M 55,60                         
			A 10,10 0 1,1 75,60   
			M 75,60
			A 10,10 0 1,0 95,60   
			M 75,60
			A 10,10 0 1,0 95,60   
		`
	}
	return path
}
export const getMapServies = (data,partition,{point_data,line_data},flag1,flag2,flag3,feedinData)=>{
    let lineData = []
    let mapData = []
    let data1 = []
    let data2 = []
    if(flag2){
        mapData = Object.keys(data).map(item=>{
            return {
                name:item,
                values:data[item].power_supply_margin,
                value:data[item].power_supply_margin<0?0:data[item].power_supply_margin<50?1:2,
            }
        })
    }
    if(flag3){
        data1 = getFeedinLine(feedinData, partition, 0).data1
        data2 = getFeedinLine(feedinData, partition, 0).data2
    }
    if(flag1){
        line_data.forEach(item=>{
            let arr = Object.keys(item.info)
            if(arr.length==1){
                let obj = Object.assign({...item},{
                    id:item.info[arr[0]],
                    name:arr[0],
                    coord:item.coords,
                    value:item.pRateDetail[0],
                    values:item.pRateDetail[0]*item.limitDetail[0],
                    label:{
                        distance:echartsResize(-4),
                    }
                })
                lineData.push(obj)
            }else if(arr.length==2||arr.length==3||arr.length==4){
                let coords
                if(arr.length==2){
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.003/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.003/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                }else{
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.0065/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.0065/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:arr.length==3?item.info[arr[2]]:item.info[arr[3]],
                        name:arr.length==3?arr[2]:arr[3],
                        coord:item.coords,
                        coords:coords[1],
                        value:arr.length==3?item.pRateDetail[2]:item.pRateDetail[3],
                        values:arr.length==3?item.pRateDetail[2]*item.limitDetail[2]:item.pRateDetail[3]*item.limitDetail[3],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                    if(arr.length==3){
                        let obj = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                            value:item.pRateDetail[1],
                            values:item.pRateDetail[1]*item.limitDetail[1],
                            label:{
                                distance:echartsResize(-4),
                            }
                        })
                        lineData.push(obj)
                    }else if(arr.length==4){
                    let coords
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.002/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.002/partitionScale[partition].scale)
                    let line3 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    let line4 = Object.assign({...item},{
                        id:item.info[arr[2]],
                        name:arr[2],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[2],
                        values:item.pRateDetail[2]*item.limitDetail[2],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-8),
                        }
                    })
                    lineData = lineData.concat([line3,line4])
                    }
                }
            }
        })
    }
    let option={
        geo: {
            map: partition,
            zoom: 1,
            silent: true,
            animation:false,
            // center:null,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 8,
                shadowOffsetY: 4,
            },
            scaleLimit:{
                min:1,
            },
            emphasis: {
                disabled:true,
            },
            show:partition=='全省'?true : false,
            roam:true,
        },
        animation: false,
        tooltip:{
            formatter: (params) => {
                if (params.componentSubType == 'lines') {
                    if([3].includes(params.componentIndex)){
                        return params.data.lineName +':'+fixInteger(params.value||0,0)+'MW'
                    }
                    return params.data.name+':'+(Math.abs(params.value)*100).toFixed(2)+'%' 
                } else if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
                    if(params.data.powerflow_detail.length!=0){
                        let str = Object.keys(params.data.info).reduce((a, b,index) => {
                            return a+ `<span style="font-size: 14px;color: #000000;">&nbsp;${b +':'+fixInteger(100*Math.abs(params.data.powerflow_detail[index]))+' %'}</span><br/>`
                        },'')
                        return `
                        <span style="font-size: 16px;color: #000000;">${params.data.name +':'+fixInteger(100*Math.abs(params.value[2]))+' %'}</span><br/>
                        `+ str
                    }
                } else if (params.componentSubType == 'map') {
                    let v = (params.data.values).toFixed(2)
                    let c = params.data.value
                    let color = c ==2 ? '#4ec48c' : c==1 ? '#fbb03b':'#bf6c66'
                    let name = params.data.name
                    return `
                    <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                    <span style="font-size: 12px;color: #000000;letter-spacing: 0;">供电裕度</span>
                    <span style="font-size: 20px;letter-spacing: 0;color: ${color};">${v}万千瓦</span>
                    `
                }
            },
        },
        visualMap: [
            {
                show: flag1,
                seriesIndex: [1],
                realtime: false,
                type:'piecewise',
                left:echartsResize(10),
                bottom:echartsResize(7),
                itemGap:echartsResize(10),
                itemHeight:echartsResize(14),
                itemWidth:echartsResize(20), 
                textGap:echartsResize(10),
                textStyle:{
                    color:'#fff',
                    fontSize:echartsResize(12),
                },
                pieces: flag2?[
                    {gt: 1,label: '100%以上', color: '#882C39'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#FF6464'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#FF9C40'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#FFD12D'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#FFF5A5'}         
                ]:[
                    {gt: 1,label: '100%以上', color: '#ff6464'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#ffaa64'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#fff5a5'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#55e9bc'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#11cbd7'}                 // (-Infinity, 5)
                ]
            },
            {
                show: flag2,
                seriesIndex: [0],
                type:"piecewise",
                itemGap:echartsResize(10),
                itemWidth:flag1?echartsResize(20):echartsResize(30), 
                itemHeight:echartsResize(14),
                textGap:echartsResize(10),
                pieces: flag1?[
                    {value: 0, label: '不足', color: '#253b6e'},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张', color: '#1f5f8b'},  // [123, 123]
                    {value: 2, label: '充裕', color: '#1891ac'},  // [123, 123]
                    ]:[
                    {value: 0, label: '不足', color: '#bf6c66',symbol:"image://"+new URL(`@/assets/icon/mapIcon1.png`, import.meta.url).href},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张', color: '#fbb03b',symbol:"image://"+new URL(`@/assets/icon/mapIcon2.png`, import.meta.url).href},  // [123, 123]
                    {value: 2, label: '充裕', color: '#4ec48c',symbol:"image://"+new URL(`@/assets/icon/mapIcon3.png`, import.meta.url).href},  // [123, 123]
                ],
                inRange: {  //对应范围内颜色
                    color: ['#bf6c66', '#fbb03b', '#4ec48c']
                },
                outOfRange:{
                    color:['rgb(44,82,118)']
                },
                left:echartsResize(105),
                bottom:echartsResize(56),
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            }
        ],
        toolbox: {
            show : true,
            top:15,
            right:0,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                // myDownload: {
                //     show: true,
                //     title: '下载数据',
                //     icon: "image://"+new URL(`@/assets/download_table.png`, import.meta.url).href,
                //     onclick: () => {
                //         
                //     },
                // },
                saveAsImage : {
                    show: true,
                    type:'png',
                    pixelRatio: 5,
                    backgroundColor:'transparent',
                    title: "保存为图片",
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    name: "图片_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                },
            }
        },
        series: [
            // 0 地图
            {
                type: 'map',
                zlevel: 1,
                map:partition,
                // center:null,
                zoom:1,
                roam:true,
                scaleLimit:{
                    min:1,
                },
                label: {
                    show:true,
                    fontSize:partition=='全省'?echartsResize(14):echartsResize(20),
                    color:"#000"
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc',
                    areaColor: 'rgb(44,82,118)',
                },
                data: mapData,
                emphasis: {
                    disabled:true,
                },
            },
            // 1 线路
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                effect: {
                    show: true,
                    period: 5, 
                    trailLength: 0,
                    symbol: 'arrow',
                    symbolSize: 5 
                },
                label:{
                    show:true,
                    color: '#fff',
                    fontSize:echartsResize(8),
                    position:'middle',
                    formatter: params => {
                        if(params.data.values){
                            return fixInteger(params.data.values,0)+''
                        }else{
                            return params.data.values+''
                        }
                    },
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    curveness: 0
                },
                data:partition=='全省'?lineData: lineData.map(item=>{
                    return Object.assign({},item,{
                        lineStyle:{
                            width:item.vn_kv==525 ? 2 : 1
                        }
                    })
                }),
            },
            // 2 变电站
            {
                animation:false,
                name: '起始涟漪城市',
                coordinateSystem: 'geo',
                zlevel: 3,
                type: 'scatter',
                symbolSize: (value, params) => {
					return echartsResize(getGisStyle(params.data).width * (['全省'].includes(partition)?4:4))* option.geo.zoom
				},
                itemStyle: {
					opacity: 1,
					color: '#fff',
					borderWidth: 1
				},
                type: 'scatter',
                symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:echartsResize(10),
                    position: 'top', 
                    color: '#fff',
                },
                data:flag1?point_data.map(item=>{
                    return {
                        ...item,
                        itemStyle: {
                            borderColor: getGisStyle({ vn_kv: item.vn_kv }).color// item.color || getGisStyle(item).color
                        }
                    }
                }):[],
            },
            // 3 区外直流 
            {
                type: 'lines',
                animation:false,
                zlevel: 1,
                polyline:true,
                lineStyle: {
                    width: 3, 
                    color: '#8BF1FE',
                    opacity: 1, 
                },
                data:flag1?data1:[],
            },
            // 4 区外直流名称 
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                symbolSize:0,
                label: {
                    show: true,
                    formatter: params => {
                        return params.name+" " + fixInteger(params.value[2]||0,0)+' 万千瓦'
                    },
                    fontSize: echartsResize(12),
                    lineHeight:echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    rich:{
                        fline: {
                            color: '#1890ff',
                            fontWeight:'bold',
                            fontSize: echartsResize(16),
                        },
                    },
                },
                data:flag1?data2:[],
            }
        ],
    }
    return option
}
export const getMapServiesCopy = (partition,{point_data,line_data},flag1,flag2,flag3,mapType,partitionMarginList,partitionEnergyList)=>{
    let lineData = []
    const fontSize = 6
    let mapData = mapType==1?partitionMarginList:partitionEnergyList
    const { data1, data2 } = getFeedinLine({}, partition, 0)
    if(flag1){
        line_data.forEach(item=>{
            let arr = Object.keys(item.info)
            if(arr.length==1){
                let obj = Object.assign({...item},{
                    id:item.info[arr[0]],
                    name:arr[0],
                    coord:item.coords,
                    value:item.pRateDetail[0],
                    values:item.pRateDetail[0]*item.limitDetail[0],
                    label:{
                        distance:echartsResize(-fontSize/2),
                    }
                })
                lineData.push(obj)
            }else if(arr.length==2||arr.length==3||arr.length==4){
                let coords
                if(arr.length==2){
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.003/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.003/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                }else{
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.0065/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.0065/partitionScale[partition].scale)
                    let line1 = Object.assign({...item},{
                        id:item.info[arr[0]],
                        name:arr[0],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[0],
                        values:item.pRateDetail[0]*item.limitDetail[0],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line2 = Object.assign({...item},{
                        id:arr.length==3?item.info[arr[2]]:item.info[arr[3]],
                        name:arr.length==3?arr[2]:arr[3],
                        coord:item.coords,
                        coords:coords[1],
                        value:arr.length==3?item.pRateDetail[2]:item.pRateDetail[3],
                        values:arr.length==3?item.pRateDetail[2]*item.limitDetail[2]:item.pRateDetail[3]*item.limitDetail[3],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line1,line2])
                    if(arr.length==3){
                        let obj = Object.assign({...item},{
                            id:item.info[arr[1]],
                            name:arr[1],
                            coord:item.coords,
                            value:item.pRateDetail[1],
                            values:item.pRateDetail[1]*item.limitDetail[1],
                            label:{
                                distance:echartsResize(-fontSize/2),
                            }
                        })
                        lineData.push(obj)
                    }else if(arr.length==4){
                    let coords
                    coords = getTangentialPoint(item.coords[0][0],item.coords[0][1],0.002/partitionScale[partition].scale,item.coords[1][0],item.coords[1][1],0.002/partitionScale[partition].scale)
                    let line3 = Object.assign({...item},{
                        id:item.info[arr[1]],
                        name:arr[1],
                        coord:item.coords,
                        coords:coords[0],
                        value:item.pRateDetail[1],
                        values:item.pRateDetail[1]*item.limitDetail[1],
                        label:{
                            distance:coords[0][0][1]>coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    let line4 = Object.assign({...item},{
                        id:item.info[arr[2]],
                        name:arr[2],
                        coord:item.coords,
                        coords:coords[1],
                        value:item.pRateDetail[2],
                        values:item.pRateDetail[2]*item.limitDetail[2],
                        label:{
                            distance:coords[0][0][1]<coords[1][0][1] ?echartsResize(0):echartsResize(-fontSize),
                        }
                    })
                    lineData = lineData.concat([line3,line4])
                    }
                }
            }
        })
    }
    let option={
        geo: {
            map: partition,
            zoom: 1.23,
            silent: true,
            animation:false,
            // center:null,
            itemStyle: {
                shadowColor: 'rgba(40,139,252,0.3)',
                shadowOffsetX: 8,
                shadowOffsetY: 4,
            },
            scaleLimit:{
                min:1.23,
            },
            emphasis: {
                disabled:true,
            },
            show:partition=='全省'?true : false,
            roam:true,
        },
        animation: false,
        tooltip:{
            formatter: (params) => {
                if (params.componentSubType == 'lines') {
                    if([4].includes(params.componentIndex)) return
                    return params.data.name+':'+fixInteger(Math.abs(params.value)*100).toFixed(2)+'%' 
                } else if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
                    if(params.data.powerflow_detail.length!=0){
                        let str = Object.keys(params.data.info).reduce((a, b,index) => {
                            return a+ `<span style="font-size: 14px;color: #000000;">&nbsp;${b +':'+fixInteger(100*Math.abs(params.data.powerflow_detail[index]))+' %'}</span><br/>`
                        },'')
                        return `
                        <span style="font-size: 16px;color: #000000;">${params.data.name +':'+fixInteger(100*Math.abs(params.value[2]))+' %'}</span><br/>
                        `+ str
                    }
                } else if (params.componentSubType == 'map') {
                    if(params.data.values==undefined) return
                    if (mapType == 1) {
                        let v = (params.data.values).toFixed(2)
                        let c = params.data.value
                        let color = c ==2 ? '#4ec48c' : c==1 ? '#fbb03b':'#bf6c66'
                        let name = params.data.name
                        return `
                        <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                        <span style="font-size: 12px;color: #000000;letter-spacing: 0;">供电裕度</span>
                        <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}万千瓦</span>
                        `
                    } else {
                        let v = (100*params.data.value).toFixed(2)
                        let name = params.data.name
                        let color = getGradientColor(rgbToHex(233,193,67),rgbToHex(7,192,96),((v-95)/5)>1?1:((v-95)/5))
                        return `
                        <span style="font-size: 14px;color: #000000;">${name}</span><br/>
                        <span style="font-size: 12px;color: #000000;letter-spacing: 0;">新能源消纳率</span>
                        <span style="font-size: 20px;color: ${color};letter-spacing: 0;">${v}%</span>
                        `
                    }
                }
            },
        },
        visualMap: [
            {
                show: flag1,
                seriesIndex: [1],
                realtime: false,
                type:'piecewise',
                left:echartsResize(10),
                bottom:echartsResize(7),
                itemGap:echartsResize(5),
                itemHeight:echartsResize(14),
                itemWidth:echartsResize(20), 
                textGap:echartsResize(10),
                textStyle:{
                    color:'#fff',
                    fontSize:echartsResize(12),
                },
                pieces: [
                    {gt: 1,label: '100%以上', color: '#882C39'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#FF6464'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#FF9C40'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#FFD12D'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#FFF5A5'}         
                ]
            },
            {
                show: mapType==1?true:false,
                seriesIndex: mapType==1?[0]:[],
                type:"piecewise",
                itemGap:echartsResize(10),
                itemWidth:flag1?echartsResize(20):echartsResize(30), 
                itemHeight:echartsResize(14),
                textGap:echartsResize(5),
                pieces: flag1?[
                    // {lt: 0, label: '不足', color: '#511e78'},  // [123, 123]
                    // {gt: 0, lte: 20,label: '紧张', color: '#8b2f97'},  // [123, 123]
                    // {gt: 20, label: '充裕', color: '#cf56a1'},  // [123, 123]
                    {value: 0, label: '不足（供电裕度<0）', color: '#253b6e'},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#1f5f8b'},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#1891ac'},  // [123, 123]
                ]:[
                    {value: 0, label: '不足（供电裕度<0）', color: '#bf6c66',symbol:"image://"+new URL(`@/assets/icon/mapIcon1.png`, import.meta.url).href},  // [123, 123]
                    {value: 1, lte: 20,label: '紧张（供电裕度<最大负荷的5%）', color: '#fbb03b',symbol:"image://"+new URL(`@/assets/icon/mapIcon2.png`, import.meta.url).href},  // [123, 123]
                    {value: 2, label: '充裕（供电裕度≥最大负荷的5%）', color: '#4ec48c',symbol:"image://"+new URL(`@/assets/icon/mapIcon3.png`, import.meta.url).href},  // [123, 123]
                ],
                inRange: {  //对应范围内颜色
                    color: ['#bf6c66', '#fbb03b', '#4ec48c']
                },
                outOfRange:{
                    color:['rgb(44,82,118)']
                },
                left:echartsResize(105),
                bottom:echartsResize(35),
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            },
            {
                show: mapType==2?true:false,
                seriesIndex: mapType==2?[0]:[],
                min:0.95,
                max:1,
                realtime: false,
                calculable: true,
                color:['rgb(7,192,96)','rgb(233,193,67)'],
                outOfRange:{
                    color:['rgb(0,27,72)']
                },
                // color:['rgb(0,168,252)','rgb(0,35,212)'],
                left:echartsResize(105),
                bottom:echartsResize(5),
                itemHeight: echartsResize(85),
                itemWidth:echartsResize(16),
                formatter: function (value) {
                    return value*100+'%'; // 范围标签显示内容。
                },
                textStyle:{
                    color:"#fff",
                    fontSize:echartsResize(12),
                }
            }
        ],
        series: [
            // 0 地图
            {
                type: 'map',
                zlevel: 1,
                map:partition,
                // center:null,
                zoom: 1.23,
                roam:true,
                scaleLimit:{
                    min:1.23,
                },
                label: {
                    show:true,
                    fontSize:partition=='全省'?echartsResize(10):echartsResize(20),
                    color:"#fff"
                },
                selectedMode: false,
                itemStyle: {
                    borderWidth: echartsResize(0.5),
                    borderColor: '#ccc',
                    areaColor: 'rgb(44,82,118)',
                },
                data: mapData,
                emphasis: {
                    disabled:true,
                },
            },
            //  线路
            {
                type: 'lines',
                zlevel: 2,
                animation:false,
                // effect: {
                //     show: true,
                //     period: 5, 
                //     trailLength: 0,
                //     symbol: 'arrow',
                //     symbolSize: 5 
                // },
                label:{
                    show:false,
                    color: '#fff',
                    fontSize:echartsResize(6),
                    position:'middle',
                    formatter: params => {
                        if(params.data.values){
                            return fixInteger(params.data.values,0)+''
                        }else{
                            return params.data.values+''
                        }
                    },
                },
                lineStyle: {
                    width: 1, 
                    opacity: 1, 
                    curveness: 0
                },
                data: lineData,
            },
            // 2 变电站
            {
                animation:false,
                name: '起始涟漪城市',
                coordinateSystem: 'geo',
                zlevel: 3,
                type: 'scatter',
                symbolSize: (value, params) => {
					return echartsResize(getGisStyle(params.data).width * (['全省'].includes(partition)?3:3))* option.geo.zoom
				},
                itemStyle: {
					opacity: 1,
					color: '#fff',
					borderWidth: 1
				},
                type: 'scatter',
                symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
                labelLayout: {
                    hideOverlap: true,
                },
                label: {
                    show: true,
                    formatter: params => {
                        return params.name;
                    },
                    fontSize:echartsResize(10),
                    position: 'top', 
                    color: '#fff',
                },
                data:flag1?point_data.map(item=>{
                    return {
                        ...item,
                        itemStyle: {
                            borderColor: getGisStyle({ vn_kv: item.vn_kv }).color// item.color || getGisStyle(item).color
                        }
                    }
                }):[],
            },
            // 区外直流 3
            {
                type: 'lines',
                animation:false,
                zlevel: 1,
                polyline:true,
                lineStyle: {
                    width: 3, 
                    color: '#8BF1FE',
                    opacity: 1, 
                },
                data:flag1&&flag3?data1:[],
            },
            // 区外直流名称 4
            {
                animation:false,
                name: '起始涟漪城市',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                symbolSize:0,
                label: {
                    show: true,
                    formatter: params => {
                        return params.name
                    },
                    fontSize: echartsResize(16),
                    lineHeight:echartsResize(10),
                    position: 'top',
                    color: '#fff',
                    rich:{
                        fline: {
                            color: '#1890ff',
                            fontWeight:'bold',
                            fontSize: echartsResize(16),
                        },
                    },
                },
                data:flag1&&flag3?data2:[],
            }
        ],
    }
    return option
}
export const getMarkData=(arr)=>{
    function findAllNonEmptyRanges(arr) {
        const ranges = [];
        let startIndex = -1;
        arr.forEach((item, index) => {
            if (item>0) {
              if (startIndex === -1) {
                startIndex = index;
              }
            } else {
              if (startIndex !== -1) {
                ranges.push([{
                    xAxis:startIndex
                },{
                    xAxis:index - 1
                }]);
                startIndex = -1;
              }
            }
        })
        if (startIndex !== -1) {
          ranges.push([{
            xAxis:startIndex,
          },{
            xAxis: arr.length - 1
          }]);
        }
        return ranges.length > 0 ? ranges : [];
    }
    return findAllNonEmptyRanges(arr)
}
export const getInterfaceOption = (data,timeData,limit,startIndex,endIndex) => {
    return { 
        tooltip: Object.assign({...tooltip},{
            // trigger: 'item',
            valueFormatter:(value)=>value.toFixed(2)+'万千瓦'
        }),
        // legend:Object.assign({
        //     ...legend,
        // }),
        xAxis: [Object.assign({ ...xAxis }, {
            data: (startIndex||endIndex)? timeData.slice(startIndex,endIndex+1) :timeData,
            axisLabel:{
                color:baseColor,
                fontSize: echartsResize(12),
                interval: 'auto',
            },
        })],
        grid: {
            x: echartsResize(40),
            y: echartsResize(40),
            x2: echartsResize(40),
            y2: echartsResize(50),
        },
        color:['#FCD620'],
        yAxis:[
            Object.assign({...yAxis},{
                name:'万千瓦',
                splitNumber:5,
            })
        ],
        dataZoom:Object.assign({...dataZoom},{
            start: 0,
            end: 100,
        }),
        series: [
            {
                type: 'line',
                name:'功率',
                data:(startIndex||endIndex)?data.slice(startIndex,endIndex+1):data,
                xAxisIndex: 0,
                yAxisIndex: 0,
                showSymbol: false,
                symbol: 'circle',
                markLine: {
                    symbol:'none',
                    data: [
                        {
                            yAxis:limit,
                            lineStyle:{
                                color:'red',
                                type:'solid',
                            },
                            label:{
                                show:true,
                                fontSize:echartsResize(14),
                                position:'start',
                                color:'red'
                            }
                        }
                    ]
                }
            }
        ]
    }
}
