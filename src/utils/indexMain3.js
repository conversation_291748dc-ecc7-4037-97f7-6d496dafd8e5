import {setting} from "../config/setting.config"
const { echartsResize, echartsLength, legend, tooltip, xAxis, yAxis, dataZoom } = setting
import { lineConfig } from "./constants"
import { fixInteger } from "./common"
import * as echarts from 'echarts'
export const getBarOption = ({using_hours_list}) => {
    return {
        grid: {
            x: echartsResize(35),
            y: echartsResize(35),
            x2: echartsResize(35),
            y2: echartsResize(35),
        },
        tooltip: Object.assign({...tooltip},{
        }),
        legend:Object.assign({...legend},
            {
                
            }
        ),
        xAxis: Object.assign({...xAxis},{
            data: ['春季','夏季','秋季','冬季'],
            axisLine: {
                color:'#333'
            }
        }),
        yAxis:Object.assign({...yAxis},{
            
        }),
        color:['#FFF5A5','#68BBC4'],
        series:[
            {
                type:'bar',
                name:'充电',
                data: using_hours_list.pump_charge,
                barWidth:echartsResize(25),
                itemStyle: {
                    color: function(params){
                        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#FFF5A5' // 渐变起始颜色
                        }, {
                            offset: 1,
                            color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                        }])
                    }
                }
            },
            {
                type:'bar',
                name:'放电',
                data: using_hours_list.pump_discharge,
                barWidth:echartsResize(25),
                itemStyle: {
                    color: function(params){
                        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#68BBC4' // 渐变起始颜色
                        }, {
                            offset: 1,
                            color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                        }])
                    }
                }
            },
        ],
    }
}
export const getPieSeries = (data1, { electricity }) => {
    const pieData1 = data1.map(item => {
        if (item.children) {
            return Object.assign({
                ...item,
            }, {
                value:item.children.reduce((a,b)=>b.value+a,0)
            })
        } else {
            return item
        }
    })
    const optiton = {
        type: 'pie',
        radius: ['40%','65%'],
        selectedOffset:echartsResize(20),
        selectedMode: 'false',
        label: {
            show: true,
            formatter:(params)=>{
                return params.value +'/'+ fixInteger(params.percent,1)+'%'
            },
            fontSize:echartsResize(8),
            color:'#fff',
        },
        labelLine: {
            length: 5,
            length2: 5,
            lineStyle: {
                width: 1,
            }
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }
    return {
        legend:Object.assign({...legend},
            {
                bottom:-5
            }
        ),
        tooltip: Object.assign({...tooltip},{
            trigger: 'item',
            formatter: (params) => {
                if (params.name == '火电') {
                    let str = ''
                    params.data.children.forEach(item => {
                        str += item.name + ' ' + item.value + (params.componentIndex==0 ? ' 万千瓦': ' 亿千瓦时')+'<br>'
                    })
                    return str
                } else {
                    return params.marker + ' ' + params.name+ ' ' + params.value + (params.componentIndex==0 ? ' 万千瓦': ' 亿千瓦时')
                }
                
            }
        }),
        // color:['#91cc75','#fac858','#73c0de','#5470c6','#ee6666'],
        series: [
            {
                ...optiton,
                center: ['25%', '45%'],
                data:pieData1.filter(item => item.value)
            },
            {
                ...optiton,
                center: ['75%', '45%'],
                data: [
                    {
                        value: electricity.feedin,
                        name: '区外来电'
                    },
                    {
                        value: electricity.hydro,
                        name: '水电'
                    },
                    {
                        value: electricity.solar,
                        name: '光伏'
                    },
                    {
                        value: electricity.wind,
                        name: '风电'
                    },
                    {
                        value: electricity.pump_discharge,
                        name: '抽蓄'
                    },
                    {
                        value: (electricity.coal?electricity.coal:0) + (electricity.gas?electricity.gas:0),
                        name: '火电',
                        children: [
                            {
                                "name": "煤电",
                                "value": electricity.coal?electricity.coal:0
                            },
                            {
                                "name": "燃气",
                                "value": electricity.gas?electricity.gas:0
                            }
                        ]
                    }
                ].filter(item => item.value),
            }
        ]
    }
}
export const getBarSeries = ({electricity}) => {
    return {
        legend:Object.assign({...legend},
            {
                left: echartsResize(80),
                top: '45%',
                orient: 'vertical',
                data: [
                    {
                        name:'风电弃电量',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#05BFC4' // 渐变起始颜色
                            }, {
                                    offset: 1,
                                    color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                            }])
                        }
                    },
                    {
                        name:'光伏弃电量',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#51DCB2' // 渐变起始颜色
                            }, {
                                    offset: 1,
                                    color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                            }])
                        }
                    }
                ]
            }
        ),
        tooltip: Object.assign({...tooltip},{
            trigger: 'item',
            formatter: (params) => {
                return params.marker + ' ' + params.seriesName+ ' ' + params.value + ' 亿千瓦时'
            }
        }),
        xAxis: Object.assign({ ...xAxis },
            {
                axisLabel: {
                   show:false
                },
                axisLine: {
                    lineStyle: {
                        color:'#05434C'
                    }
                }
            }
        ),
        grid: {
            x: echartsResize(250),
            y: echartsResize(35),
            x2: echartsResize(40),
            y2: echartsResize(35),
        },
        yAxis: Object.assign({ ...yAxis },
            {
                axisLabel: {
                   show:false
                },
            }
        ),
        series: [
            {
                type: 'bar',
                name:'风电弃电量',
                data: [electricity.wind_curtailment],
                barGap:'200%',
                barWidth:echartsResize(35),
                itemStyle: {
                    color: function(params){
                        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#05BFC4' // 渐变起始颜色
                        }, {
                            offset: 1,
                            color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                        }])
                    }
                }
            },
            {
                type: 'bar',
                name:'光伏弃电量',
                data:[electricity.solar_curtailment],
                barWidth:echartsResize(35),
                itemStyle: {
                    color: function(params){
                        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#51DCB2' // 渐变起始颜色
                        }, {
                            offset: 1,
                            color: 'rgba(0, 0, 0, 0)' // 渐变结束颜色
                        }])
                    }
                }
            },
        ]
    }
}
export const getLineOption = (charts_data,timeData,index, name, start, end, markLineList, startIndex, endIndex) => {
    const lineData = markLineList.map(item=>{
        return Object.assign(item,{
            label:{
                formatter: (params) => {
                    return (end-start)>25?'':params.name
                },
            }
        })
    })
    const lineConfigs = Object.keys(charts_data).filter(items =>lineConfig[items]).map(item => Object.assign({ lineName: item },lineConfig[item]))
    const legendData = lineConfigs.map(item => item.name)
	const legendColor = lineConfigs.map(item => item.color)
    let option={
        grid: {
            x: echartsResize(65),
            y: echartsResize(55),
            x2: echartsResize(55),
            y2: echartsResize(55),
        },
        tooltip: Object.assign({...tooltip},{
            triggerOn:"click",
            // alwaysShowContent:true,
            valueFormatter:(value)=>value.toFixed(2)+' 万千瓦',
            showContent:true
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
                left:echartsResize(150),
                itemGap:echartsResize(30),
                data:legendData
            }
        ),
        xAxis: Object.assign({ ...xAxis }, {
            data: (startIndex||endIndex)? timeData.slice(startIndex,endIndex+1) :timeData,
            axisPointer:{
                type:'none'
            },
        }),
        yAxis:Object.assign({...yAxis},{
            name: '功率(万千瓦)',
        }),
        color: legendColor,
        series: lineConfigs.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',
				showSymbol: false,
				color: item.color,
				data: (startIndex||endIndex)?charts_data[item.lineName].slice(startIndex,endIndex+1):charts_data[item.lineName],
				stack: item.type == 'area' ? '总量' : item.name
			}, item.type == 'area' ? {
				areaStyle: {
					opacity: 1
				},
				lineStyle: {
					width: 0
				}
			} : {})
		}).filter(item => item.data).sort((a, b) => a.sequence - b.sequence),
        dataZoom: [
            Object.assign({ ...dataZoom }, {
                bottom:10,
                start,
                end ,
                realtime:false,
            })  
        ],
    }
    option.series.push({
        animation: false,
        markLine:{
            symbol:'none',
            data:index?[
                {
                    name:name,
                    xAxis: timeData[index],
                    label:{
                        distance:echartsResize(0),
                    }
                }
            ].concat(lineData):lineData,
            lineStyle: {//标注线样式
                width:1,
                color:"red",
                type:'dashed'
            },
            label: {
                show:true,
                distance:echartsResize(-12),
                color: '#fff',
                formatter:'{b}',
                fontSize:echartsResize(12),
                position:'end',
            },  
            animation:false
        },
        type: 'line',
        data:[]
    })
    return option
}
export const getLineSeries = (data,timeData) => {
    let option = {
        grid: {
            x: echartsResize(65),
            y: echartsResize(55),
            x2: echartsResize(55),
            y2: echartsResize(55),
        },
        tooltip: Object.assign({...tooltip},{
            valueFormatter:(value)=>value.toFixed(2)+' 万千瓦',
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
            }
        ),
        xAxis: Object.assign({ ...xAxis }, {
            data: timeData,
        }),
        color:['#D56D39','#F94566'],
        yAxis:Object.assign({...yAxis},{
            name: '功率(万千瓦)',
        }),
        series: [
            {
                animation:false,
                smooth:true,
                symbol: 'circle',
                showSymbol: false,
                name: '消纳负荷',
                type: 'line',
                data: data.load_series,
            },
            {
                animation:false,
                smooth:true,
                symbol: 'circle',
                showSymbol: false,
                name: '削减负荷',
                type: 'line',
                data: data.load_cutailment_series,
                // lineStyle: {
				// 	width: 0
                // },
                areaStyle: {
                    
                }
            },
        ],
        dataZoom: [
            Object.assign({ ...dataZoom }, {
                bottom:10,
                start:0,
                end:5,
                realtime:false,
            })  
        ],
    }
    return option
}