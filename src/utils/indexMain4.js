import {setting} from "../config/setting.config"
const { echartsResize, echartsLength, legend, tooltip, xAxis, yAxis, dataZoom,baseColor } = setting
import { lineConfig } from "./constants"
import * as echarts from 'echarts'
import dayjs from 'dayjs'
export const getLineSeries = (type,timeData,data1,data2) => {
    let option = {
        grid: {
            x: echartsResize(60),
            y: echartsResize(30),
            x2: echartsResize(25),
            y2: echartsResize(25),
        },
        tooltip: Object.assign({...tooltip},{
            valueFormatter:(value)=>value.toFixed(2)+' 万千瓦',
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
            }
        ),
        color:type==1?['rgb(255, 245, 165)']:['rgb(242, 153, 61)','rgb(57, 181, 178)'],
        xAxis: Object.assign({ ...xAxis }, {
            data: timeData,
            axisPointer:{
                type: 'line',
                lineStyle: {
                    color:'#fff'
                }
            },
        }),
        yAxis:Object.assign({...yAxis},{
            name: '万千瓦',
        }),
        series: type==1? [
            {
                animation:false,
                smooth:true,
                symbol: 'circle',
                showSymbol: false,
                name: '负荷',
                type: 'line',
                data: data1,
                lineStyle: {
                    width: echartsResize(2)
                },
                areaStyle: {
                    opacity:0.4
                }
            },
        ] : [
            {
                animation:false,
                smooth:true,
                symbol: 'circle',
                showSymbol: false,
                name: '光伏',
                type: 'line',
                data: data1,
                lineStyle: {
                    width: echartsResize(2)
                },
                areaStyle: {
                    opacity:0.4
                }
            },{
                animation:false,
                smooth:true,
                symbol: 'circle',
                showSymbol: false,
                name: '风电',
                type: 'line',
                data: data2,
                lineStyle: {
                    width: echartsResize(2)
                },
                areaStyle: {
                    opacity:0.4
                }
            },
        ]
    }
    return option
}
export const getLineOption = (charts_data,timeData) => {
    const lineConfigs = Object.keys(charts_data).filter(items =>lineConfig[items]).map(item => Object.assign({ lineName: item },lineConfig[item]))
    const legendData = lineConfigs.map(item => item.name)
	const legendColor = lineConfigs.map(item => item.color)
    let option={
        grid: {
            x: echartsResize(60),
            y: echartsResize(30),
            x2: echartsResize(35),
            y2: echartsResize(20),
        },
        tooltip: Object.assign({...tooltip},{
            valueFormatter: (value) => value.toFixed(2) + ' 万千瓦',
        }),
        legend:Object.assign({...legend},
            {
                top: echartsResize(10),
                // left:echartsResize(150),
                itemGap:echartsResize(10),
                data:legendData
            }
        ),
        xAxis: Object.assign({ ...xAxis }, {
            data: timeData,
            axisPointer:{
                type: 'line',
                lineStyle: {
                    color:'#fff'
                }
            },
        }),
        yAxis:Object.assign({...yAxis},{
            name: '功率(万千瓦)',
        }),
        color: legendColor,
        series: lineConfigs.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',
				showSymbol: false,
				color: item.color,
				data: charts_data[item.lineName],
				stack: item.type == 'area' ? '总量' : item.name
			}, item.type == 'area' ? {
				areaStyle: {
					opacity: 1
				},
				lineStyle: {
					width: 0
				}
			} : {})
		}).filter(item => item.data).sort((a, b) => a.sequence - b.sequence),
    }
    return option
}
export const getPieSeries = (type,data) => {
    const optiton = {
        type: 'pie',
        radius: ['50%','70%'],
        selectedOffset:echartsResize(20),
        selectedMode: 'false',
        label: {
            show: true,
            formatter:(params)=>{
                return params.percent.toFixed(0)+'%'
            },
            color:'#fff',
        },
        labelLine: {
            length: echartsResize(5),
            length2: echartsResize(5),
            lineStyle: {
                width: 1,
            }
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }
    return {
        legend:Object.assign({...legend},
            {
                left: echartsResize(10),
                top:'middle',
                orient:'vertical'
            }
        ),
        tooltip: Object.assign({...tooltip},{
            trigger: 'item',
        }),
        color: ['#11cbd7','#55e9bc','#fff5a5','#ffaa64','#ff6464'],
        // color:['#91cc75','#fac858','#73c0de','#5470c6','#ee6666'],
        series: [
            {
                ...optiton,
                center: ['65%', '50%'],
                data: [
                    {
                        value: data[0],
                        name: '0小时'
                    },
                    {
                        value: data[1],
                        name: '0-10'
                    },
                    {
                        value: data[2],
                        name: '10-30'
                    },
                    {
                        value: data[3],
                        name: '30-50'
                    },
                    {
                        value: data[4],
                        name: '50以上'
                    },
                ]
            },
        ]
    }
}
export function get_canvas(watermark_text) {
    let waterMarkText = watermark_text;
    let canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    canvas.width = canvas.height = 100;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.globalAlpha = 0.08;
    ctx.font = '20px Microsoft Yahei';
    ctx.translate(50, 50);
    ctx.rotate(-Math.PI / 4);
    ctx.fillText(waterMarkText, 0, 0);
    return canvas;
}
export const getLineOptions = (type,data,time,charts_obj,add_watermark,watermark_text,start,end)=>{
    let option;
    let nameArr = data.map(item=>item.name)
    let datas=[]
    if (type != 'power_output') {
        data.forEach((item,index)=>{
            datas=datas.concat(item.data.map((items,indexs)=>{
                return[
                    indexs,index,items
                ]
            }))
        })
    }
    option = {
        backgroundColor: add_watermark?{
            type: 'pattern',
            image: get_canvas(watermark_text),
            repeat: 'repeat'
        }:undefined,
        //   title: {
        //       text: type=='power_output'?'功率曲线':'启停状态',
        //   },
        tooltip: type=='power_output'?{
            trigger: 'axis',
            backgroundColor: "rgba(255,255,255,1)",
            formatter: function (datas) {
                let res =  datas[0].name + '<br/>';
                    for (let i = 0, length = datas.length; i < length; i++) {
                        let format_value = datas[i].data;
                        if(format_value!==0){format_value=format_value.toFixed(2)}
                        res += datas[i].marker + datas[i].seriesName + ': <b>' + format_value + '</b> MW<br/>'
                    }
                    return res
                },
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        }:{
            trigger: 'item',
            position: 'bottom',
            formatter: function (e) {
                let t = 1 === e.data[2] ? '开启' : '停止';
                return '<b>' + nameArr[e.data[1]]+ '</b> ' + e.seriesName + '<br/>' + '<b>' + e.name + '</b>' + ': ' + e.marker + t;
            }
        },
        visualMap:type!='power_output'? {
            textStyle: { color: '#fff' },
            text: ['启', '停'],
            type: 'piecewise',
            orient: 'horizontal',
            pieces: [
                { value: 0, color: '#DD4F42', label: '停' },
                { value: 1, color: '#1CD66C', label: '启' }
            ],
            left: 'center',
            bottom: '0'
        }
        :undefined
        ,
        color:['rgb(84, 112, 198)','rgb(145, 204, 117)','rgb(250, 200, 88)','rgb(238, 102, 102)','rgb(115, 192, 222)','rgb(59, 162, 114)','rgb(252, 132, 82)','rgb(154, 96, 180)','rgb(234, 124, 204)','gray'],
        grid: type=='power_output'?{
            left: '2%',
            right: '2%',
            bottom: '5%',
            containLabel: true
        }:{
            height:'86%',width: '90%', left: '8%',
        }
        ,
        toolbox: {
            show : true,
            top:20,
            emphasis:{
                iconStyle:{
                    textPosition:'top'
                }
            },
            feature : {
                mark : {show: true},
                saveAsImage : {
                    show: true,
                    pixelRatio: 5,
                    title: "保存为图片",
                    icon: "image://"+new URL(`@/assets/download.png`, import.meta.url).href,
                    name: "功率曲线_"+dayjs(new Date()).format("YYYYMMDD_HHmmss"),
                },
                myReload: {
                    show: true,
                    title: "刷新",
                    icon: "image://"+new URL(`@/assets/refresh.png`, import.meta.url).href,
                    onclick: () => {
                        let opt = charts_obj.getOption();
                        charts_obj.clear();
                        charts_obj.setOption(opt);
                    },
                },
            },
        },
        xAxis: type=='power_output'?{
            type: 'category',
            boundaryGap: false,
            data: time,
            name: '时间(小时)',
            nameLocation:'center',
            nameTextStyle:{
                fontSize: 12,
                color:'#fff',
                padding:[5, 0, 0, 0]
            },
        }:{
            type: 'category',
            data: time,
            nameTextStyle: { padding: [10, 10, 10, -10] },
            splitArea: {
                show: true
            },
        },
        yAxis:type=='power_output'?
        {
            type: 'value',
            name: '功率(兆瓦)',
            nameLocation:'end',
            nameTextStyle:{
                fontSize:echartsResize(14),
                align: 'left',
                color:'#fff'
                // padding: [0, 0]
            },
        }:{
            type: 'category',
            data: nameArr,
            splitArea: {
                show: true
            },
            splitNumber: 5,
            axisLabel:{
                overflow: 'truncate',
                width: '60',
                align: 'right',
                color:baseColor
            }
        }
        ,
        series: type=='power_output'?data.map((item,index)=>{
            return Object.assign({...item},{
                type:'line',
                symbol:'none',
                index,
                emphasis: {
                    focus: 'self'
                },
            })
        }):[
            {
                name: '机组启停状态',
                type: 'heatmap',
                data: datas,
                label: {
                    show: false
                },
                markArea: {
                    animation: false,
                    animationDurationUpdate: 0,
                },
                emphasis: { itemStyle: { shadowBlur: 10, shadowColor: '#000' } }
            }
            ],
        dataZoom: type=='power_output'?[
            {
                type: 'slider',
                xAxisIndex: 0,
                filterMode: 'none',
                start: start,
                end: end,
            },
            {
                type: 'inside',
                xAxisIndex: 0,
                filterMode: 'none',
                start: start,
                end: end,
            },
        ]:[
            {
                type:"slider",
                show:true,
                xAxisIndex:[0],
                start: start,
                end: end,
                bottom: '3%',
            },
            {
                type:"inside",
                show:true,
                zoomOnMouseWheel: false,
                moveOnMouseMove: true,
                moveOnMouseWheel: true,
                yAxisIndex:[0],
                start: start,
                end: end,
                // start:100-(nameArr.length<=16?100:(16/nameArr.length*100)),
                // end: 100 ,
            },
            ]
        ,
    };
    return option
}
export const getHeatMapOption = (type, time, data) => {
    let datas = []
    let nameArr = data.map(item=>item.name)
    data.forEach((item,index)=>{
        datas=datas.concat(item.data.map((items,indexs)=>{
            return[
                indexs,index,items
            ]
        }))
    })
    let option  = {
        tooltip: {
            trigger: 'item',
            position: 'bottom',
            formatter: function (e) {
                let falgList = type ==0 ? ['检修','未检修'] :  ['开启', '停止']
                let t = 1 === e.data[2] ? falgList[0] : falgList[1];
                return '<b>' + nameArr[e.data[1]]+ '</b> ' + e.seriesName + '<br/>' + '<b>' + e.name + '</b>' + ': ' + e.marker + t;
            }
        },
        visualMap:{
            textStyle: { color: '#fff' },
            // text: type ==0 ? ['未检修','检修'] :  ['停','启'],
            type: 'piecewise',
            orient: 'horizontal',
            pieces:type ==0 ?  [
                { value: 0, color: '#1CD66C', label: '未检修' },
                { value: 1, color: '#DD4F42', label: '检修' }
            ] : [
                { value: 1, color: '#1CD66C', label: '启' },
                { value: 0, color: '#DD4F42', label: '停' },
                
            ],
            left: 'center',
            top: '0'
        },
        grid: {
            height:'70%',width: '88%', left: '10%',bottom: '0',top: '15%'
        },
        xAxis:{
            type: 'category',
            data: time,
            nameTextStyle: { padding: [10, 10, 10, -10] },
            splitArea: {
                show: true
            },
            axisLabel:{
                color:'#fff'
            }
        },
        yAxis:{
            type: 'category',
            data: nameArr,
            splitArea: {
                show: true
            },
            splitNumber: 5,
            axisLabel:{
                overflow: 'truncate',
                width: '60',
                align: 'right',
                color:'#fff'
            }
        },
        series:[
            {
                name: type==0?'机组检修状态':'机组启停状态',
                type: 'heatmap',
                data: datas,
                label: {
                    show: false
                },
                markArea: {
                    animation: false,
                    animationDurationUpdate: 0,
                },
                emphasis: { itemStyle: { shadowBlur: 10, shadowColor: '#000' } }
            }
        ],
    }
    return option
}