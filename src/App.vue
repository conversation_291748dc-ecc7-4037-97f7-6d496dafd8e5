<template>
    <a-config-provider :locale="zhCN">
        <a-spin size="large" :indicator="indicator" :spinning="loading">
            <div class="main" :class="{ 'home-mode': isHome }">
                <div class="main_body">
                    <index-header v-if="!isHome"></index-header>
                    <router-view/>
                </div>
            </div>
        </a-spin>
    </a-config-provider>
</template>
<script setup>
import * as echarts from 'echarts'
import { h } from 'vue'
import { onMounted, provide, reactive, ref ,computed} from '@vue/runtime-core'
import { useRoute } from 'vue-router'
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { LoadingOutlined } from '@ant-design/icons-vue';
import { getBaseOptionsApi,getCaseOptionsApi } from '@/api/index'
const store = dataStore()
const { loading} = storeToRefs(store)
provide('ec',echarts)
dayjs.locale('zh-cn')
// const indicator =h('div', {
//   class:'loading',
//   innerHTML:'Analyz&nbsp;ng...'
// })
const state = reactive({

})
const route = useRoute()
const isHome = computed(()=> route.path.startsWith('/home'))
const indicator = h(LoadingOutlined, {
    style: {
        fontSize: '54px',
    },
    spin: true,
    show:true,
})
onMounted(()=>{

})
</script>
<style lang="scss">
    .main{
        height: 1080px;
        overflow-y: overlay;
        background: url('@/assets/images/index/bg.png');
        background-size: 100% 100%;
        .main_body{
            height: 100%;
            padding: 0 38px 23px 37px;
            >div{
                height: calc(100% - 189px);
            }
        }
    }
    .home-mode{
        height: auto;
        background: none;
        .main_body{
            height: auto;
            padding: 0;
            >div{ height: auto; }
        }
    }
    .loading {
        -webkit-background-clip: text;
        color: transparent;
        animation:myfrist 1s ease-in infinite;
        position: relative;
        font-family: Arial, Helvetica, sans-serif;
        letter-spacing: 4px;
        transform: scale(2);
    }
    .loading::before {
        content: "";
        position: absolute;
        right: 88px;
        bottom: 14px;
        height: 18px;
        width: 3px;
        color: red;
        background: currentColor;
    }
    .loading::after {
        content: "";
        width: 7px;
        height: 7px;
        position: absolute;
        right: 86px;
        top: 5px;
        border-radius: 100%;
        background: red;
        animation: loading-animation 1s ease-in infinite;
    }
    @keyframes myfrist{
        0% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 0%,transparent 0%,transparent 100%);
        }
        10% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 10%,transparent 20%,transparent 100%);
        }
        20% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 20%,transparent 30%,transparent 100%);
        }
        30% {
        background-image: linear-gradient(to right,deeppink 0%,dodgerblue 30%,transparent 40%,transparent 100%);
        }
        40% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 40%,transparent 50%,transparent 100%);
        }
        50% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 50%,transparent 60%,transparent 100%);
        }
        60% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 60%,transparent 70%,transparent 100%);
        }
        70% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 70%,transparent 80%,transparent 100%);
        }
        80% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 80%,transparent 90%,transparent 100%);
        }
        90% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 90%,transparent 100%,transparent 100%);
        }
        100% {
            background-image: linear-gradient(to right,deeppink 0%,dodgerblue 100%);
        }
    }
    @keyframes loading-animation {
        0% {
            transform: translateY(5px) scaleY(1) scaleX(1.25);
        }
        25%,
        75% {
            transform: translateY(-4px) scaleY(1.2) scaleX(1);
        }
        50% {
            transform: translateY(-8px) scaleY(1) scaleX(1);
        }
        100% {
            transform: translateY(5px) scaleY(0.8) scaleX(0.8);
        }
    }
</style>

