@use "./variable.scss" as *;

// 全局滚动条
.scroll {
    overflow-y: overlay;

    &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        display: block;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background: rgb(196, 196, 196);
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
        border-radius: 3px;
    }
}

.yellow {
    color: $activeTextColor !important;
    font-weight: bolder;
}

.red {
    color: #ff4d4f !important;
    font-weight: bolder;
}

.green {
    color: #4ec48c !important;
    font-weight: bolder;
}

.echarts_map_tooltip {
    border: 1px solid rgb(29, 191, 220);
    background-color: rgb(6, 24, 34);
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    >p {
        font-size: 16px;
        line-height: 16px;
        font-weight: bolder;
        text-align: center;
    }

    >div {
        width: 180px;

        >p {
            display: flex;
            font-size: 14px;
            justify-content: space-between;
        }
    }
}

.point_bg {
    border: 1px solid $borderColor;
    position: relative;

    &::before,
    &::after,
    .point_content::before,
    .point_content::after {
        content: '';
        height: 8px;
        width: 8px;
        display: block;
        border-radius: 100%;
        position: absolute;
        background: $borderColor;
        z-index: 1;
    }

    .point_content::before {
        left: -4px;
        bottom: -4px;
    }

    .point_content::after {
        right: -4px;
        bottom: -4px;
    }

    &::before {
        left: -4px;
        top: -4px;
    }

    &::after {
        right: -4px;
        top: -4px;
    }
}

.title {
    line-height: 44px;
    height: 44px;
    text-align: center;
    font-size: 23px;
    font-weight: bolder;
    background-color: rgb(10, 64, 79);
}

.point_wrap {
    border: 1px solid $borderColor;

    .point {
        height: 8px;
        width: 8px;
        display: block;
        border-radius: 100%;
        position: absolute;
        background: $borderColor;
    }

    .point:nth-last-child(1) {
        left: -4px;
        top: -4px;
    }

    .point:nth-last-child(2) {
        right: -4px;
        top: -4px;
    }

    .point:nth-last-child(3) {
        left: -4px;
        bottom: -4px;
    }

    .point:nth-last-child(4) {
        right: -4px;
        bottom: -4px;
    }
}

.modal_top {
    height: 50px;
    border-radius: 8px 8px 0 0;
    background: rgb(17, 71, 87);

    p {
        font-size: 20px;
        color: #fff;
        line-height: 50px;
        padding-left: 20px;
    }

    span {
        top: 10px;
        color: #fff;
        right: 10px;
        font-size: 30px;
    }
}

p.underLine {
    text-align: center;
    font-size: 18px;
    font-weight: bolder;
    line-height: 36px;
    display: flex;
    flex-direction: column;
    align-items: center;

    // background: url('@/assets/images/index/linebg.png');
    // background-repeat: no-repeat;
    // background-position: center bottom;

    &::after {
        content: '';
        height: 3px;
        display: block;
        background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
        border-radius: 100%;
        width: 40%;
    }
}

.switch_select {
    display: flex;

    p {
        color: rgba(255, 255, 255, 0.88);
        font-family: 思源黑体;
        font-size: 14px;
        font-weight: 400;
        line-height: 31px;
        letter-spacing: 0px;
        text-align: center;
        min-width: 44.84px;
        padding: 0 10px;
        height: 31px;
        background: rgba(8, 143, 197, 0.36);

        &:hover {
            cursor: pointer;
        }
    }

    >p:first-child {
        border-radius: 4px 0px 0px 4px;
    }

    >p:last-child {
        border-radius: 0px 4px 4px 0px;
    }

    .active {
        background: rgb(8, 143, 197);
        color: rgb(255, 255, 255);
        font-weight: 700;
    }
}

.table_underline {
    padding: 0 5px;

    >div:first-child,
    >div:last-child>div {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;

        >p {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            text-align: center;
        }
    }

    >div:first-child {
        >p {
            line-height: 46px;
            background: url('@/assets/images/index/linebg.png');
            background-repeat: no-repeat;
            background-position: center bottom;
        }
    }

    >div:last-child {
        height: 168px;

        >div {
            p {
                line-height: 32px;
            }
        }
    }
}

.info_list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 10px 10px 0;

    p {
        font-size: 18px;
        line-height: 36px;
        height: 36px;
        width: 48%;
        position: relative;

        span {
            color: $activeTextColor;
            font-weight: bolder;
            margin-left: 5px;
            // position: absolute;
            // right: 0;
        }
    }
}

.grid_title {
    display: grid;
    grid-template-columns: 1fr 1fr;
    text-align: center;
    font-size: 18px;
    font-weight: bolder;
    line-height: 34px;
}

.main_content3_index1,
.main_content4_index1 {
    .top_content {
        display: flex;
        justify-content: space-between;
        height: 585px;
        margin-bottom: 10px;

        .top_left_content {
            width: 534px;
        }

        .top_middle_content {
            width: 756px;
            position: relative;

            .map_title {
                line-height: 40px;
                font-size: 24px;
                text-align: center;
                font-weight: bolder;
            }

            .map_checked {
                position: absolute;
                left: 15px;
                bottom: 110px;
                display: flex;
                align-items: center;
                z-index: 2;

                .ant-checkbox-wrapper {
                    span {
                        color: #fff;
                        font-size: 12px;
                        text-align: center;
                        line-height: 22px;
                    }
                }

                .ant-radio-wrapper {
                    color: #fff;
                    font-size: 12px;
                }

                .ant-radio-inner {
                    width: 16px;
                    height: 16px;

                    &::after {
                        transform: scale(1);
                        width: 8px;
                        height: 8px;
                        top: 11px;
                        left: 11.5px;
                        border-radius: 16px;
                        margin-block-start: -8px;
                        margin-inline-start: -8px;
                    }
                }

                input[type="radio"] {
                    margin: 0;
                }
            }

            .map {
                height: 543px;
            }
        }

        .top_right_content {
            width: 534px;
        }
    }

    .bottom_content {
        height: 275px;
        padding: 5px 10px;

        >div {
            height: 100%;
            width: 100%;

            .fade-enter-active,
            .fade-leave-active {
                transition: all .2s linear;
            }

            .fade-leave-to {
                transform: translateX(100%);
                opacity: 0;
            }

            .fade-enter-from {
                transform: translateX(100%);
                opacity: 0;
            }

            .timeList {
                right: 10px;
                top: 5px;
                height: 260px;
                width: 370px;
                background-color: #03161D;
                border: 1px solid #04BDC9;
                border-radius: 4px;
                position: absolute;
                z-index: 1;

                >div:first-child {
                    display: flex;
                    height: 32px;
                    align-items: center;
                    border-bottom: 1px solid #04BDC9;
                    padding-left: 15px;

                    >span {
                        font-size: 20px;
                        margin-right: 15px;
                        color: rgb(191, 191, 191);
                        opacity: 0.5;

                        &:hover {
                            cursor: pointer;
                            opacity: 1;
                        }
                    }

                    .active {
                        color: #04BDC9;
                    }

                    button {
                        line-height: 14px;
                        height: 24px;
                        background: #04BDC9;
                        border: none;
                        font-weight: bolder;
                    }
                }

                >div:last-child {
                    padding: 10px 20px 0;
                    height: 220px;

                    >div {
                        display: grid;
                        grid-template-columns: 0.8fr 3fr 5fr;

                        &:hover {
                            cursor: pointer;

                            p {
                                color: rebeccapurple;
                            }
                        }

                        &:deep(.ant-checkbox) {
                            .ant-checkbox-inner {
                                background-color: transparent;
                                border-color: #05BFC4;
                                border-radius: 0;

                                &::after {
                                    border-color: #05BFC4;
                                }
                            }

                            .ant-checkbox-checked:after {
                                border: none;
                            }
                        }

                        p {
                            font-size: 16px;
                            line-height: 32px;
                            color: #05BFC4;
                        }
                    }
                }

                .ding {
                    width: 25px;
                    height: 25px;
                    position: absolute;
                    right: 5px;

                    img {
                        width: 22px;
                        height: 22px;
                    }
                }
            }

            .ding {
                width: 32px;
                height: 32px;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #04BDC9;
                border-radius: 4px;
                margin-left: 10px;

                &:hover {
                    cursor: pointer;
                }

                img {
                    width: 24px;
                    height: 24px;
                }
            }

            .line_select {
                position: absolute;
                display: flex;
                right: 10px;
                top: 5px;
                align-items: center;
                z-index: 1;

                button {
                    font-size: 16px;
                    height: 32px;
                    background: $baseColor;
                    border: none;
                    font-weight: bolder;
                    color: #000;
                    border-radius: 0;
                    border-radius: 5px;
                    margin-left: 10px;
                    padding: 0 10px;
                }

                .ant-picker {
                    width: 350px;
                }

                .ant-select {
                    margin-left: 10px;
                }
            }

            .line {
                height: 100%;
                width: 100%;
            }
        }
    }
}

.map_info_move,
.map_info_no_move {
    z-index: 3;

    >img {
        width: 25px;
        position: absolute;
        z-index: 6;
        left: -25px;

        &:hover {
            cursor: pointer;
        }
    }

    >div {
        position: absolute;
        z-index: 1;
        width: 150px;
        height: 141px;
        background: url('@/assets/images/index2/map_info.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 5px 10px;

        &:hover {
            // cursor: move;
        }

        >p:first-child {
            text-align: center;
        }

        >p:nth-child(n+2) {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            line-height: 12px;
        }
    }
}

.map_info_no_move {
    top: 70px;
    left: 20px;
}

.echarts-tooltip {
    p {
        color: #000;
    }

    .p_span {
        display: inline-block;
        width: 40px;
        margin: 0 5px;
        text-align: right;
    }
}