@use "./variable.scss" as *;

.ant-spin {
    max-height: 100% !important;
}

.ant-spin-nested-loading,
.ant-spin-container {
    // height: 100%;
}

.ant-spin-dot {
    width: auto !important;
    height: auto !important;
}

.ant-modal {
    background-color: $bgColor;

    .ant-modal-content {
        background: transparent;
        padding: 0px !important;
    }
}

.ant-select,
.ant-modal .ant-select,
.ant-select:not(.ant-select-customize-input) {
    .ant-select-selector .ant-select-selection-placeholder {
        line-height: 31px;
        font-size: 16px;
    }

    .ant-select-selection-item {
        font-size: 16px;
        color: #fff;
        line-height: 31px;
    }

    .ant-select-selection-placeholder {
        color: #fff;
    }

    .ant-select-dropdown {
        padding: 0;
        color: #898989;

        .ant-select-item-option-selected {
            color: #fff;
            background: $baseColor;
            font-weight: normal;
        }

        .ant-select-item {
            padding: 0 10px;

            &:hover {
                color: #fff;
                background: $baseColor;
                font-weight: normal;
            }

            .ant-select-item-option-content {
                font-size: 16px;
                line-height: 32px;
            }
        }
    }

    .ant-select-arrow {
        // height: 31px;
        // width: 31px;
        // font-size: 20px;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        // background: $baseColor;
        // color: $AntBgColor;
        // margin-top: 0;
        // top: 0;
        // right: 0;

        color: $baseColor;
        font-size: 20px;
    }

    .ant-select-clear {
        height: 20px;
        width: 20px;
        top: 10px;

        span {
            font-size: 20px;
        }
    }

    .ant-select-selector {
        background: $AntBgColor;
        border: none;
        height: 31px;
    }
}

.ant-picker {
    background: $AntBgColor;
    border: none;
    padding: 0 5px;

    .ant-picker-input>input {
        line-height: 34px;
        font-size: 16px;
        color: #fff;

        &::placeholder {
            color: #fff;
        }
    }

    .ant-picker-range-separator {
        span {
            color: #fff;
        }
    }
}

.ant-cascader {
    width: 200px;
}

.ant-slider-tooltip {
    .ant-tooltip-inner {
        background: transparent;
        box-shadow: none;
        min-height: 0;
        padding: 0;
        font-size: 12px;
        color: $activeTextColor;
        margin-bottom: -13px;
        text-align: center;
    }

    .ant-tooltip-arrow {
        display: none;
    }
}

.ant-btn[disabled],
.ant-btn[disabled]:hover,
.ant-btn[disabled]:focus,
.ant-btn[disabled]:active {
    background: #f5f5f5 !important;
    color: rgba(0, 0, 0, 0.25) !important;
}

.ant-picker.ant-picker-disabled {
    background: rgb(245, 245, 245);

    input {
        color: rgba(0, 0, 0, 0.25) !important;
    }
}

.ant-checkbox-disabled .ant-checkbox-inner {
    background: rgb(245, 245, 245);
}