<template>
    <a-modal wrapClassName="interface_modal" :afterClose="()=>{emit('close')}" :mask="false" v-model:open="ifShow" :footer="null" :centered="true" :closable="false" :maskClosable="true">
        <div class="point_wrap">
            <a-spin size="large" :indicator="indicator" :spinning="state.loading">
                <div>
                    <div class="modal_top relative">
                        <close-outlined class="pointer absolute" @click="emit('close')" />
                        <p>断面时刻数据</p>
                    </div>
                    <div class="modal_content">
                        <div class="time_select">
                            <div class="switch_select">
                                <p @click="changeType('line_inf')" :class="state.type=='line_inf'?'active':''">500kV断面</p>
                                <p @click="changeType('transformer_inf')" :class="state.type=='transformer_inf'?'active':''">500kV主变</p>
                                <p @click="changeType('zone220_inf')" :class="state.type=='zone220_inf'?'active':''">220kV断面</p>
                                <p @click="changeType('zone220_trafo')" :class="state.type=='zone220_trafo'?'active':''">220kV主变</p>
                            </div>
                            <div class="flex-all-center">
                                <p>选择时间</p>
                                <!-- <a-date-picker :showNow="false" :show-time="{ format: 'HH:mm' }" format="YYYY-MM-DD HH:mm" valueFormat="YYYY-MM-DD HH:mm" v-model:value="state.searchTime" :allowClear="false">
                                    <template #suffixIcon>
                                        
                                    </template>
                                    <template #renderExtraFooter>
                                    </template>
                                </a-date-picker> -->
                                <a-range-picker :allowClear="false" v-model:value="state.searchTime" :showNow="false" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
                            </div>
                            <!-- <div class="flex-all-center" v-if="props.type==1">
                                <p>选择电压</p>
                                <div class="checkbox_group">
                                    <a-checkbox-group v-model:value="state.checked1" :options="state.vlevelOptions" />
                                </div>
                            </div> -->
                            <a-button type="primary" @click="selectTime" :disabled="!state.searchTime">确定</a-button>
                            <a-button type="primary" @click="downLoad">下载
                                <template #icon>
                                    <DownloadOutlined />
                                </template>
                            </a-button>
                            <!-- <div class="checkbox_group">
                                <a-checkbox-group v-model:value="state.checked" :options="state.options" />
                            </div> -->
                        </div>
                        <div class="modal_list">
                            <div>
                                <div>序号</div>
                                <div>时间</div>
                                <div>断面名称</div>
                                <div>断面描述</div>
                                <div>电压等级（kV）</div>
                                <div>限额</div>
                                <div>潮流</div>
                                <div>
                                    负载率（%）
                                    <!-- <div class="sort_icon">
                                        <CaretUpOutlined @click="handleSort(true,'rate')" />
                                        <CaretDownOutlined @click="handleSort(false,'rate')" />
                                    </div> -->
                                </div>
                            </div>
                            <div class="scroll">
                                <div v-for="(item,index) in state.dataList"> 
                                    <div>{{ index+1+pagination.pageSize*(pagination.current-1) }}</div>
                                    <div>{{ item.time }}</div>
                                    <a-tooltip>
                                        <template #title>{{ item.inf_name }}</template>
                                        <div class="ellipsis">
                                            {{ item.inf_name }}
                                        </div>
                                    </a-tooltip>
                                    <a-tooltip>
                                        <template #title>{{ item.inf_desc }}</template>
                                        <div class="ellipsis">
                                            {{ item.inf_desc }}
                                        </div>
                                    </a-tooltip>
                                    <div>{{ fixInteger(item.vlevel) }}</div>
                                    <div>{{ fixInteger(item.inf_limit) }}</div>
                                    <div>{{ fixInteger(item.value) }}</div>
                                    <div>{{ fixInteger(item.rate) }}</div>
                                </div>
                            </div>
                            <div>
                                <a-pagination @change="changePage" :showQuickJumper="true" :showSizeChanger="false" :hideOnSinglePage="true" v-model:current="pagination.current"  v-model:pageSize="pagination.pageSize" :total="pagination.total" show-less-items />
                            </div>
                        </div>
                    </div>
                </div>
            </a-spin>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
        </div>

    </a-modal>
</template>
<script setup> 
import { computed, onMounted, ref, reactive, watch, markRaw, inject,h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { dataStore } from '@/store/dataStore'
import { getInterfaceList,getInterfaceLists,DownloadData } from '@/api/index'     
import { fixInteger,exportExcels,downloadApiFile } from '@/utils/common'  
import dayjs from 'dayjs'
const store = dataStore()
const props=defineProps({
    searchTime:{
        type:Array,
        default:()=>[]
    },
})
const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '54px',
	},
	spin: true,
	show:true,
})
const ifShow = ref(true)
const emit = defineEmits(['close'])
const state = reactive({
    loading:false,
    year:undefined,
    month:undefined,
    searchTime:props.searchTime,
    dataList:[],
    allData:[],
    checked:[],
    checked1:[500,220],
    type:'line_inf',
    options:[
        { label: '线路', value: 'line' },
        { label: '主变', value: 'trafo' },
    ],
    vlevelOptions:[
        { label: '500kV', value: 500},
        { label: '220kV', value: 220 },
    ]
})
const pagination = reactive({
    pageSize:10,
    current:1,
    total:0
})
const changePage = ()=>{
    initAllData()
}
const handleSort = (flag,val)=>{
    if(state.dataList.length==0) return
    state.dataList = state.dataList.sort((a,b)=>{
        if(flag){
            return a[val] - b[val]
        }else{
            return b[val] - a[val]
        }
    })
}
const changeType = (val)=>{
    if (state.type == val) {
        return
    } else {
        state.type = val
    }
    pagination.current = 1
    initAllData()
}
const selectTime = ()=>{
    pagination.current = 1
    initAllData()
}
const initAllData  = ()=>{ 
    state.loading = true
    getInterfaceList({
        start_time: state.searchTime[0] + ' 00:00:00',
        end_time: state.searchTime[1] + ' 23:00:00',
        inf_type: state.type,
        page: pagination.current,
        size: pagination.pageSize
    }).then(res=>{
        state.loading = false
        state.dataList = res.data
        pagination.total = res.total
    }).catch(() => {
        state.loading = false
    })
}
const downLoad = ()=>{
    state.loading = true
    DownloadData({
        start_time: state.searchTime[0] + ' 00:00:00',
        end_time: state.searchTime[1] + ' 23:00:00',
        inf_type: state.type,
        // page: pagination.current,
        // size: pagination.pageSize
    }).then(res => {
        downloadApiFile(res)
        state.loading = false
    }).catch(() => {
        state.loading = false
    })
    return
    const xlsxData = [
        {
            'sheetName':'断面潮流数据',
            'title':['序号','断面名称','断面描述','电压等级（kV）','限额','潮流','负载率（%）'],
            data:state.dataList.map((item,index)=>[index+1,item.inf_name,item.inf_desc,item.vlevel,item.inf_limit,item.value,item.rate])
        }
    ]
    if(state.checked.length==0){
        exportExcels(xlsxData, '时刻潮流数据'+dayjs(new Date()).format("YYYYMMDD_HHmmss")+'.xlsx')
        state.loading = false
    }else{
        getInterfaceLists({
            time_value:state.searchTime+':00',
        }).then(res=>{
            exportExcels(xlsxData.concat(state.checked.map(item=>{
                return{
                    sheetName:item=='line'?'线路':'主变',
                    title:res.data[item].length!=0?Object.keys(res.data[item][0]):[],
                    data:res.data[item].map(items=>Object.values(items))
                }
            })), '时刻潮流数据'+dayjs(new Date()).format("YYYYMMDD_HHmmss")+'.xlsx')
            state.loading = false
        })
    }
}
onMounted(async() => {
    
})
</script>
<style lang="scss">
    .interface_modal{
        .ant-modal{
            width: auto!important;
        }
        .modal_content{
            padding:20px 40px 20px;
            width: 1350px;
            position: relative;
            .time_select{
                display: flex;
                align-items: center;
                .flex-all-center{
                    margin-left: 15px;
                    p{
                        font-size: 16px;
                        margin-right: 10px;
                    }
                }
                >button{
                    margin-left: 20px;
                    height: 32px;
                    font-size: 16px;
                    min-width: 80px;
                }
                >:last-child{
                    min-width: 50px;
                }
                .checkbox_group{
                    margin-left: 20px;
                    span{
                        color: #fff;
                        font-size: 16px;
                    }
                }
            }   
            .modal_list{
                padding: 20px 0;
                .sort_icon{
                    display: flex;
                    flex-direction: column;
                    position: absolute;
                    right: 10px;
                    top: 3px;
                    >span{
                        &:hover{
                            color:#1677ff
                        }
                    }
                }
                >div:first-child{
                    display: grid;
                    grid-template-columns: 0.5fr 1fr 1fr 1fr 0.7fr 0.7fr 0.7fr 0.7fr;
                    border-radius: 8px 8px 0 0;
                    background: rgb(17, 71, 87);
                    >div{
                        color: #fff;
                        font-size: 16px;
                        line-height: 38px;
                        text-align: center;
                        position: relative;
                    }
                }
                .scroll{
                    height: 620px;
                    padding: 10px;
                    >div:nth-child(2n+1){
                        background: rgb(11, 58, 104);
                    }
                    >div{
                        display: grid;
                        grid-template-columns: 0.5fr 1fr 1fr 1fr 0.7fr 0.7fr 0.7fr 0.7fr;
                        position: relative;
                        margin-bottom: 10px;
                        &::after {
                            content: '';
                            position: absolute;
                            height: 2px;
                            display: block;
                            background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
                            border-radius: 100%;
                            width: 90%;
                            bottom: -10px;
                            left: 5%;
                        }
                        div{
                            color: #fff;
                            font-size: 16px;
                            line-height: 50px;
                            text-align: center;
                        }
                    }
                    .ellipsis{
                        overflow: hidden;
                        text-overflow: ellipsis; // 显示省略符号来代表被修剪的文本。
                        white-space: nowrap; // white-space 
                        text-align: left;
                    }
                }
                >div:last-child{
                    display: flex;
                    align-items: center;
                    justify-self: flex-end;
                    a{
                        color: #fff;
                    }
                    span{
                        color: #fff;
                    }
                    .ant-pagination-item-active {
                        border-color: #fff;
                        a{
                            color: #000;
                        }
                    }
                    .ant-pagination-options-quick-jumper{
                        color: #fff;
                    }
                }
            }         
        }
    }
</style>