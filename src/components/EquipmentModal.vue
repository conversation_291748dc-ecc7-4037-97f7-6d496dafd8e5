<template>
    <a-modal wrapClassName="equipment_modal" :afterClose="()=>{emit('close')}" :mask="false" v-model:open="ifShow" :footer="null" :centered="true" :closable="false" :maskClosable="true">
        <div class="point_wrap">
            <a-spin size="large" :indicator="indicator" :spinning="state.loading">
                <div class="modal_content">
                    <div>
                        <div>
                            <p>机组名称</p>
                            <p>装机</p>
                            <p>发电功率下限</p>
                            <p>机组id</p>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.equipmentList">
                                <p>{{ item.name }}</p>
                                <p>{{ item.max_p_mw }}</p>
                                <p>{{ item.min_p_mw }}</p>
                                <p>{{ item.dev_id }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </a-spin>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
        </div>
    </a-modal>
</template>
<script setup> 
import { computed, onMounted, ref, reactive, watch, markRaw, inject,h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'      
import { getGenListApi } from '@/api/index'     
import { fixInteger } from '@/utils/common'     
const store = dataStore()
const  {caseId2} = storeToRefs(store)
const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '54px',
	},
	spin: true,
	show:true,
})
const ifShow = ref(true)
const emit = defineEmits(['close'])
const props=defineProps({
    
})
const state = reactive({
    case_id:caseId2,
    loading:true,
    equipmentList:[]
})
onMounted(async() => {
    state.loading = true
    getGenListApi({
        case_id:state.case_id,
    }).then(res=>{
        state.equipmentList = res.data
        state.loading = false
    })
})
</script>
<style lang="scss">
    .equipment_modal{
        .ant-modal{
            width: auto!important;
        }
        .modal_content{
            width: 1000px;
            padding:20px 30px 30px;
            position: relative;
            background: rgba(46, 126, 194, 0.06);
            >div{
                >div:first-child,>div:last-child>div{
                    display: grid;
                    // grid-template-columns: 1fr 1fr 1fr;
                    grid-template-columns: 1fr 0.8fr 0.8fr 1fr;
                    p{
                        text-align: center;
                        font-size: 16px;
                    }
                }
                >div:first-child{
                    >p{
                        position: relative;
                        display: flex;
                        justify-content: center;
                        line-height: 50px;
                        font-size: 18px;
                        &::after {
                            position: absolute;
                            content: '';
                            height:2px;
                            display: block;
                            background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
                            border-radius: 100%;
                            width: 70%;
                            bottom: 0;
                        }
                    }
                }
                >div:last-child>div{
                    p{
                        line-height: 36px;
                    }
                }
            }
            .scroll{
                overflow-y: auto;
                height: 500px;
            }
        }
    }
</style>