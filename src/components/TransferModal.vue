<template>
  <a-modal wrapClassName="modal_tranfer" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <div class="user-select flex align-center flex-direction">
            <div class="">
                <p class="bolder">{{ props.transferName }}</p>
                <close-outlined class="pointer absolute" @click="emit('close')" />
            </div>
            <div class="modal_tranfer_content">
                <div class="modal_tranfer_p grid">
                    <p>可选曲线</p>
                    <p>已选曲线</p>
                </div>
                <div>
                    <a-transfer
                        v-model:target-keys="state.targetKeys"
                        :data-source="props.transferData"
                        show-search
                        :filter-option="filterOption"
                        :render="item => item"
                        @change="handleChange"
                        @search="handleSearch"
                    >
                        <template
                        #children="{
                            direction,
                            filteredItems,
                            selectedKeys,
                            disabled: listDisabled,
                            onItemSelectAll,
                            onItemSelect,
                        }"
                        >   
                        <a-table
                        :row-selection="
                            getRowSelection({
                                disabled: listDisabled,
                                selectedKeys,
                                onItemSelectAll,
                                onItemSelect,
                            })
                        "
                        :columns="direction === 'left' ? leftColumns : rightColumns"
                        :data-source="filteredItems"
                        :showHeader="false"
                        size="small"
                        :pagination="false"
                        :style="{ pointerEvents: listDisabled ? 'none' : null }"
                        :custom-row="
                            ({ key, disabled: itemDisabled }) => ({
                                onClick: () => {
                                    if (itemDisabled || listDisabled) return;
                                    onItemSelect(key, !selectedKeys.includes(key));
                                },
                                onDblclick:()=>{
                                    // dblAdd(key,direction)
                                }
                            })
                        "
                        />
                        </template>
                    </a-transfer>
                </div>
            </div>
            <a-button type="primary" @click="confirm">更新曲线</a-button>
        </div>
    </a-modal>
</template>
<script setup>
    import { inject, onMounted, watch } from '@vue/runtime-core'
    import { ref ,toRefs,reactive} from '@vue/reactivity'
    import { CloseOutlined} from '@ant-design/icons-vue';
    const emit = defineEmits(['close','confirm'])
   
    const props=defineProps({
        transferName: String,
        transferData:{
            type:Array,
            default:[]
        },
        transferSelectData:{
            type:Array,
            default:[]
        }
    })
    const state = reactive({
        ifShow:true,
        targetKeys:props.transferSelectData,
    })
    const closeModal = ()=>{
        emit('close')
    }
    const leftTableColumns = [{
        dataIndex: 'name',
    }];
    const rightTableColumns = [{
        dataIndex: 'name',
    }];
    const leftColumns = ref(leftTableColumns);
    const rightColumns = ref(rightTableColumns);
    const confirm = ()=>{
        emit('confirm',state.targetKeys)
    }
    const filterOption = (inputValue, option) => {
      return option.name.indexOf(inputValue) > -1;
    };
    const handleSearch = (dir, value) => {
      console.log('search:', dir, value);
    };
    const handleChange = (nextTargetKeys, direction, moveKeys) => {
    //   console.log('targetKeys: ', nextTargetKeys);
    //   console.log('direction: ', direction);
    //   console.log('moveKeys: ', moveKeys);
    };
    const dblAdd = (e,a)=>{
        if(a=='left'){
            state.targetKeys.push(e)
        }else{
            state.targetKeys=state.targetKeys.filter(item=>item!=e)
        }
    }
    const getRowSelection = ({
      disabled,
      selectedKeys,
      onItemSelectAll,
      onItemSelect,
    }) => {
      return {
        getCheckboxProps: item => ({
          disabled: disabled || item.disabled,
        }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({
            key,
          }) => key);
          onItemSelectAll(treeSelectedKeys, selected);
        },
        onSelect({
          key,
        }, selected) {
          onItemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
      };
    };
    
    onMounted(()=>{
        
    })
</script>
<style lang="scss">
    .modal_tranfer{
        .ant-modal{
            width: 800px!important;
            .ant-modal-body{
                padding: 16px 20px 20px;
                background-color: #fff;
                >div{
                    >div:first-child{
                        font-size: 30px;
                        p{
                            line-height: 48px;
                            color: #000;
                        }
                        span{
                            right: 10px;
                            top: 10px;
                            // color: $baseColor;
                        }
                    }
                    .modal_tranfer_content{
                        width: 100%;
                        >div:last-child{
                            height: 499px;
                            .ant-transfer{
                                height: 100%;
                            }
                        }
                        .ant-table-content{
                            height: 425px;
                            overflow: auto;
                            &::-webkit-scrollbar {
                                width: 4px;
                                height: 4px;
                                display: block;
                            }

                            &::-webkit-scrollbar-thumb {
                                border-radius: 3px;
                                background: rgb(196, 196, 196);
                            }

                            &::-webkit-scrollbar-track {
                                background-color: transparent;
                                border-radius: 3px;
                            }
                        }
                    }
                    .modal_tranfer_p{
                        grid-template-columns: 1fr 1fr;
                        margin-bottom: 20px;
                        p{
                            text-align: center;
                            font-size: 18px;
                            font-weight: bolder;
                            line-height: 30px;
                            color: #000
                        }
                    }
                    button{
                        height: 36px;
                        font-size: 16px;
                        border-radius: 5px;
                        margin-top: 20px;
                    }
                }
            }
        }
    }
</style>