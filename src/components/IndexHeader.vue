<template>
    <div class="supply_demand_header">
        <!-- 顶部主header -->
        <div class="main_header">
            <!-- 左侧主标题 -->
            <div class="main_title">
                <h1>供需风险识别和决策推演平台</h1>
            </div>

            <!-- 右侧课题选择 -->
            <div class="subject_tabs" v-if="state.type!='/'">
                <div class="subject_tab" :class="{ active: state.currentSubject === 1 }" @click="switchSubject(1)">
                    课题一
                </div>
                <div class="subject_tab" :class="{ active: state.currentSubject === 2 }" @click="switchSubject(2)">
                    课题二
                </div>
                <div class="subject_tab" :class="{ active: state.currentSubject === 3 }" @click="switchSubject(3)">
                    课题三
                </div>
            </div>
        </div>

        <!-- 课题三的二级导航 -->
        <div class="sub_nav" v-if="state.type!='/' && state.currentSubject === 3">
            <div class="nav_left">
                <!-- 数据优化配置 -->
                <div class="nav_item" :class="isActiveModule('FunctionModule1')" @click="changeUrl('/index/FunctionModule1')">
                    <span>数据优化配置</span>
                </div>

                <!-- 风险态势分析 -->
                <div class="nav_item" :class="isActiveModule('FunctionModule2')" @click="toggleDropdown('FunctionModule2')">
                    <span>风险态势分析</span>
                    <i class="dropdown_icon" :class="{ active: state.typeShow2 }"></i>
                </div>
                <div class="dropdown_menu" v-if="state.typeShow2">
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule2-1')">负荷特性分析</div>
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule2-2')">电源特性分析</div>
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule2-3')">区外电力交换特性分析</div>
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule2-4')">稳定断面特性分析</div>
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule2-5')">典型时刻反演</div>
                </div>

                <!-- 方案主要推演 -->
                <div class="nav_item" :class="isActiveModule('FunctionModule3')" @click="toggleDropdown('FunctionModule3')">
                    <span>方案主要推演</span>
                    <i class="dropdown_icon" :class="{ active: state.typeShow3 }"></i>
                </div>
                <div class="dropdown_menu" v-if="state.typeShow3">
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule3-1')">全年运行态势模拟</div>
                    <div class="dropdown_item" @click="changeUrl('/index/FunctionModule3-2')">故障校验及断面推演</div>
                </div>
            </div>

            <!-- 中间标题区域 -->
            <div class="nav_center">
                <h2>{{ getTitle() }}</h2>
            </div>

            <div class="nav_right">
                <!-- 返回首页按钮 -->
                <div class="home_btn" @click="backHome" v-if="state.type && state.type !== '/'">
                    <i class="home_icon"></i>
                    <span>首页</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { watch, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { reactive, ref } from '@vue/reactivity'

const router = useRouter()
const store = dataStore()
const { year1, year2, token, end_time } = storeToRefs(store)

/**
 * 组件状态管理
 */
const state = reactive({
    type: undefined,
    currentSubject: 3,       // 当前选中的课题，默认课题三
    typeShow2: false,        // 风险态势分析下拉菜单
    typeShow3: false,        // 方案主要推演下拉菜单
})

const timer = ref()

/**
 * 切换课题
 * @param {number} subjectNumber - 课题编号
 */
const switchSubject = (subjectNumber) => {
    state.currentSubject = subjectNumber
    closeAllDropdowns()

    // 根据课题切换到对应的默认页面
    if (subjectNumber === 1) {
        // 课题一的默认页面（可以根据需要修改）
        changeUrl('/index/FunctionModule1')
    } else if (subjectNumber === 2) {
        // 课题二的默认页面（可以根据需要修改）
        changeUrl('/index/FunctionModule1')
    } else if (subjectNumber === 3) {
        // 课题三的默认页面
        changeUrl('/index/FunctionModule3-1')
    }
}

/**
 * 切换下拉菜单显示状态
 * @param {string} menuType - 菜单类型
 */
const toggleDropdown = (menuType) => {
    if (menuType === 'FunctionModule2') {
        state.typeShow3 = false
        state.typeShow2 = !state.typeShow2
    } else if (menuType === 'FunctionModule3') {
        state.typeShow2 = false
        state.typeShow3 = !state.typeShow3
    }
}

/**
 * 判断当前模块是否激活
 * @param {string} module - 模块名称
 * @returns {string} - CSS类名
 */
const isActiveModule = (module) => {
    const currentPath = router.currentRoute.value.path
    if (module === 'FunctionModule1' && currentPath.includes('/FunctionModule1')) {
        return 'active'
    }
    if (module === 'FunctionModule2' && (
        currentPath.includes('/FunctionModule2') ||
        state.typeShow2
    )) {
        return 'active'
    }
    if (module === 'FunctionModule3' && (
        currentPath.includes('/FunctionModule3') ||
        currentPath.includes('/FunctionModule4') ||
        state.typeShow3
    )) {
        return 'active'
    }
    return ''
}

/**
 * 路由跳转
 * @param {string} url - 目标路径
 */
const changeUrl = (url) => {
    closeAllDropdowns()
    router.push({
        path: url,
    })
}

/**
 * 返回首页
 */
const backHome = () => {
    if (location.origin === 'http://localhost:7777') return
    window.location.href = location.origin + '/admin'
}

/**
 * 关闭所有下拉菜单
 */
const closeAllDropdowns = () => {
    state.typeShow2 = false
    state.typeShow3 = false
}

/**
 * 获取当前页面标题
 * @returns {string} - 页面标题
 */
const getTitle = computed(() => () => {
    if (router.currentRoute.value.fullPath == "/index/FunctionModule1") {
        return ''
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule3-1") {
        return year1.value +'年时序运行模拟'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-1"){
        return '负荷特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2"){
        return '电源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2-1"){
        return '常规电源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2-2"){
        return '新能源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-3"){
        return '区外电力交换特性分析'
    } else if (router.currentRoute.value.fullPath.includes("/index/FunctionModule2-4")){
        return '稳定断面特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-5"){
        return '典型时刻潮流分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule4-1") {
        return year2.value +'年时序运行模拟——短期模拟'
    } else {
        return ''
    }
})

/**
 * 组件挂载后的初始化
 */
onMounted(() => {
    document.addEventListener('click', (e) => {
        // 点击外部区域关闭下拉菜单
        if (!e.target.closest('.nav_item') && !e.target.closest('.dropdown_menu')) {
            closeAllDropdowns()
        }
    })
})

/**
 * 监听路由变化
 */
watch(() => router.currentRoute.value.path, (toPath) => {
    state.type = toPath
    closeAllDropdowns() // 路由变化时关闭所有下拉菜单
}, { immediate: true, deep: true })

/**
 * 监听token变化处理登录状态
 */
watch(() => token.value, (v) => {
    if (!v) {
        changeUrl('/')
    } else {
        const timeout = new Date(end_time.value).getTime() - Date.now()
        timer.value = setTimeout(() => {
            token.value = undefined
            sessionStorage.removeItem('token')
            sessionStorage.removeItem('end_time')
            changeUrl('/')
        }, timeout)
    }
}, { immediate: true, deep: true })
</script>
<style lang="scss" scoped>
.supply_demand_header {
    height: 189px !important;
    background: linear-gradient(135deg, #0a2a3a 0%, #1a4a5a 100%);
    border-bottom: 2px solid #00d4ff;
    position: relative;

    // 主header样式
    .main_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 40px 15px;
        height: 126px;

        .main_title {
            h1 {
                font-size: 46px;
                font-family: 'Microsoft YaHei', sans-serif;
                font-weight: bold;
                letter-spacing: 5px;
                color: #00f5ff;
                text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
                margin: 0;
                background: linear-gradient(45deg, #00f5ff, #00d4ff);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }

        .subject_tabs {
            display: flex;
            gap: 20px;

            .subject_tab {
                padding: 12px 24px;
                background: rgba(26, 74, 90, 0.6);
                border: 1px solid #00d4ff;
                border-radius: 8px;
                color: #00d4ff;
                font-size: 18px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 80px;
                text-align: center;

                &:hover {
                    background: linear-gradient(135deg, #00d4ff, #00b4df);
                    color: #ffffff;
                    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
                    transform: translateY(-2px);
                }

                &.active {
                    background: linear-gradient(135deg, #00d4ff, #00b4df);
                    color: #ffffff;
                    box-shadow: 0 0 15px rgba(0, 212, 255, 0.7);
                }
            }
        }
    }

    // 二级导航样式
    .sub_nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: calc(189px - 126px);
        padding: 0 40px;
        position: relative;

        .nav_left {
            display: flex;
            gap: 20px;
            position: relative;

            .nav_item {
                position: relative;
                padding: 12px 24px;
                background: linear-gradient(135deg, #1a4a5a, #2a5a6a);
                border: 1px solid #00d4ff;
                border-radius: 8px;
                color: #00d4ff;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
                min-width: 160px;
                justify-content: center;

                &:hover {
                    background: linear-gradient(135deg, #00d4ff, #00b4df);
                    color: #ffffff;
                    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
                    transform: translateY(-2px);
                }

                &.active {
                    background: linear-gradient(135deg, #00d4ff, #00b4df);
                    color: #ffffff;
                    box-shadow: 0 0 15px rgba(0, 212, 255, 0.7);
                }

                .dropdown_icon {
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 6px solid currentColor;
                    transition: transform 0.3s ease;

                    &.active {
                        transform: rotate(180deg);
                    }
                }
            }

            .dropdown_menu {
                position: absolute;
                top: 100%;
                left: 0;
                margin-top: 8px;
                background: rgba(26, 74, 90, 0.95);
                border: 1px solid #00d4ff;
                border-radius: 8px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(10px);
                z-index: 1000;
                min-width: 180px;

                .dropdown_item {
                    padding: 12px 20px;
                    color: #00d4ff;
                    font-size: 14px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border-bottom: 1px solid rgba(0, 212, 255, 0.2);

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background: rgba(0, 212, 255, 0.2);
                        color: #ffffff;
                        padding-left: 25px;
                    }
                }
            }
        }

        .nav_center {
            flex: 1;
            text-align: center;

            h2 {
                font-size: 28px;
                font-weight: bold;
                color: #00f5ff;
                margin: 0;
                text-shadow: 0 0 8px rgba(0, 245, 255, 0.4);
                letter-spacing: 2px;
            }
        }

        .nav_right {
            .home_btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 20px;
                background: linear-gradient(135deg, #1a4a5a, #2a5a6a);
                border: 1px solid #00d4ff;
                border-radius: 6px;
                color: #00d4ff;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    background: linear-gradient(135deg, #00d4ff, #00b4df);
                    color: #ffffff;
                    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
                }

                .home_icon {
                    width: 16px;
                    height: 16px;
                    background: currentColor;
                    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E") no-repeat center;
                    mask-size: contain;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .supply_demand_header {
        .header_title h1 {
            font-size: 36px;
        }

        .header_nav {
            .nav_left .nav_item {
                min-width: 140px;
                font-size: 14px;
                padding: 10px 18px;
            }

            .nav_center h2 {
                font-size: 24px;
            }
        }
    }
}
</style>