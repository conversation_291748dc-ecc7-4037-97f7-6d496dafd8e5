<template>
    <div class="new-home-header">
        <div class="header-inner">
            <div class="tabs-wrap">
                <div class="tabs-track" ref="trackRef">
                    <div
                        v-for="t in tabs"
                        :key="t.path"
                        class="tab-item"
                        :data-path="t.path"
                        :class="{ active: isActive(t.path), 'is-click': clickingPath===t.path }"
                        @click="onTabClick(t.path)"
                        ref="setItemRef"
                    >
                        <span class="label">{{ t.label }}</span>
                    </div>

                </div>
            </div>
        </div>
    </div>
    
</template>
<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const tabs = [
    { path: '/home/<USER>', label: '场景生成模拟' },
    { path: '/home/<USER>', label: '供需量化分析' },
    { path: '/home/<USER>', label: '资源配置评估与决策推演' },
]

const clickingPath = ref('')

const onTabClick = (path) => {
    clickingPath.value = path
    if (!route.path.startsWith(path)) {
        router.push(path)
    }
    // 点击后短暂保留点击态
    setTimeout(() => {
        if (clickingPath.value === path) clickingPath.value = ''
    }, 180)
}
const isActive = (path) => route.path.startsWith(path)



// 不需要复杂的监听，直接通过类名切换
</script>
<style scoped lang="scss">
.new-home-header {
    width: 100%;
    height: 96px;
    background: url('@/assets/image_new/header_bg.png') no-repeat center top,
                linear-gradient(135deg, #08221b 0%, #0e3b2f 100%);
    background-size: cover, 100% 100%;
}
.header-inner {
    display: flex;    
    align-items: flex-end;
    max-width: 100%;
    height: 100%;
    padding: 0 24px 8px 24px;
}
.brand {
    h1 {
        margin: 0;
        font-size: 30px;
        color: #ffffff;
        letter-spacing: 3px;
        text-shadow: 0 0 10px rgba(0, 255, 120, 0.55);
    }
}
.tabs-wrap {
    margin-top: 0;
    margin-left:45%;
}
.tabs-track {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 10px 12px;
    
}
.tab-item {
    position: relative;
    padding: 10px 22px;
    color: #dffcf6;
    font-size: 18px;
    cursor: pointer;
    transition: color .2s ease;
}
.tab-item .label { font-weight: 600; letter-spacing: 1px; }
.tab-item.active { 
    color: #ffffff; 
    // border-bottom: 2px solid #00ff7a;
    position: relative;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: -16px; /* 贴到背景图底部 */
    left: 0;
    right: 0;
    height: 2px;
    background: #00ff7a;
    box-shadow: 0 0 8px rgba(0, 255, 122, 0.6);
}
.tab-item.is-click .label { transform: translateY(-1px); opacity: .95; }
.tab-item .label { display: inline-block; transition: transform .12s ease, opacity .12s ease; }
.tab-item + .tab-item { margin-left: 8px; }


</style>


