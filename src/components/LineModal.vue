<template>
    <a-modal wrapClassName="modal_line" :afterClose="()=>{emit('close')}" :mask="false" v-model:open="ifShow" :footer="null" :centered="true" :closable="false" :maskClosable="true">
        <div class="point_wrap">
            <a-spin size="large" :indicator="indicator" :spinning="state.loading">
                <div class="bar_content">
                    <p class="modal_title">{{ props.title }}</p>
                    <div>
                        <div class="line_select" v-if="props.showSelect">
                            <a-range-picker format="YYYY-MM-DD HH:mm" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate" picker="date" :show-time="{ format: 'HH' }" v-model:value="state.searchTime" :allowClear="false">
                                <template #suffixIcon>
                                    
                                </template>
                            </a-range-picker>
                            <a-button  @click="changeTime">确定</a-button>
                        </div>
                        <div class="line" ref="line">

                        </div>
                    </div>
                </div>
            </a-spin>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
        </div>
    </a-modal>
</template>
<script setup> 
import { computed, onMounted, ref, reactive, watch, markRaw, inject,h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'     
import { getInterfaceOption } from '@/utils/indexMain'     
import { getInterfaceApi } from '@/api/index'     
import dayjs from 'dayjs'
const store = dataStore()
const  { caseId1,year1,timeData1,caseId2,year2,timeData2} = storeToRefs(store)
const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '54px',
	},
	spin: true,
	show:true,
})
const ifShow = ref(true)
const emit = defineEmits(['close'])
const props=defineProps({
    title:{
        type:String,
        default:''
    },
    showSelect:{
        type:Boolean,
        default:true
    },
    type:{
        type:Number,
        default:1
    },
})
const state = reactive({
    year:props.type==1?year1:year2,
    case_id:props.type==1?caseId1:caseId2,
    timeData:props.type==1?timeData1:timeData2,
    startIndex:undefined,
    endIndex:undefined,
    limit:undefined,
    lineData:[],
    loading:true,
})
const echarts = inject("ec");
const line = ref()
const lineChart = ref()
const disabledDate = (current )=>{
    // const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year;
}
const changeTime = () => {
    state.startIndex =state.timeData.findIndex(item=>item==state.searchTime[0])
    state.endIndex = state.timeData.findIndex(item => item == state.searchTime[1])
    initLine()
}
const initLine = async() => {
    const option = getInterfaceOption(state.lineData,state.timeData,state.limit,state.startIndex,state.endIndex)
    lineChart.value.setOption(option)
}
onMounted(async() => {
    state.loading = true
    lineChart.value = markRaw(echarts.init(line.value))
    state.searchTime = [state.timeData[0], state.timeData[state.timeData.length - 1]]
    getInterfaceApi({
        name:props.title,
        is_short:props.type,
        case_id:state.case_id
    }).then(res=>{
        if(res.code==200){
            state.limit = res.data.limit
            state.lineData = res.data.power
        }
        initLine()
        state.loading = false
    })
})
</script>
<style lang="scss">
    .modal_line{
        .ant-modal{
            width: auto!important;
        }
        .modal_title {
            line-height: 44px;
            text-align: center;
            font-size: 20px;
            font-weight: bolder;
            letter-spacing: 1px;
            background-color: rgb(10, 64, 79);
        }
        .bar_content{
            >div{
                width: 1000px;
                padding: 50px 30px 20px;
                position: relative;
                .line_select{
                    position: absolute;
                    display: flex;
                    align-items: center;
                    right: 20px;
                    top: 5px;
                    button {
                        font-size: 16px;
                        height: 32px;
                        background: $baseColor;
                        border: none;
                        font-weight: bolder;
                        color: #000;
                        border-radius: 0;
                        border-radius: 5px;
                        margin-left: 10px;
                        padding: 0 10px;
                    }

                    .ant-picker {
                        width: 350px;
                    }
                }
                .line{
                    height: 500px;
                }
            }
        }
    }
</style>