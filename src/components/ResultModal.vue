<template>
    <a-modal wrapClassName="modal_result" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <a-spin size="large" :indicator="indicator" :spinning="state.loading">
        <div class="user-select">
            <div class="modal_top relative">
                <close-outlined class="pointer absolute" @click="emit('close')" />
                <p>详情查看</p>
            </div>
            <div class="modal_result_content grid">
                <div class="flex-direction">
                    <p>曲线类型</p>
                    <a-select
                        v-model:value="state.select_type"
                        :options="state.type_options"
                        @change="ChangeSelectType"
                        :getPopupContainer="triggerNode=>{return triggerNode.parentNode}"
                    >
                    <template #suffixIcon><caret-down-outlined class="ant-select-suffix"/></template>
                    </a-select>
                    <p>设备类别</p>
                    <a-select
                        v-model:value="state.select_category"
                        :options="state.select_type=='power_output'? state.category_options_one: state.category_options_two"
                        @change="ChangeSelectCategory"
                        :getPopupContainer="triggerNode=>{return triggerNode.parentNode}"
                    >
                    <template #suffixIcon><caret-down-outlined class="ant-select-suffix"/></template>
                    </a-select>
                    <div class="select_line">
                        <div>
                            <p>可选曲线</p>
                            <div class="icon_list flex justify-between ">
                                <check-circle-outlined @click="selectAll"/>
                                <close-circle-outlined @click="selectNone"/>
                                <search-outlined @click="openTransfer(0)"/>
                            </div>
                        </div>
                        <div class="select_line_list">
                            <div v-for="(item,index) in state.category_list" :key="index">
                                <a-checkbox v-model:checked="item.checked" @change="changeCheck(item)">{{item.name}}</a-checkbox>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="line" ref="line"> -->
                <!-- <div class="line" ref="line" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`,width:`${state.width}px`}"> -->
                <div class="line" ref="line">

                </div>
                <div>
                    <div class="select_line">
                        <div>
                            <p>已选曲线</p>
                            <div class="icon_list flex justify-end">
                                <search-outlined @click="openTransfer(1)"/>
                            </div>
                        </div>
                        <div class="selected_line_list">
                            <div :class="getClassName(index)" @mouseenter="onMouseenter(index)" @mouseleave="onMouseLeave(index)" v-for="(item,index) in (state.select_type=='power_output'?state.select_power_list:state.select_status_list)" :key="index">
                                {{ item.name }} <close-square-outlined @click="remove(item,index)" />
                            </div>
                        </div>
                        <a-button type="primary" @click="clear">全部清除</a-button>
                    </div>
                </div>
            </div>
        </div>
        </a-spin>
    </a-modal>
    <transfer-modal  @confirm="confirm" v-if="state.transferShow" @close="state.transferShow=false" :transferData="state.transferData" :transferSelectData="state.transferSelectData" :transferName="state.transferName"></transfer-modal>
</template>
<script setup>
import { inject, onMounted, watch,computed,markRaw,onUnmounted} from '@vue/runtime-core'
import { ref, toRefs, reactive } from '@vue/reactivity'
import { h } from 'vue'
import { LeftOutlined,LoadingOutlined,CloseOutlined,CaretDownOutlined,CheckCircleOutlined,CloseCircleOutlined,SearchOutlined,CloseSquareOutlined} from '@ant-design/icons-vue';
import { getDeviceList,getDetailResultLineApi}  from '@/api/index'
import { getLineOptions } from '@/utils/indexMain4'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
    const indicator = h(LoadingOutlined, {
        style: {
            fontSize: '54px',
        },
        spin: true,
        show:true,
    })
    const store = dataStore()
    const  { caseId2,year,timeData2 } = storeToRefs(store)
    const echarts = inject("ec");
    const line = ref()
    const lineChart = ref()
    const lineOption = ref({})
    const state = reactive({
        case_id: caseId2,
        timeData: timeData2,
        ifShow:true,
        select_type:undefined,
        select_category:undefined,
        type_options:[],
        category_options_one:[],
        category_options_two:[],
        category_name_one:[],
        category_name_two:[],
        category_list:[],
        category_list_base:[],
        select_power_list:[],
        select_status_list:[],
        transferType:undefined,
        transfersShow:false,
        transfersName:undefined,
        transferData:[],
        transferSelectData:[],
        nameList:[],
        loading:true,
        activeIndex:undefined,
        start:0,
        end:100,
    })
    const emit = defineEmits(['close'])
    const closeModal = ()=>{
        emit('close')
    }
    const ChangeSelectType = (val)=>{
        state.select_category = val=='power_output'?'gen_output':'gen_state'
        // state.start=0
        // state.end=100
        state.category_list = state.category_list_base[state.select_category]
        initEcharts()
    }
    const ChangeSelectCategory = ()=>{
        state.category_list = state.category_list_base[state.select_category]
    }
    const onMouseenter = (index)=>{
        state.activeIndex = index
        if (state.select_type != 'power_output') {
            // let start = lineOption.value.series[0].data.findIndex((item)=>item[1]==index)
            // lineChart.value.dispatchAction({
            //     type: 'highlight',
            //     dataIndex: Array(state.timeData.length).fill(0).map((item,index) => {
            //         return start+index
            //     }),
            // });
        }else{
            lineChart.value.dispatchAction({
                type: 'highlight',
                seriesIndex: index,
            });
        }
        
    }
    const onMouseLeave = (index)=>{
        state.activeIndex = undefined
        if (state.select_type != 'power_output') {
            // let start = lineOption.value.series[0].data.findIndex((item)=>item[1]==index)
            // lineChart.value.dispatchAction({
            //     type: 'downplay',
            //     dataIndex: Array(state.timeData.length).fill(0).map((item,index) => {
            //         return start+index
            //     }),
            // });
        } else {
            lineChart.value.dispatchAction({
                type: 'downplay',
                seriesIndex: index,
            })
        }
        
    }
    const clear = ()=>{
        state.select_type=='power_output'?state.select_power_list=[]:state.select_status_list=[]
        let arr =  state.select_type=='power_output'? state.category_name_one:state.category_name_two
        Object.keys(state.category_list_base).forEach(item=>{
            if(arr.includes(item)){
                state.category_list_base[item].forEach(item=>item.checked=false)
            }
        })
        initEcharts()
    }
    const initEcharts = (val,index)=>{
        if(val){
            const option =  getLineOptions(state.select_type,state.select_type=='power_output'?state.select_power_list:state.select_status_list,state.timeData,lineChart.value,true,'xxx',state.start,state.end)
            lineChart.value.setOption(option,true)
            if(state.select_type=='power_output'){
                if(index==state.select_power_list.length){
                    state.activeIndex = undefined
                    lineChart.value.dispatchAction({
                        type: 'highlight',
                        seriesIndex: state.select_power_list.map((item,index)=>index),
                    })
                }else{
                    state.activeIndex = index
                    lineChart.value.dispatchAction({
                        type: 'highlight',
                        seriesIndex: index,
                    })
                }
            }
            return
        }
        if ((state.select_type == 'power_output' && state.select_power_list.length > 0) || (state.select_type != 'power_output' && state.select_status_list.length > 0)) {
            let obj =state.select_type=='power_output'? {
                gen_output: [],
                wind_output:[],
                wind_curtailment:[],
                solar_output:[],
                solar_curtailment:[],
                feedin_output:[],
                hydropower_output:[],
                stogen_output:[],
                load_output:[],
            } : {
                gen_state:[],
            }   
            Object.keys(obj).forEach(item=>{
                obj[item] = (state.select_type == 'power_output' ? state.select_power_list : state.select_status_list).filter(items => items.type == item).map(item1 => {
                    let objs = {}
                    objs[item1.key] = item1.name
                    return objs
                })
            })
            getDetailResultLineApi(
                {
                    case_id: state.case_id,
                    device: obj,
                    is_short:2
                }
            ).then(res=>{
                if (res.code == 200) {
                    (state.select_type=='power_output'?state.select_power_list:state.select_status_list).map(item=>{
                        return Object.assign(
                            item,
                            {
                                data:res.data[item.type].find(item1=>item1[item.name])[item.name]
                            }
                        )
                    })
                    const option =  getLineOptions(state.select_type, state.select_type=='power_output'?state.select_power_list:state.select_status_list,state.timeData,lineChart.value,true,'xxx',state.start,state.end)
                    lineOption.value = option
                    lineChart.value.setOption(option, true)
                    lineChart.value.on('datazoom', (event)=> {
                        state.start = event.start
                        state.end = event.end
                    })
                }
            })
        } else {
            const option =  getLineOptions(state.select_type,[],state.timeData,lineChart.value,true,'xxx',state.start,state.end)
            lineChart.value.setOption(option,true)
        }
        
    }
    const openTransfer = (val)=>{
        state.transferType = val
        if(val==0){
            state.transferName = (state.select_type=='power_output'?'功率曲线':'启停状态')+'-'+state.nameList[state.select_category]
            state.transferData = state.category_list
            state.transferSelectData = state.category_list.filter(item=>item.checked).map(items=>items.name)
        }else{
            state.transferData=[]
            if(state.select_type=='power_output'){
                state.transferName='功率曲线'
                Object.keys(state.category_list_base).forEach(item=>{
                    if(state.category_name_one.includes(item)){
                        state.transferData=state.transferData.concat(state.category_list_base[item].map(items=>{
                            return Object.assign({...items},{
                                key:items.typeName+items.name,
                                name:items.typeName+items.name,
                            })
                        }))
                    }
                })
                state.transferSelectData=state.select_power_list.map(item=>item.typeName+item.name)
            }else{
                state.transferName='启停状态'
                Object.keys(state.category_list_base).forEach(item=>{
                    if(state.category_name_two.includes(item)){
                        state.transferData=state.transferData.concat(state.category_list_base[item].map(items=>{
                            return Object.assign({...items},{
                                key:items.typeName+items.name,
                                name:items.typeName+items.name,
                            })
                        }))
                    }
                })
                state.transferSelectData=state.select_status_list.map(item=>item.typeName+item.name)
            }
        }   
        state.transferShow = true
    }
    const changeCheck = (item) =>{
        if(item.checked){
            state.select_type=='power_output'?state.select_power_list.push(item)
            :state.select_status_list.push(item)
        }else{
            state.select_type=='power_output'?state.select_power_list=state.select_power_list.filter(items=>item.name!=items.name)
            :state.select_status_list=state.select_status_list.filter(items=>item.name!=items.name)
        }
        initEcharts()
    }
    const getClassName = computed(() =>(index)=> {
        return  index==state.activeIndex ?  'animate '+ 'class'+(index%10): ' '+ 'class'+(index%10)
    })
    const selectAll = ()=>{
        state.category_list.forEach(item=>{
            if(!item.checked){
                state.select_type=='power_output'?state.select_power_list.push(item)
                :state.select_status_list.push(item)
                item.checked=true
            }
        })
        initEcharts()
    }
    const selectNone = ()=>{
        state.select_type=='power_output'?state.select_power_list=state.select_power_list.filter(items=>{
            if(items.type!=state.select_category){
                return items
            }else{
                items.checked = false
            }
        })
        :state.select_status_list=state.select_status_list.filter(items=>{
            if(items.type!=state.select_category){
                return items
            }else{
                items.checked = false
            }
        })
        initEcharts()
    }
    const confirm = (data)=>{
        if(state.transferType==0){
            state.category_list.forEach(item=>{
                if(data.includes(item.name)){
                    if(!item.checked){
                        state.select_type=='power_output'?state.select_power_list.push(item)
                        :state.select_status_list.push(item)
                        item.checked = true
                    }
                }else{
                    item.checked = false
                }
            })
            state.select_type=='power_output'?
                state.select_power_list=state.select_power_list.filter(item=>{
                return (item.type==state.select_category&&data.includes(item.name))||(item.type!=state.select_category)
                })
            :
                state.select_status_list=state.select_status_list.filter(item=>{
                    return (item.type==state.select_category&&data.includes(item.name))||(item.type!=state.select_category)
                })
        }else{
            if(state.select_type=='power_output'){
                Object.keys(state.category_list_base).forEach(item=>{
                    if(state.category_name_one.includes(item)){
                        state.category_list_base[item].forEach(items=>{
                            if(data.includes(items.typeName+items.name)){
                                if(!items.checked){
                                    state.select_power_list.push(items)
                                    items.checked = true
                                }
                            }else{
                                items.checked = false
                            }
                        })
                    }
                })
                state.select_power_list=state.select_power_list.filter(item=>data.includes(item.typeName+item.name))
            }else{
                Object.keys(state.category_list_base).forEach(item=>{
                    if(state.category_name_two.includes(item)){
                        state.category_list_base[item].forEach(items=>{
                            if(data.includes(items.typeName+items.name)){
                                if(!items.checked){
                                    state.select_status_list.push(items)
                                    items.checked = true
                                }
                            }else{
                                items.checked = false
                            }
                        })
                    }
                })
                state.select_status_list=state.select_status_list.filter(item=>data.includes(item.typeName+item.name))
            }
        }
        state.transferShow = false
        initEcharts()
    }
    const remove = (item,index)=>{
        item.checked = false
        state.select_type=='power_output'?state.select_power_list.splice(index,1)
        :state.select_status_list.splice(index,1)
        initEcharts(true,index)
    }   
    onMounted(()=>{
        lineChart.value = markRaw(echarts.init(line.value))
        getDeviceList({
            case_id: state.case_id,
            is_short:2
        }).then(res => {
            if(res.code==200){
                state.nameList = res.data.equipment_type_map.on_off_state.concat(res.data.equipment_type_map.power_output)
                state.type_options = res.data.line_type_map.map(item=>{
                    return{
                        label:item.menu_name,
                        value:item.id
                    }
                })
                state.select_category = res.data.equipment_type_map.power_output[0].id
                state.select_type = res.data.line_type_map[0].id
                let obj = {}
                Object.keys(res.data.device_detail).forEach(item=>{
                    obj[item]=res.data.device_detail[item].map((items)=>{
                        return {
                            key:items[0],
                            type:item,
                            typeName:state.nameList.find(item1=>item1.id==item).menu_name,
                            name:items[1],
                            // data:res.detail_data[item][items],
                            checked:false,
                        }
                    })
                })
                state.category_list_base = obj
                state.category_list = state.category_list_base[state.select_category]

                state.category_name_one = res.data.equipment_type_map.power_output.map(item=>item.id)
                state.category_name_two = res.data.equipment_type_map.on_off_state.map(item=>item.id)

                state.category_options_one=res.data.equipment_type_map.power_output.map(item=>{
                    return{
                        label:item.menu_name,
                        value:item.id
                    }
                })
                state.category_options_two=res.data.equipment_type_map.on_off_state.map(item=>{
                    return{
                        label:item.menu_name,
                        value:item.id
                    }
                })
                initEcharts()
            }
            state.loading = false
        })
    })
</script>
<style lang="scss">
  .modal_result{
    [class*="class0"] {
        border: 2px solid rgb(84, 112, 198);
        &:hover{
            box-shadow: rgb(84, 112, 198) 0px 2px 4px;
        }
    }
    [class*="class1"]{
        border: 2px solid rgb(145, 204, 117);
        &:hover{
            box-shadow: rgb(145, 204, 117) 0px 2px 4px;
        }
    }
    [class*="class2"]{
        border: 2px solid rgb(250, 200, 88);
        &:hover{
            box-shadow: rgb(250, 200, 88) 0px 2px 4px;
        }
    }
    [class*="class3"]{
        border: 2px solid rgb(238, 102, 102);
        &:hover{
            box-shadow: rgb(238, 102, 102) 0px 2px 4px;
        }
    }
    [class*="class4"]{
        border: 2px solid rgb(115, 192, 222);
        &:hover{
            box-shadow: rgb(115, 192, 222) 0px 2px 4px;
        }
    }
    [class*="class5"]{
        border: 2px solid rgb(59, 162, 114);
        &:hover{
            box-shadow: rgb(59, 162, 114) 0px 2px 4px;
        }
    }
    [class*="class6"]{
        border: 2px solid rgb(252, 132, 82);
        &:hover{
            box-shadow: rgb(252, 132, 82) 0px 2px 4px;
        }
    }
    [class*="class7"]{
        border: 2px solid rgb(154, 96, 180);
        &:hover{
            box-shadow: rgb(154, 96, 180) 0px 2px 4px;
        }
    }
    [class*="class8"]{
        border: 2px solid rgb(234, 124, 204);
        &:hover{
            box-shadow: rgb(234, 124, 204) 0px 2px 4px;
        }
    }
    [class*="class9"] {
        border: 2px solid gray ;
        &:hover{
            box-shadow: gray  0px 2px 4px;
        }
    }
    .animate{
        // animation: shakeX 1s;
    }
    .ant-modal{
        width:1820px!important;
        .ant-modal-body{
            background-color: rgb(11,30,45);
            .user-select{
                height: 930px;
                .modal_result_content{
                    padding: 20px;
                    height: 885px;
                    grid-template-columns: 1fr 2fr 1fr;
                    p{
                        font-size: 16px;
                        font-weight: bolder;
                        line-height: 32px;
                    }
                    >div{
                        height: 845px;
                    }
                    .ant-select{
                        width: 100%;
                    }
                    >div:first-child,>div:last-child{
                        padding: 0 10px;
                    }
                    >div:last-child{
                        .select_line{
                            background-color: transparent;
                            >div{
                                background-color: #fff;
                            }
                            >div:first-child{
                                margin-top: 0px;
                            }
                        }
                    }
                    .line{
                        transform-origin: 0 0;
                    }
                    .select_line{
                        background-color: #fff;
                        border-radius: 5px;
                        >div:first-child{
                            position: relative;
                            margin-top: 20px;
                            border-radius: 5px 5px 0 0;
                            border: 1px solid #d3d3d3;
                            border-top: 3px solid $borderColor;
                            p{
                                line-height: 50px;
                                font-size: 18px;
                                font-weight: normal;
                                text-indent: 1em;
                                color: #000;
                            }
                            .icon_list{
                                position: absolute;
                                width: 90px;
                                right: 20px;
                                top: 15px;
                                span{
                                    font-size: 18px;
                                    color: #ccc;
                                    &:hover{
                                        cursor: pointer;
                                        color: #000;
                                    }
                                }
                            }
                        }
                        .select_line_list,.selected_line_list{
                            &::-webkit-scrollbar {
                                width: 4px;
                                height: 4px;
                                display: block;
                            }
                            &::-webkit-scrollbar-thumb {
                                border-radius: 3px;
                                background: rgb(196, 196, 196);
                            }

                            &::-webkit-scrollbar-track {
                                background-color: transparent;
                                border-radius: 3px;
                            }
                            overflow: auto;
                            border: 1px solid #d3d3d3;
                            border-top: none;
                            border-radius:0 0 5px 5px;
                           >div{
                                font-size: 16px;
                            }
                        }
                        .select_line_list{
                            height: 640px;
                            >div{
                                font-weight: bolder;
                                line-height: 16px;
                                padding:8px 20px;
                                span{
                                    font-size: 16px;
                                }
                            }
                        }
                        .selected_line_list{
                            height: 740px;
                            >div{
                                height: 40px;
                                margin:10px 20px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                padding: 0 20px;
                                // color: var(--base-color);
                                &:hover{
                                    cursor: pointer;
                                }
                                span{
                                    margin-left: 20px;
                                    &:hover{
                                        cursor: pointer;
                                        color: #000;
                                    }
                                }
                            }
                        }
                        button{
                            width: 100%;
                            margin-top: 10px;
                            height: 40px;
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
  }
</style>