import { createWebHashHistory, createRouter } from 'vue-router';

// 新首页路由（旧路由整体保留但注释，后续需要时再迁移）
const routes = [
    {
        path: '/',
        redirect: '/home/<USER>'
    },
    {
        path: '/home',
        component: () => import('../views/HomePage/HomeLayout.vue'),
        children: [
            {
                path: 'scenario',
                name: 'ScenarioHome',
                component: () => import('../views/HomePage/ScenarioView.vue')
            },
            {
                path: 'quantify',
                name: 'QuantifyHome',
                component: () => import('../views/HomePage/QuantifyView.vue')
            },
            {
                path: 'resource',
                name: 'ResourceHome',
                component: () => import('../views/HomePage/ResourceView.vue')
            }
        ]
    },
    // 旧系统入口与模块（临时屏蔽保留）
    // {
    //     path: '/dashboard',
    //     name: 'dashboard',
    //     component: () => import('../views/HomePage/DashboardView.vue')
    // },
    // {
    //     path: '/index',
    //     name: 'index',
    //     component: () => import('../views/IndexRouter/IndexView.vue'),
    //     children: [ ... ]
    // },
    {
        path: '/:pathMatch(.*)*',
        redirect: '/home/<USER>'
    }
];

const router = createRouter({
    history: createWebHashHistory(),
    routes,
});

export default router;
