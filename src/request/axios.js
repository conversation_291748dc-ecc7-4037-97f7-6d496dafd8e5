import { network } from '@/config/net.config';
import { dataStore } from '@/store/dataStore';
import { message } from 'ant-design-vue';
import axios from "axios";
import { storeToRefs } from 'pinia';
const request = axios.create({
    baseURL: network.baseURL,
    // timeout: network.requestTimeout,
    headers: {
        'Content-Type': network.contentType,
    },
})
request.interceptors.request.use(
    (config) => {
        const store = dataStore()
        const { token } = storeToRefs(store)
        config.headers.token = token.value
        return config
    },
    (error) => {
        console.log(error);
        return Promise.reject(error)
    }
)
request.interceptors.response.use(
    (response) => {
        if (network.successCode.includes(response.data.code)) {
            return response.data
        } else if (network.errorCode.includes(response.data.code)) {
            if (response.data.code == 1001) {
                // message.error(response.data.msg)
                const store = dataStore()
                const { token, end_time } = storeToRefs(store)
                if (token.value == undefined) {
                    return Promise.reject(response.data)
                } else {
                    message.error(response.data.msg)
                }
                end_time.value = undefined
                token.value = undefined
                sessionStorage.removeItem('token')
                sessionStorage.removeItem('end_time')
                return Promise.reject(response.data)
            } else {
                if (response.data.msg) {
                    message.error(response.data.msg)
                }
                return Promise.reject(response.data)
            }
        } else {
            return response
        }
    },
    (error) => {
        console.log('error', error);
        if (error.message.includes('timeout')) {
            message.error('请求超时');
        } else {
            message.error(error.message);
        }
    }
)
export default request