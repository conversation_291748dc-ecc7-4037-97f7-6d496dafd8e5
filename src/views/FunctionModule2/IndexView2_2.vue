<template>
    <div class="main_content2_index2-2">
        <div class="index_select index_select_left">
            <a-select
                v-model:value="state.year"
                placeholder="年份"
                :options="state.yearOptions"
                @change="changeYear"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="index_select">
            <a-select
                v-model:value="state.type"
                placeholder="电源类型"
                @change="changeType"
                :options="option"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="point_bg" >
            <div class="point_content left_content">
                <div class="select absolute">
                    <a-select
                        v-model:value="state.partition"
                        @change="changeMap"
                        :options="state.partitionOptions"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <div class="map" ref="map">
                    
                </div>
                <div v-if="state.partition=='全省'" class="map_info_move absolute" v-for="(item,index) in state.mapInfoList" :style="{left:(item.position[0])+'px',top:(item.position[1])+'px'}">
                    <img @click="item.show=!item.show" src="@/assets/images/index2/map_info_btn.png" alt="">
                    <div v-show="item.show"> 
                        <p>{{ item.name }}</p>
                        <p v-for="item in item.info">
                            <span>{{ item.name }}</span>
                            <span>{{ item.value }}</span>
                        </p>
                        <p>
                            <span>总计</span>
                            <span>{{ fixInteger(item.count) }}</span>
                        </p>
                    </div>
                </div>
                <div class="map_info_no_move absolute">
                    <div> 
                        <p>装机容量</p>
                        <p v-for="item in state.mapInfo">
                            <span>{{ item.name }}</span>
                            <span>{{ item.value }}</span>
                        </p>
                        <p>
                            <span>总计</span>
                            <span>{{ state.count }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content right_content">
                <p class="title">{{ state.type }}出力分析</p>
                <div class="type" v-if="state.type=='新能源'">
                    <p>{{state.type}}出力曲线</p>
                    <div class="select_type absolute">
                        <div class="switch_select">
                            <p @click="changeTypes(0)" :class="state.types==0?'active':''">时间模式</p>
                            <p @click="changeTypes(1)" :class="state.types==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-show="state.types==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            :options="state.caseList"
                            @change="changeCaseType"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                    </div>
                    <div class="echarts" ref="line">
                        
                    </div>
                    <p>{{state.type}}出力率分析</p>
                    <div class="select_content absolute">
                        <a-select
                            v-model:value="state.months"
                            mode="multiple"
                            :options="state.timeOption1"
                            :max-tag-count="1"
                            :placeholder="'月份选择'"
                        >
                        </a-select>
                        <a-select
                            v-model:value="state.hours"
                            mode="multiple"
                            :options="state.timeOption2"
                            :max-tag-count="1"
                            :placeholder="'时刻选择'"
                        >
                        </a-select>
                        <a-input-number v-if="state.type!='储能'" :bordered="false" :controls="false" addon-after="%" v-model:value="state.confidence"  :min="1" :max="100" />
                        <a-button type="primary" @click="changeTime">确定</a-button>
                        <div class="icon_list">
                            <img src="@/assets/images/index2/icon.png" alt="">
                            <p>{{ fixInteger(state.text,1) }}</p>
                            <span>万千瓦</span>
                        </div>
                    </div>
                    <div class="echarts" ref="bar">
                        
                    </div>
                </div>
                <div class="types" v-else>
                    <p>出力曲线图</p>
                    <div class="select_type absolute">
                        <div class="switch_select">
                            <p @click="changeTypes(0)" :class="state.types==0?'active':''">时间模式</p>
                            <p @click="changeTypes(1)" :class="state.types==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-show="state.types==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            :options="state.caseList"
                            @change="changeCaseType"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                    </div>
                    <div class="echarts" ref="echarts1">
                        
                    </div>
                    <p>出力率分布统计图</p>
                    <div class="select_content absolute">
                        <a-select
                            v-model:value="state.months"
                            mode="multiple"
                            :options="state.timeOption1"
                            :max-tag-count="1"
                            :placeholder="'月份选择'"
                        >
                        </a-select>
                        <a-select
                            v-model:value="state.hours"
                            mode="multiple"
                            :options="state.timeOption2"
                            :max-tag-count="1"
                            :placeholder="'时刻选择'"
                        >
                        </a-select>
                        <a-input-number v-if="state.type!='储能'" :bordered="false" :controls="false" addon-after="%" v-model:value="state.confidence"  :min="1" :max="100" />
                        <a-button type="primary" @click="changeTime">确定</a-button>
                        <div class="icon_list">
                            <img src="@/assets/images/index2/icon.png" alt="">
                            <p>{{ fixInteger(state.text,1) }}</p>
                            <span>万千瓦</span>
                        </div>
                    </div>
                    <div class="echarts" ref="echarts2">
                        
                    </div>
                    <p>利用小时统计图</p>
                    <div class="icon_list_absolute" v-if="state.type!='储能'">
                        <span>年总利用小时&nbsp;&nbsp;&nbsp;</span>
                        <p>{{ fixInteger(state.usehours1,1) }}</p>
                    </div>
                    <div class="icon_list_absolute" v-else>
                        <span>年总利用小时&nbsp;&nbsp;</span>
                        <span>(充电：</span>
                        <p>{{ fixInteger(state.usehours1,1) }}</p>
                        <span>放电：</span>
                        <p>{{ fixInteger(state.usehours2,1) }})</p>
                    </div>
                    <div class="echarts" ref="echarts3">

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject, nextTick, onMounted } from '@vue/runtime-core'
import { markRaw, reactive, toRefs,computed } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { getBarOption,getLineOption,getMapSeries} from '@/utils/indexMain2'
import { storeToRefs } from 'pinia'
import { registerMapCopy,fixInteger } from '@/utils/common'
import { echartsResize } from '@/config/setting.config'
import { partitionCenter } from '@/utils/constants'
import { getHourRate,getUsualMap,getEnergyOutput,getOutputDistribute,getStationData } from '@/api/index'
const lineConfig = [
    {
        name:'新能源',
        showArea:false,
        color:'rgb(118, 178, 117)',
        areaColor:'rgb(118, 178, 117)',
    },
    {
        name:'风电',
        showArea:true,
        color:'rgb(84, 178, 208)',
        areaColor:'rgb(84, 178, 208)',
    },
    {
        name:'光伏',
        showArea:true,
        color:'rgb(210, 179, 48)',
        areaColor:'rgb(210, 179, 48)',
    },
    {
        name:'储能',
        showArea:true,
        color:'rgb(117, 107, 197)',
        areaColor:'rgb(117, 107, 197)',
    },
]
const echarts = inject("ec");
const store = dataStore()
const { partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    caseType:undefined,
    text:undefined,
    types:0,
    type: '新能源',
    usehours1:0,
    usehours2:0,
    barData:{},
    barData2:{},
    partitionOptions,
    yearOptions,
    stationList: [],
    mapData:[],
    caseList:[],
    caseOptions,
    lineData:{},
    confidence:100,
    timeOption1:Array(12).fill('').map((item,index)=>({label:(index+1)+'月',value:index+1})),
    timeOption2:Array(24).fill('').map((item,index)=>({label:(index)+'时',value:index})),
    mapInfoList:[],
    mapInfo:[],
    count:0
})
const option = [
    { value: '新能源' ,label:'新能源'},
    { value: '风电' ,label:'风电'},
    { value: '光伏',label:'光伏' },
    { value: '储能',label:'储能' },
]
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
    line:undefined,
    lineChart:undefined,
    bar:undefined,
    barChart:undefined,
    echarts1:undefined,
    echarts2:undefined,
    echarts3:undefined,
    echarts1_chart:undefined,
    echarts2_chart:undefined,
    echarts3_chart:undefined,
})
const { map, mapChart, mapOption,line,lineChart,bar,barChart,echarts1,echarts1_chart,echarts2,echarts2_chart,echarts3,echarts3_chart} = toRefs(allEcharts)
const initMapInfo = ()=>{
    const position = {
        '豫西':[250,30],
        '豫北':[750,150],
        '豫南':[300,720],
        '豫中东':[750,520],
    }
    state.mapInfoList = state.mapData.filter(item=>['豫西','豫北','豫南','豫中东'].includes(item.name)).map(item1=>{
        return{
            name:item1.name,
            info:Object.keys(item1.values).map(item2=>{
                return{
                    name:item2,
                    value:item1.values[item2]
                }
            }),
            count:Object.keys(item1.values).map(item2=>{
                return{
                    name:item2,
                    value:item1.values[item2]
                }
            }).reduce((a,b)=>a+b.value,0),
            show:true,
            position:position[item1.name].map(item=>echartsResize(item))
        }
    })
}
const changeType = () =>{
    if(state.type=='储能'){
        state.confidence = undefined
    }
    nextTick(()=>{
        if(state.type=='新能源'){
            lineChart.value = markRaw(echarts.init(line.value))
            barChart.value = markRaw(echarts.init(bar.value))
        }else{
            echarts1_chart.value = markRaw(echarts.init(echarts1.value))
            echarts2_chart.value = markRaw(echarts.init(echarts2.value))
            echarts3_chart.value = markRaw(echarts.init(echarts3.value))
        }
        initAllData('type')
    })
}
const changeMap = async() =>{
    await registerMapCopy(state.partition)
    initAllData('map')
}
const changeYear = () =>{
    initAllData('year')
}
const changeCaseType = async() =>{
    store.showModal()
    const data = await  getEnergyOutput({
        area:state.partition,
        year:state.year,
        gen_type:state.type=='新能源'?undefined:[state.type],
        scene_date:state.caseType?state.caseList.find(item=>item.value==state.caseType).values:undefined,
    })
    state.lineData = data.data
    initLine()
    store.hiddenModal()
}
const changeTypes = async(val)=>{
    if(state.types!=val){
        state.types=val
        if(state.caseType==undefined) return
        state.caseType = undefined
        if(state.types==0){
            store.showModal()
            const data = await  getEnergyOutput({
                area:state.partition,
                year:state.year,
                gen_type:state.type=='新能源'?undefined:[state.type],
                scene_date:state.caseType?state.caseList.find(item=>item.value==state.caseType).values:undefined,
            })
            state.lineData = data.data
            initLine()
            store.hiddenModal()
        }
    }
}
const changeTime = async()=>{
    store.showModal()
    const data = await  getOutputDistribute({
        area:state.partition,
        gen_type:state.type,
        year:state.year,
        months:state.months,
        hours:state.hours,
        mode:1,
        confidence:state.confidence?state.confidence/100:undefined
    })
    state.barData = data.data.outputs
    state.text = data.data.max_value
    initBar()
    store.hiddenModal()
}
const initAllData = async (val) => {
    store.showModal()
    if(val){
        state.hours = undefined
        state.months = undefined
        state.types = 0
        state.caseType = undefined
    }
    const [data1,data2,data5,data3,data4] = await Promise.all([
        getEnergyOutput({
            area:state.partition,
            year:state.year,
            gen_type:state.type=='新能源'?undefined:[state.type]
        }),
        getOutputDistribute({
            area:state.partition,
            gen_type:state.type,
            year:state.year,
            mode:1
        }),
        state.type!='新能源' ? getHourRate({
            area:state.partition,
            year:state.year,
            gen_type:state.type
        }):undefined,
        val=='year'||!val||val=='map'?getUsualMap({
            year:state.year,
            mode:1
        }):undefined,
        (val=='year'||!val||val=='map')?getStationData({
            name:state.partition,
            mode: 1
        }):undefined,
    ])
    state.lineData = data1.data
    state.barData = data2.data.outputs
    state.text = data2.data.max_value
    if(!val){
        state.year = +state.lineData.time_range[0].split('-')[0]
    }
    if(!val||val=='year'||val=='map'){
        state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
            return {
                label:item.name,
                value:item.name,
                values:item.date_time,
            }
        })
        state.mapData = Object.keys(data3.data).map(item=>{
            return{
                name:item,
                values:data3.data[item]
            }
        })
        state.mapInfo = Object.keys(data3.data[state.partition]).map(item=>{
            return {
                name:item,
                value:data3.data[state.partition][item]
            }
        })
        state.count = state.mapInfo.reduce((a,b)=>a+b.value,0)
        state.stationList = data4.data.map(item=>{
            return Object.assign({...item},{
                value:[item.longitude,item.latitude,item.type]
            })
        })
        initMap()
        if(state.partition=='全省'){
            initMapInfo()
        }
    }
    initLine()
    initBar()
    if(state.type!='新能源'){
        state.barData2 = data5.data
        initBars()
    }
    store.hiddenModal()
}
const initLine = ()=>{
    const option = getLineOption(state.type=='新能源'?lineChart.value:echarts1_chart.value,{
        yAxisName:'万千瓦',
        data:state.type=='新能源'?lineConfig.map(item=>{
            return Object.assign({ ...item },{data:state.lineData[item.name]})
        }):lineConfig.filter(item=>item.name==state.type).map(item=>{
            return Object.assign({ ...item },{data:state.lineData[item.name]})
        }),
        xAxisData:state.lineData.time_range.map(item=>item.slice(5, 16)),
        unit:'万千瓦',
        showDataZoom:state.caseType?false:true
    })
    if(state.type=='新能源'){
        lineChart.value.setOption(option,true)
    }else{
        echarts1_chart.value.setOption(option,true)
    }
}
const initBar = ()=>{
    const option = getBarOption(
        state.type=='新能源'? barChart.value:echarts2_chart.value,
        {
            showLegend:false,
            showXAxisSymbol:true,
            showYAxisLine:true,
            yAxisName:'概率值',
            xAxisName:'出力率值',
            data:[
                {
                    name:'',
                    color:'#D8BFD8',
                    data:Object.values(state.barData),
                }
            ],
            xAxisData:Object.keys(state.barData),
            unit:''
        }
    )
    if(state.type=='新能源'){
        barChart.value.setOption(option)
    }else{
        echarts2_chart.value.setOption(option)
    }
}
const initBars = ()=>{
    if(state.type=='储能'){
        state.usehours1 = state.barData2.usinghour_charge.reduce((a,b)=>a+b,0)
        state.usehours2 = state.barData2.usinghour_discharge.reduce((a,b)=>a+b,0)
        const option = getBarOption(
            echarts3_chart.value,
            {
                showLegend:true,
                yAxisName:'h',
                showXAxisSymbol:true,
                showYAxisLine:true,
                xAxisName:'月',
                data:[
                    {
                        name:'充电小时',
                        data:state.barData2.usinghour_charge,
                        color:'#FFA500',
                    },
                    {
                        name:'放电小时',
                        data:state.barData2.usinghour_discharge,
                        color:'#ADD8E6',
                    },
                ],
                xAxisData:state.barTime,
                unit:'h',
            }
        )
        echarts3_chart.value.setOption(option,true)
    }else{
        state.usehours1 = state.barData2.usinghour.reduce((a,b)=>a+b,0)
        const option = getBarOption(
            echarts3_chart.value,
            {
                showLegend:false,
                yAxisName:'h',
                showXAxisSymbol:true,
                showYAxisLine:true,
                xAxisName:'月',
                data:[
                    {
                        name:'',
                        data:state.barData2.usinghour,
                        color:'#87CEEB',
                    }
                ],
                xAxisData:state.barData2['时间'],
                unit:'h'
            }
        )
        echarts3_chart.value.setOption(option,true)
    }
}
const initMap = (val) => {
    const option = getMapSeries(1,state.partition,state.mapData, state.stationList,['光伏','风电','储能'])
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();  
        mapOption.value.geo.zoom= _option.series[0].zoom
        mapOption.value.geo.center= _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click',async (params)=>{//点击地图
        
    })
}
onMounted(async() => {
    await registerMapCopy(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    lineChart.value = markRaw(echarts.init(line.value))
    barChart.value = markRaw(echarts.init(bar.value))
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index2-2{
        display: flex;
        justify-content: space-between;
        position: relative;
        &:deep(.ant-select){
            width: 150px;
        }
        .index_select{
            position: absolute;
            top: -45px;
            right: 570px;
            &:deep(.ant-select){
                width: 150px;
            }
        }
        .index_select_left{
            right: unset;
            left: 570px;
        }
        .left_content,.right_content{
            width: 915px;
            height: 100%;
        }
        .select_type{
            right: 20px;
            top: 20px;
            display: flex;
            .switch_select{
                position: absolute;
                right: 170px;
                p{
                    width: 80px;
                }
            }
        }
        .left_content{
            .select{
                top: 20px;
                left: 20px;
                z-index: 1;
            }
            .map{
                height: 100%;
                width: 100%;
            }
            .map_info_move,.map_info_no_move{
                >div{
                    height: 101px;
                }
            }
        }
        .right_content{
            >div{
                padding:10px 20px;
                position: relative;
                >p{
                    color: rgb(236, 253, 253);
                    font-family: 阿里巴巴普惠体 2.0;
                    font-size: 22px;
                    font-weight: 700;
                    line-height: 50px;
                    letter-spacing: 0px;
                }
                .select_content{
                    display: flex;
                    align-items: center;
                    z-index: 1;
                    &:deep(.ant-select-multiple){
                        width: 140px;
                        margin-right: 20px;
                        .ant-select-selection-item-content{
                            line-height: 20px;
                        }
                        .ant-select-selection-item{
                            display: flex;
                            align-items: center;
                        }
                    }
                    &:deep(.ant-input-number-group-wrapper){
                        margin-right: 20px;
                    }
                    &:deep(.ant-input-number){
                        width: 70px;
                        background: $AntBgColor;
                        input{
                            height:  30px;
                            font-size: 14px;
                            color: #fff;
                        }
                    }
                    &:deep(.ant-input-number-group-addon){
                        border: none;
                        background: $AntBgColor;
                        color: #fff;
                        font-size: 16px;
                        margin-right: 20px;
                    }
                    .icon_list{
                        margin-left: 20px;
                        display: flex;
                        align-items: center;
                        img{
                            width: 35px;
                            height: 27.32px;
                            margin-right: 10px;
                        }
                        line-height: 26px;
                        letter-spacing: 0px;
                        font-size: 18px;
                        font-family: 思源黑体;
                        p{
                            color: rgb(0, 212, 238);
                            font-weight: 500;
                            min-width: 70px;
                        }
                        span{
                            color: rgb(255, 255, 255);
                            font-weight: 400;
                        }
                    }
                    button{
                        height: 31px;
                        width: 70px;
                        font-size: 16px;
                        padding: 0;
                    }
                }
            }
            .type{
                .select{
                    right: 20px;
                }
                .select_content{
                    right: 45px;
                }
                >p{
                    text-align: center;
                }
                .echarts{
                    width: 100%;
                    height: 350px;
                }
            }
            .types{
                .select_content{
                    right: 20px;
                    top: 290px;
                }
                .icon_list_absolute{
                    position: absolute;
                    right: 20px;
                    top: 560px;
                    display: flex;
                    align-items: center;
                    img{
                        width: 35px;
                        height: 27.32px;
                        margin-right: 10px;
                    }
                    line-height: 26px;
                    letter-spacing: 0px;
                    font-size: 18px;
                    font-family: 思源黑体;
                    p{
                        color: rgb(0, 212, 238);
                        font-weight: 500;
                        min-width: 70px;
                    }
                    span{
                        color: rgb(255, 255, 255);
                        font-weight: 400;
                    }
                }
                .echarts{
                    width: 875px;
                    height: 220px;
                }
            }
        }
    }
</style>