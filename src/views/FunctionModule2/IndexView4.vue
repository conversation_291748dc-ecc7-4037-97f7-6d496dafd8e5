<template>
    <div class="main_content2_index4">
        <div class="point_bg" >
            <div class="point_content left_content">
                <div class="map" ref="map">

                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content right_content">
                <div class="time_select">
                    <a-range-picker :allowClear="false" :disabled-date="disabledDate" v-model:value="state.searchTime" :showNow="false" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
                    <a-button type="primary" @click="changeTime">确定</a-button>
                    <a-button type="primary" @click="state.modalShow=true">断面时刻数据</a-button>
                </div>
                <div class="select_content">
                    <div class="switch_select">
                        <p @click="changeType('line_inf')" :class="state.type=='line_inf'?'active':''">500kV断面</p>
                        <p @click="changeType('transformer_inf')" :class="state.type=='transformer_inf'?'active':''">500kV主变</p>
                        <p @click="changeType('zone220_inf')" :class="state.type=='zone220_inf'?'active':''">220kV断面</p>
                        <p @click="changeType('zone220_trafo')" :class="state.type=='zone220_trafo'?'active':''">220kV主变</p>
                    </div>
                    <a-select
                        v-model:value="state.zone"
                        :options="state.partitionOptions"
                        @change="changeMap"
                        placeholder="选择分区"
                        :allowClear="true"
                        v-if="state.type=='zone220_inf'"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <div class="table_content" v-if="state.type=='line_inf'||state.type=='zone220_inf'">
                   <div>
                        <p>断面名称</p>
                        <p>限额</p>
                        <p>重载时长</p>
                        <p>越限时长</p>
                        <p>最大功率(MW)</p>
                        <p>最大负载率(%)</p>
                        <p>最大负载率时刻</p>
                    </div>
                    <div class="scroll">
                        <div v-for="(item,index) in state.tableData[state.type]" :key="index" @click="goToDetail(item.inf_name)">
                            <p>{{ item.inf_name }}</p>
                            <p>{{ item.inf_limit }}</p>
                            <p>{{ item.heavy_hours }}</p>
                            <p>{{ item.overload_hours }}</p>
                            <p>{{ item.max_power_value }}</p>
                            <p>{{ fixInteger(100*item.max_power_rate) }}</p>
                            <p>{{ item.max_power_time }}</p>
                        </div>
                    </div>
                </div>
                <div class="table_content_sp" v-else>
                    <div>
                        <p>断面名称</p>
                        <p>变电站</p>
                        <p>限额</p>
                        <p>重载时长</p>
                        <p>越限时长</p>
                        <p>最大功率(MW)</p>
                        <p>最大负载率(%)</p>
                        <p>最大负载率时刻</p>
                    </div>
                    <div class="scroll">
                        <div v-for="(item,index) in state.tableData[state.type]" :key="index">
                            <p>{{ item.name }}</p>
                            <div>
                                <div v-for="(item1,index1) in item.data" :key="index1" @click="goToDetail(item1.inf_name)">
                                    <p>{{ item1.inf_name }}</p>
                                    <p>{{ item1.inf_limit }}</p>
                                    <p>{{ item1.heavy_hours }}</p>
                                    <p>{{ item1.overload_hours }}</p>
                                    <p>{{ item1.max_power_value }}</p>
                                    <p>{{ fixInteger(100*item1.max_power_rate) }}</p>
                                    <p>{{ item1.max_power_time }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <interface-modal :searchTime="state.searchTime" v-if="state.modalShow" @close="state.modalShow = false"></interface-modal>
</template>
<script setup>
import { inject, onMounted } from '@vue/runtime-core'
import { markRaw, reactive, toRefs,ref } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { registerMapData,fixInteger,getMapScaleByBbox,getPrevDays } from '@/utils/common'
import { getMapOption} from '@/utils/indexMain'
import { useRouter } from 'vue-router'
import { getInterfaceArea ,getInterfaceData ,getInterfaceMap} from '@/api/index'
import { echartsResize } from '@/config/setting.config';
const store = dataStore()
const router = useRouter()
const echarts = inject("ec");
const {  } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    modalShow:false,
    searchTime:undefined,
    partitionOptions: [],
    tableData:{},
    type:'line_inf',
    geoScale:undefined,
    zone:undefined,
    mapData:{},
})
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
})
const { map, mapChart, mapOption} = toRefs(allEcharts)
const disabledDate = ()=>{
    
}
const changeTime = ()=>{
    store.showModal()
    initAllData()
}
const changeMap = async(val) => {
    store.showModal()
    const geoData = await registerMapData(state.zone?state.zone.replace(/地区/,''):'全省')
    echarts.registerMap(state.zone?state.zone.replace(/地区/,''):'全省', geoData)
    state.geoScale = (0.75 / getMapScaleByBbox(geoData, map.value.clientWidth, map.value.clientHeight).scale / 1)
    initAllData()
}
const changeType = (val) => {
    if(state.type==val) return
    state.type = val
    if(state.zone!=undefined){
        store.showModal()
        initAllData()
    }
}
const goToDetail = (name) => {
    router.push({
        path: '/index/FunctionModule2-4-1',
        query: {
            type: state.type,
            name,
            stime: state.searchTime[0],
            etime: state.searchTime[1]
        }
    })
}
const initAllData = () => {
    Promise.all([
        getInterfaceData({
            "stime": state.searchTime[0]+' 00:00:00',
            "etime": state.searchTime[1]+' 23:00:00',
            "zone": state.type=='zone220_inf'?state.zone:undefined
        }),
        getInterfaceMap({
            area:state.zone? state.zone.replace(/地区/,'') : '全省',
            "start_date": state.searchTime[0],
            "end_date": state.searchTime[1],
        })
    ]).then(([res1,res2])=>{
        state.tableData = res1.data
        if(res1.data.zone220_trafo) state.tableData['zone220_trafo'] = Object.keys(res1.data['zone220_trafo']).map(item => {
            return {
                name: item,
                data: res1.data['zone220_trafo'][item]
            }
        })
        if(res1.data.transformer_inf) state.tableData['transformer_inf'] = Object.keys(res1.data['transformer_inf']).map(item => {
            return {
                name: item,
                data: res1.data['transformer_inf'][item]
            }
        })
        state.mapData = res2.data
        initMap()
        store.hiddenModal()
    }).catch(()=>{
        store.hiddenModal()
    })
}
const initMap = (val) => {
    const option = getMapOption({
        partition:state.zone?state.zone.replace(/地区/,'') : '全省',
        mapData:state.mapData,
        showArea:false,
        showFeedin:true,
        geoScale:state.geoScale,
        partitionData:[],
        visualMap:[
            {
                seriesIndex: [1],
                realtime: false,
                type:'piecewise',
                left:echartsResize(10),
                bottom:echartsResize(7),
                itemGap:echartsResize(10),
                itemHeight:echartsResize(14),
                itemWidth:echartsResize(20), 
                textGap:echartsResize(10),
                textStyle:{
                    color:'#fff',
                    fontSize:echartsResize(12),
                },
                pieces: [
                    {gt: 1,label: '100%以上', color: '#882C39'},            // (1500, Infinity]
                    {gt: 0.8, lte: 1,label: '80-100%', color: '#FF6464'},  // (900, 1500]
                    {gt: 0.5, lte: 0.8,label: '50-80%', color: '#FF9C40'},  // (310, 1000]
                    {gt: 0.3, lte: 0.5,label: '30-50%', color: '#FFD12D'},   // (200, 300]
                    {lt: 0.3,label: '0-30%', color: '#FFF5A5'}         
                ]
            }
        ]
    })
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();
        mapOption.value.geo.zoom = _option.series[0].zoom
        mapOption.value.geo.center = _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click',async (params)=>{//点击地图
        if(params.componentSubType=='map'){
            
        }
    })
}
onMounted(async() => {
    store.showModal()
    const geoData = await registerMapData(state.partition)
    echarts.registerMap(state.partition, geoData)
	state.geoScale = (0.75 / getMapScaleByBbox(geoData, map.value.clientWidth, map.value.clientHeight).scale / 1)
    mapChart.value = markRaw(echarts.init(map.value))
    const res = await getInterfaceArea({})
    state.partitionOptions = res.data.map(item => {
        return {
            label: item,
            value:item
        }
    })
    state.searchTime = getPrevDays(3)
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index4{
        position: relative;
        display: flex;
        justify-content: space-between;
        .point_bg,.point_content{
            height: 100%;
        }
        .point_content{
            padding: 20px;
        }
        .left_content{
            width: 800px;
            .map{
                height: 100%;
            }
        }
        .right_content{
            width: 1035px;
        }
        .time_select{
            display: flex;
            height: 32px;
            align-items: center;
            &:deep(.ant-select){
                width: 150px;
                margin-right: 20px;
            }
            .switch_select{
                margin-right: 20px;
            }
            button{
                margin-left: 20px;
                height: 32px;
                min-width: 70px;
                font-size: 16px;
                padding: 0 10px;
            }
        }
        .select_content{
            margin: 20px 0;
            display: flex;
            align-items: center;
            &:deep(.ant-select){
                width: 150px;
                margin-left: 20px;
            }
        }
        .table_content,.table_content_sp{
            border-radius: 10px;
            padding: 10px;
            border: 4px solid rgb(10, 64, 79);
        }
        .table_content{
            >div:first-child,>div:last-child>div{
                display: grid;
                grid-template-columns: 2fr 1fr 1fr 1fr 1.1fr 1fr 2fr;
                p{
                    text-align: center;
                }
            }
            >div:first-child{
                p{
                    font-size: 14px;
                    font-weight: bolder;
                    line-height: 40px;
                }
            }
            >div:last-child{
                height: 650px;
                >div{
                    border: 1px solid transparent;
                    border-radius: 5px;
                    p{
                        font-size: 12px;
                        line-height: 12px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    &:hover{
                        cursor: pointer;
                        border: 1px solid aqua;
                        p{
                            color: aqua;
                        }
                    }
                }
            }
        }
        .table_content_sp{
            >div:first-child{
                display: grid;
                grid-template-columns: 2fr 1.7fr 0.7fr 0.8fr 0.8fr 1fr 1fr 2fr;
                p{
                    text-align: center;
                    font-size: 14px;
                    font-weight: bolder;
                    line-height: 40px;
                }
            }
            .scroll{      
                height: 650px;
                p{
                    text-align: center;
                }
                >div{
                    display: grid;
                    grid-template-columns: 2fr 8fr;
                    border: 1px solid transparent;
                    border-radius: 5px;
                    &:hover{
                        cursor: pointer;
                        border: 1px solid aqua;
                        >p{
                            color: aqua;
                        }
                    }
                    >p{
                        padding: 5px;
                        border: 1px solid rgb(10, 64, 79);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    >div>div{
                        display: grid;
                        grid-template-columns: 1.7fr 0.7fr 0.8fr 0.8fr 1fr 1fr 2fr;
                        // border: 1px solid transparent;
                        p{
                            line-height: 32px;
                            border: 1px solid rgb(10, 64, 79);
                            border-left: none;
                        }
                        &:hover{
                            cursor: pointer;
                            border: 1px solid aqua;
                            >p{
                                color: aqua;
                            }
                        }
                    }
                }
            }
        }
    }
</style>