<template>
    <div class="main_content2_index5">
        <div class="left_content">
            <div class="select">
                <a-date-picker 
                    :show-time="{ format: 'HH' }"
                    format="YYYY-MM-DD HH" 
                    placeholder="时刻" @change="onChange" @ok="onOk" />
            </div>
            <div class="icon_bg active">夏季负荷最大</div>
            <div class="icon_bg">冬季负荷最大</div>
            <div class="icon_bg">全年负荷最小</div>
            <div class="icon_bg">新能源出力最大</div>
            <div class="icon_bg">新能源出力最小</div>
            <div class="icon_bg">旋备最大</div>
            <div class="icon_bg">旋备最小</div>
            <div class="icon_bg">外受电最大</div>
            <div class="icon_bg">外受电最小</div>
            <div class="icon_bg">外受电+新能源最大</div>
            <div class="icon_bg">外受电+新能源最小</div>
            <div class="icon_bg">弃风弃光最大</div>
        </div>
        <div class="point_bg" >
            <div class="point_content middle_content">
                <p class="title">电网潮流示意图</p>
                <div class="map" ref="map">
                    
                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content right_content">
                <p class="title">典型时刻潮流统计</p>
                <div class="select">
                    <a-select
                        v-model:value="state.partition"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <div class="table_underline">
                    <div>
                        <p>指标名称</p>
                        <p>指标数值</p>
                    </div>
                    <div>
                        <div>
                            <p>负荷</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>统调电厂出力</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>地方电厂出力</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>直流送电</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>新能源出力</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>风电出力</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>光伏出力</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>鄂豫断面</p>
                            <p>xxx</p>
                        </div>
                        <div>
                            <p>长南线</p>
                            <p>xxx</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject, onMounted } from '@vue/runtime-core'
import { markRaw, reactive, toRefs } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { getMapSeries} from '@/utils/indexMain2'
import { storeToRefs } from 'pinia'
import { registerMap } from '@/utils/common'
const echarts = inject("ec");
const store = dataStore()
const state = reactive({
    partition: '全省',
})
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
})
const { map, mapChart, mapOption,} = toRefs(allEcharts)
const initAllData = async () => {
    store.showModal()
    initMap()
    store.hiddenModal()
}
const initMap = () => {
    const option = getMapSeries(0,state.partition,[])
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();  
        mapOption.value.geo.zoom= _option.series[0].zoom
        mapOption.value.geo.center= _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapChart.value.setOption(mapOption.value)
    })
}
onMounted(async() => {
    await registerMap(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    // initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index5{
        display: flex;
        justify-content: space-between;
        .left_content,.middle_content,.right_content{
            height: 100%;
        }
        
        .left_content{
            padding: 10px;
            width: 320px;
            display: flex;
            flex-direction: column;
            align-items: center;
            &:deep(.ant-select){
                width: calc(42px + 192px);
            }
            >div:first-child{
                margin-bottom: 14.27px;
            }
            .icon_bg{
                background-image: url('@/assets/images/index2/bg.png');
                background-size: 100%;
                background-repeat: no-repeat;
                height: 45.67px;
                width: calc(42px + 192px);
                margin-bottom: 14.27px;
                color: rgb(175, 255, 250);
                font-family: 思源黑体;
                font-size: 21px;
                font-weight: 500;
                line-height: 45.67px;
                letter-spacing: 0px;
                text-align: center;
                padding-right: 42px;
                &:hover{
                    cursor: pointer;
                    background-image: url('@/assets/images/index2/bgs.png');
                }
            }
            .active{
                background-image: url('@/assets/images/index2/bgs.png');
            }
        }
        .middle_content{
            width: 940px;
            .map{
                height: calc(100% - 44px);
                width: 100%;
            }
        }
        .right_content{
            width: 520px;
            >div{
                padding: 20px;
                &:deep(.ant-select){
                    width: 150px;
                }
            }
            .table_underline{
                >div:first-child,>div:last-child>div{
                    grid-template-columns: 1fr 1fr;
                    p{
                        color: rgb(236, 253, 253);
                        font-family: 阿里巴巴普惠体 2.0;
                        font-size: 20px;
                        font-weight: 400;
                        line-height: 50px;
                        letter-spacing: 0px;
                    }
                }
                >div:last-child{
                    height: auto;
                }
            }
        }
    }
</style>