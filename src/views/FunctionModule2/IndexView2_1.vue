<template>
    <div class="main_content2_index2-1">
        <div class="index_select index_select_left">
            <a-select
                v-model:value="state.year"
                placeholder="年份"
                :options="state.yearOptions"
                @change="changeYear"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="index_select">
            <a-select
                v-model:value="state.type"
                placeholder="电源类型"
                @change="changeType"
                :options="option"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="point_bg" >
            <div class="point_content left_content">
                <div class="map_select absolute">
                    <a-select
                        v-model:value="state.partition"
                        placeholder="区域选择"
                        @change="changeMap"
                        :options="state.partitionOptions"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <div class="map" ref="map">
                    
                </div>
                <div v-if="state.partition=='全省'" class="map_info_move absolute" v-for="(item,index) in state.mapInfoList" :style="{left:(item.position[0])+'px',top:(item.position[1])+'px'}">
                    <img @click="item.show=!item.show" src="@/assets/images/index2/map_info_btn.png" alt="">
                    <div v-show="item.show"> 
                        <p>{{ item.name }}</p>
                        <p v-for="item in item.info">
                            <span>{{ item.name }}</span>
                            <span>{{ item.value }}</span>
                        </p>
                        <p>
                            <span>总计</span>
                            <span>{{ fixInteger(item.count) }}</span>
                        </p>
                    </div>
                </div>
                <div class="map_info_no_move absolute">
                    <div> 
                        <p>装机容量</p>
                        <p v-for="item in state.mapInfo">
                            <span>{{ item.name }}</span>
                            <span>{{ item.value }}</span>
                        </p>
                        <p>
                            <span>总计</span>
                            <span>{{ fixInteger(state.count) }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content right_content">
                <p class="title">{{ getTitle() }}</p>
                <div>
                    <div class="text_content" v-if="state.type=='全部'">
                        <p class="text">装机容量比</p>
                        <p class="text">发电量占比</p>
                    </div>
                    <p v-else class="text">出力曲线图</p>
                    <div class="echarts" ref="echarts1">

                    </div>
                    <p class="text">{{ ['全部'].includes(state.type)?'出力曲线图':'出力率分布统计图' }}</p>
                    <div :class="state.type=='全部'?'select_type select_type1 absolute':'select_type select_type3 absolute'">
                        <div class="switch_select">
                            <p @click="changeTypes(0)" :class="state.types==0?'active':''">时间模式</p>
                            <p @click="changeTypes(1)" :class="state.types==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-show="state.types==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            @change="changeCaseType"
                            :options="state.caseList"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                    </div>
                    <div class="echarts" ref="echarts2">

                    </div>
                    <p class="text">{{ state.type=='全部'?'出力率分布统计图': '利用小时统计图' }}</p>
                    <div class="icon_list_absolute" v-if="state.type=='抽蓄'">
                        <span>年总利用小时&nbsp;&nbsp;</span>
                        <span>(充电：</span>
                        <p>{{ fixInteger(state.usehours1,1) }}</p>
                        <span>放电：</span>
                        <p>{{ fixInteger(state.usehours2,1) }})</p>
                    </div>
                    <div :class="state.type=='全部'?'select_content select_content_type1 absolute':'select_content select_content_type2 absolute'">
                        <a-select
                            v-model:value="state.months"
                            mode="multiple"
                            :options="state.timeOption1"
                            :max-tag-count="1"
                            :placeholder="'月份选择'"
                        >
                        </a-select>
                        <a-select
                            v-model:value="state.hours"
                            mode="multiple"
                            :options="state.timeOption2"
                            :max-tag-count="1"
                            :placeholder="'时刻选择'"
                        >
                        </a-select>
                        <a-button type="primary" @click="changeTime">确定</a-button>
                        <div class="icon_list">
                            <img src="@/assets/images/index2/icon.png" alt="">
                            <p>{{ fixInteger(state.countPower,1) }}</p>
                            <span>万千瓦</span>
                        </div>
                    </div>
                    <div class="icon_list_absolute" v-if="!['全部','抽蓄'].includes(state.type)">
                        <span>年总利用小时&nbsp;&nbsp;&nbsp;</span>
                        <p>{{ fixInteger(state.countHours,1) }}</p>
                    </div>
                    <div class="echarts" ref="echarts3">

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject, onMounted ,nextTick} from '@vue/runtime-core'
import { markRaw, reactive, toRefs,computed,ref, } from '@vue/reactivity'
import { getPieSeries} from '@/utils/indexMain2_2'
import { getBarOption,getLineOption,getMapSeries} from '@/utils/indexMain2'
import { getLineSeries} from '@/utils/indexMain1'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { registerMapCopy,fixInteger,calculateCentroid } from '@/utils/common'
import { partitionCenter } from '@/utils/constants'
import { echartsResize } from '@/config/setting.config'
import { getOutputData,getOutputDistribute,getUsualMap,getCapacityData,getPowerData,getHourRate,getStationData } from '@/api/index'
const echarts = inject("ec");
const store = dataStore()
const { partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    type: '全部',
    year: undefined,
    countPower: undefined,
    countHours: 0,
    types: 0,
    yearOptions,
    partitionOptions,
    caseType1:undefined,
    caseType2:undefined,
    hours:undefined,
    months:undefined,
    caseType:undefined,
    caseList:[],
    stationList:[],
    caseOptions,
    lineData1:[],
    timeData1:[],
    barData1:{},
    barData2:[],
    barTime:[],
    mapData:[],
    pieData1:[],
    pieData2:[],
    usehours1:0,
    usehours2:0,
    timeOption1:Array(12).fill('').map((item,index)=>({label:(index+1)+'月',value:index+1})),
    timeOption2:Array(24).fill('').map((item,index)=>({label:(index)+'时',value:index})),
    mapInfoList:[],
    mapInfo:[],
    count:0,
})
const option = [
    { text: '常规电源装机', value: '全部' ,label:'全部'},
    { text: '煤电', value: '煤电',label:'煤电' },
    { text: '热电', value: '供热煤电' ,label:'热电'},
    // { text: '非供热煤电机组', value: '非供热煤电',label:'非供热煤电机组' },
    { text: '燃气机组', value: '燃气',label:'燃气' },
    { text: '水电机组', value:'水电',label:'水电'},
    { text: '抽蓄机组', value:'抽蓄',label:'抽蓄'},
]
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
    pieOption:{},
    lineOption:{},
    echarts1:undefined,
    echarts1_chart:undefined,
    echarts2:undefined,
    echarts2_chart:undefined,
    echarts3:undefined,
    echarts3_chart:undefined,
})
const { map, mapChart, mapOption,echarts1,echarts1_chart,echarts2,echarts2_chart,echarts3,echarts3_chart,pieOption,lineOption} = toRefs(allEcharts)
const getTitle = computed(() => () => {
    return option.find(item=>item.value==state.type).text+'统计'
})
const initMapInfo = ()=>{
    const position = {
        '豫西':[250,30],
        '豫北':[750,150],
        '豫南':[300,720],
        '豫中东':[750,520],
    }
    state.mapInfoList = state.mapData.filter(item=>['豫西','豫北','豫南','豫中东'].includes(item.name)).map(item1=>{
        return{
            name:item1.name,
            info:Object.keys(item1.values).map(item2=>{
                return{
                    name:item2,
                    value:item1.values[item2]
                }
            }),
            count:Object.keys(item1.values).map(item2=>{
                return{
                    name:item2,
                    value:item1.values[item2]
                }
            }).reduce((a,b)=>a+b.value,0),
            show:true,
            position:position[item1.name].map(item=>echartsResize(item))
        }
    })
}
const initAllData = async (val) => {
    store.showModal()
    if(val){
        state.hours = undefined
        state.months = undefined
        state.types = 0
        state.caseType = undefined
    }
    const [data1,data2,data3,data4] = await Promise.all([
        getOutputData({
            area:state.partition,
            gen_type:state.type,
            year:state.year,
        }),
        getOutputDistribute({
            area:state.partition,
            gen_type:state.type,
            year:state.year,
            months:state.months,
            hours:state.hours,
            mode:2
        }), 
        val=='year'||!val||val=='map'?getUsualMap({
            year:state.year,
            mode:2
        }):undefined,
        (val=='year'||!val||val=='map')?getStationData({
            name:state.partition,
            mode: 2
        }):undefined,
    ]).catch(()=>{
        store.hiddenModal()
    })
    if(state.type=='全部'){
        const [data1,data2] = await Promise.all([
            getCapacityData({
                area:state.partition,
                year:state.year,
            }),
            getPowerData({
                area:state.partition,
                year:state.year,
            }),
        ])
        state.pieData1 = [data1.data['供热煤电'],data1.data['供热煤电'],data1.data['燃气'],data1.data['水电'],data1.data['抽蓄']].filter(item=>item>0)
        state.pieData2 = [data2.data['供热煤电'],data2.data['供热煤电'],data2.data['燃气'],data2.data['水电'],data2.data['抽蓄']].filter(item=>item>0)
    }else{
        const data =await getHourRate({
            area:state.partition,
            year:state.year,
            gen_type:state.type
        })
        state.barData2 = data.data
        state.barTime = data.data['时间']
    }
    state.lineData1 = data1.data
    state.timeData1 = data1.data['时间']
    state.year = +state.timeData1[0].split('-')[0]
    if(!val||val=='year'||val=='map'){
        state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
            return {
                label:item.name,
                value:item.name,
                values:item.date_time,
            }
        })
        state.mapData = Object.keys(data3.data).map(item=>{
            return{
                name:item,
                values:data3.data[item]
            }
        })
        state.mapInfo = Object.keys(data3.data[state.partition]).map(item=>{
            return {
                name:item,
                value:data3.data[state.partition][item]
            }
        })
        state.count = state.mapInfo.reduce((a,b)=>a+b.value,0)
        state.stationList = data4.data.map(item=>{
            return Object.assign({...item},{
                value:[item.longitude,item.latitude,item.type]
            })
        })
        initMap()
        if(state.partition=='全省'){
           initMapInfo()
        }
    }
    state.barData1 = data2.data.outputs
    state.countPower = data2.data.max_value
    initAllEcharts()
    store.hiddenModal()
}
const changeType = ()=>{
    initAllData('type')
}
const changeTypes = async(val)=>{
    if(state.types!=val){
        state.types=val
        if(state.caseType==undefined) return
        state.caseType = undefined
        if(state.types==0){
            store.showModal()
            const data = await getOutputData({
                area:state.partition,
                gen_type:state.type,
                scene_date:state.caseType? state.caseList.find(item=>item.value==state.caseType).values:undefined,
                year:state.year,
            })
            state.lineData1 = data.data
            state.timeData1 = data.data['时间']
            initEchart2()
            store.hiddenModal()
        }
    }
}
const changeCaseType = async()=>{
    store.showModal()
    const data = await getOutputData({
        area:state.partition,
        gen_type:state.type,
        scene_date:state.caseType? state.caseList.find(item=>item.value==state.caseType).values:undefined,
        year:state.year,
    })
    state.lineData1 = data.data
    state.timeData1 = data.data['时间']
    initEchart2()
    store.hiddenModal()
}
const changeTime = async()=>{
    store.showModal()
    const data = await  getOutputDistribute({
        area:state.partition,
        gen_type:state.type,
        year:state.year,
        months:state.months,
        hours:state.hours,
        mode:2
    })
    state.barData1 = data.data.outputs
    state.countPower = data.data.max_value
    initEchart3()
    store.hiddenModal()
}
const changeMap = async()=>{
    await registerMapCopy(state.partition)
    initAllData('map')
}
const changeYear = ()=>{
    initAllData('year')
}
const initEchart1 = ()=>{
    if(state.type=='全部'){
        pieOption.value = getPieSeries(echarts1_chart.value,state.pieData1,state.pieData2,{
            gen_coal:'',
            gen_heat:'',
            gen_gas:'',
            gen_hydro:'',
            stogen_pump_hydro:'',
        })
        echarts1_chart.value.setOption(pieOption.value,true)
        echarts1_chart.value.off('legendselectchanged')
        echarts1_chart.value.on('legendselectchanged', function(event) {
            lineOption.value.legend.selected = event.selected
            echarts2_chart.value.setOption(lineOption.value,true)
        })
    }else if(['抽蓄'].includes(state.type)){
        state.usehours1 = state.barData2.usinghour_charge.reduce((a,b)=>a+b,0)
        state.usehours2 = state.barData2.usinghour_discharge.reduce((a,b)=>a+b,0)
        const option = getBarOption(
            echarts3_chart.value,
            {
                showLegend:true,
                yAxisName:'h',
                data:[
                    {
                        name:'充电小时',
                        data:state.barData2.usinghour_charge,
                        color:'#FFA500',
                    },
                    {
                        name:'放电小时',
                        data:state.barData2.usinghour_discharge,
                        color:'#ADD8E6',
                    },
                ],
                xAxisData:state.barTime,
                unit:'h',
            }
        )
        echarts3_chart.value.setOption(option,true)
    }else{
        state.countHours =  state.barData2.usinghour.reduce((a,b)=>a+b,0)
        const option = getBarOption(
            echarts3_chart.value,
            {
                
                showLegend:false,
                yAxisName:'h',
                showXAxisSymbol:true,
                showYAxisLine:true,
                xAxisName:'月',
                data:[
                    {
                        name:'',
                        data:state.barData2.usinghour,
                        color:'#87CEEB',
                    }
                ],
                xAxisData:state.barTime,
                unit:'h'
            }
        )
        echarts3_chart.value.setOption(option,true)
    }
}
const initEchart2 = ()=>{
    let lineData
    if(state.type=='全部'){
        const { solar, wind, ...rest } = state.lineData1;
        lineData = rest;
    }
    lineOption.value = getLineSeries(echarts2_chart.value,state.type=='全部'? lineData:state.lineData1,state.timeData1,state.caseType?false:true,0,5,true)
    if(state.type=='全部'){
        echarts2_chart.value.setOption(lineOption.value,true)
        echarts2_chart.value.off('legendselectchanged')
        echarts2_chart.value.on('legendselectchanged', function(event) {
            pieOption.value.legend.selected = event.selected
            echarts1_chart.value.setOption(pieOption.value,true)
        })
    }else{
        echarts1_chart.value.setOption(lineOption.value,true)
    }
}
const initEchart3 = ()=>{
    const option = getBarOption(
        state.type=='全部'?echarts3_chart.value:echarts2_chart.value,
        {
            showLegend:false,
            showXAxisSymbol:true,
            showYAxisLine:true,
            yAxisName:'概率值',
            xAxisName:'出力率值',
            data:[
                {
                    name:'',
                    color:'#D8BFD8',
                    data:Object.values(state.barData1),
                }
            ],
            xAxisData:Object.keys(state.barData1),
            unit:''
        }
    )
    if(state.type=='全部'){
        echarts3_chart.value.setOption(option,true)
    }else{
        echarts2_chart.value.setOption(option,true)
    }
}
const initAllEcharts = () => { 
    initEchart1()
    initEchart2()
    initEchart3()
}
const initMap = (val) => {
    const option = getMapSeries(0,state.partition,state.mapData,state.stationList)
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();  
        mapOption.value.geo.zoom= _option.series[0].zoom
        mapOption.value.geo.center= _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click',async (params)=>{//点击地图
        // if(params.componentSubType=='map'){
        //     if(params.name==state.partition){
        //         state.partition = '全省'
        //     } else {
        //         if(!state.partitionOptions.find(item=>item.label==params.name)) return
        //         state.partition = params.name
        //     }
        //     await registerMapCopy(state.partition)
        //     initAllData('map')
        // }
    })
}
onMounted(async() => {
    await registerMapCopy('全省')
    echarts1_chart.value = markRaw(echarts.init(echarts1.value))
    echarts2_chart.value = markRaw(echarts.init(echarts2.value))
    echarts3_chart.value = markRaw(echarts.init(echarts3.value))
    mapChart.value = markRaw(echarts.init(map.value))
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index2-1{
        display: flex;
        justify-content: space-between;
        position: relative;
        &:deep(.ant-select){
            width: 150px;
        }
        .index_select{
            position: absolute;
            top: -45px;
            right: 570px;
            &:deep(.ant-select){
                width: 150px;
            }
        }
        .index_select_left{
            right: unset;
            left: 570px;
        }
        .left_content,.right_content{
            width: 915px;
            height: 100%;
            position: relative;
        }
        .left_content{
            overflow: hidden;
            .map_select{
                left: 20px;
                top: 20px;
                z-index: 2;
            }
            .map{
                height: 100%;
                width: 100%;
            }
        }
        .right_content{
            .text{
                color: rgb(236, 253, 253);
                font-family: 阿里巴巴普惠体 2.0;
                font-size: 22px;
                font-weight: 700;
                line-height: 50px;
                letter-spacing: 0px;
            }
            .icon_list_absolute{
                position: absolute;
                right: 20px;
                top: 560px;
                display: flex;
                align-items: center;
                img{
                    width: 35px;
                    height: 27.32px;
                    margin-right: 10px;
                }
                line-height: 26px;
                letter-spacing: 0px;
                font-size: 18px;
                font-family: 思源黑体;
                p{
                    color: rgb(0, 212, 238);
                    font-weight: 500;
                    min-width: 70px;
                }
                span{
                    color: rgb(255, 255, 255);
                    font-weight: 400;
                }
            }
            .select_content{
                display: flex;
                align-items: center;
                &:deep(.ant-select-multiple){
                    width: 140px;
                    margin-right: 20px;
                    .ant-select-selection-item-content{
                        line-height: 20px;
                    }
                    .ant-select-selection-item{
                        display: flex;
                        align-items: center;
                    }
                }
                .icon_list{
                    margin-left: 20px;
                    display: flex;
                    align-items: center;
                    img{
                        width: 35px;
                        height: 27.32px;
                        margin-right: 10px;
                    }
                    line-height: 26px;
                    letter-spacing: 0px;
                    font-size: 18px;
                    font-family: 思源黑体;
                    p{
                        color: rgb(0, 212, 238);
                        font-weight: 500;
                        min-width: 60px;
                    }
                    span{
                        color: rgb(255, 255, 255);
                        font-weight: 400;
                    }
                }
                button{
                    height: 31px;
                    width: 70px;
                    font-size: 16px;
                    padding: 0;
                }
            }
            >div{
                padding:0 20px;
                position: relative;
                .text_content{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    p{
                        text-align: center;
                    }
                }
                .select_type{
                    display: flex;
                    .switch_select{
                        position: absolute;
                        right: 170px;
                        p{
                            width: 80px;
                        }
                    }
                }
                .select_type1{
                    right: 20px;
                    top: 282px;
                }
                .select_type2{
                    right: 20px;
                    top: 552px;
                }
                .select_type3{
                    right: 20px;
                    top: 12px;
                }
                .select_content_type1{
                    right: 20px;
                    top: 552px;
                }
                .select_content_type2{
                    right: 20px;
                    top: 282px;
                }
                .echarts{
                    width: 875px;
                    height: 220px;
                }
            }
        }
    }
</style>