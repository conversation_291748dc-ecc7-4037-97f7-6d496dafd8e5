<template>
    <div class="main_content2_index3">
        <div class="index_select index_select_left">
            <a-select
                v-model:value="state.year"
                placeholder="年份"
                :options="state.yearOptions"
                @change="changeYear"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="index_select">
            <a-select
                v-model:value="state.type"
                placeholder="默认曲线"
                :options="state.typeOption"
                @change="changeType"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="point_bg" >
            <div class="point_content left_content">
                <p class="title">直流送电曲线</p>
                <div>
                    <p>全年送电曲线</p>
                    <div class="switch_select absolute_left" v-if="state.type!=='受入总加'">
                        <p @click="changeType1(0)" :class="state.type1==0?'active':''">时序模式</p>
                        <p @click="changeType1(1)" :class="state.type1==1?'active':''">日最大值模式</p>
                    </div>
                    <div class="select_content absolute_left" v-else>
                        <div class="switch_select">
                            <p @click="changeFeedInType('dc')" :class="state.feedInType=='dc'?'active':''">区外直流</p>
                            <p @click="changeFeedInType('ac')" :class="state.feedInType=='ac'?'active':''">区外交流</p>
                        </div>
                        <a-range-picker :allowClear="false" :disabled-date="disabledDate" v-model:value="state.searchTime" :valueFormat="'YYYY-MM-DD'" />
                        <a-button type="primary" @click="changeTime">确定</a-button>
                    </div>
                    <div class="line" ref="line1">
                        
                    </div>
                    <div class="select_content">
                        <div class="switch_select">
                            <p @click="changeType2(0)" :class="state.type2==0?'active':''">时间模式</p>
                            <p @click="changeType2(1)" :class="state.type2==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-if="state.type2==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            :options="state.caseList"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                        <a-date-picker 
                            v-if="state.type2==0"
                            v-model:value="state.date"
                            placeholder="日期"
                            :valueFormat="'YYYY-MM-DD'"
                            :disabled-date="disabledDate"
                         />
                        <a-button type="primary" @click="changeTime1">确定</a-button>
                    </div>
                    <div class="select select2">
                        
                    </div>
                    <p>日送电曲线</p>
                    <div class="line" ref="line2">
                        
                    </div>
                </div>
            </div>
        </div>
        <div class="right_content">
            <div class="point_bg" >
                <div class="point_content right_top_content">
                    <p class="title">直流输送电量统计</p>
                    <div>
                        <div class="select">
                            <a-select
                                v-model:value="state.timeType1"
                                placeholder="时间"
                                :options="[
                                    {
                                        label:'季度',
                                        value:'S'
                                    },
                                    {
                                        label:'月度',
                                        value:'M'
                                    },
                                    {
                                        label:'小时',
                                        value:'H'
                                    },
                                ]"
                                @change="changeTimeType1"
                            >
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <a-select
                                v-if="state.timeType1=='M'"
                                v-model:value="state.timeType2"
                                placeholder="选择月份"
                                :options="state.timeOption1"
                                @change="changeTimeType2"
                                :disabled="state.timeType1=='S'"
                            >
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <a-select
                                v-else
                                v-model:value="state.timeType2"
                                placeholder="选择月份"
                                :options="state.timeOption1"
                                @change="changeTimeType2"
                                :max-tag-count="1"
                                mode="multiple"
                                :disabled="state.timeType1=='S'"
                            >
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <a-select
                                v-model:value="state.timeType3"
                                placeholder="选择日期"
                                :options="state.timeOption3"
                                allowClear
                                :disabled="state.timeType1!='M'||!state.timeType2"
                            >
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <!-- <a-date-picker 
                                v-model:value="state.timeType3"
                                placeholder="选择日期"
                                allowClear
                                :valueFormat="'DD'"
                                :disabled="state.timeType1!='M'||!state.timeType2"
                                :disabled-date="disabledDates"
                            /> -->
                            <a-button type="primary" @click="changeTime2">确定</a-button>
                            <p>总输送电量统计值：<span>{{fixInteger(state.count,0)}}</span>亿千瓦时 </p>
                        </div>
                        <div class="line" ref="bar1">
                        
                        </div>
                    </div>
                </div>
            </div>
            <div class="point_bg" >
                <div class="point_content right_bottom_content">
                    <p class="title">受入功率分布特性</p>
                    <div>
                        <div class="select_content absolute">
                            <a-select
                                v-model:value="state.months"
                                mode="multiple"
                                :options="state.timeOption1"
                                :max-tag-count="1"
                                :placeholder="'月份选择'"
                            >
                            </a-select>
                            <a-select
                                v-model:value="state.hours"
                                mode="multiple"
                                :options="state.timeOption2"
                                :max-tag-count="1"
                                :placeholder="'时刻选择'"
                            >
                            </a-select>
                            <a-button type="primary" @click="changeTime3">确定</a-button>
                            <div class="icon_list">
                                <img src="@/assets/images/index2/icon.png" alt="">
                                <p>{{ fixInteger(state.text,1) }}</p>
                                <span>万千瓦</span>
                            </div>
                        </div>
                        <div class="line" ref="bar2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject, onMounted } from '@vue/runtime-core'
import { computed, markRaw, reactive, toRefs } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { getBarOption,getLineOption} from '@/utils/indexMain2'
import { getLineSeries} from '@/utils/indexMain2_3'
import { fixInteger,exportExcelTableSimple,sortByKey} from '@/utils/common'
import { storeToRefs } from 'pinia'
import { getCurveList,getPowerYear,getPowerDay,getAcdcPowerData,getAcdcPowerDistribute,getHistoryFeedinData } from '@/api/index'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
const echarts = inject("ec");
const store = dataStore()
const { partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    type:'受入总加',
    feedInType:'dc',
    count:0,
    type2:0,
    type1:0,
    caseType:undefined,
    text:undefined,
    timeType1:'M',
    timeType2:undefined,
    timeType3:undefined,
    year:undefined,
    date:undefined,
    lineData1:{},
    lineData2:{},
    barData1:{},
    barData2:{},
    typeOption: [],
    yearOptions,
    caseList:[],
    caseOptions,
    hours:undefined,
    months:undefined,
    searchTime:undefined,
    timeOption1:Array(12).fill('').map((item,index)=>({label:(index+1)+'月',value:index+1})),
    timeOption2:Array(24).fill('').map((item,index)=>({label:(index)+'时',value:index})),
    timeOption3:[]
})
const allEcharts = reactive({
    line1:undefined,
    lineChart1:undefined,
    line2:undefined,
    lineChart2:undefined,
    bar1:undefined,
    barChart1:undefined,
    bar2:undefined,
    barChart2:undefined,
})
const { line1, lineChart1, line2, lineChart2, bar1, barChart1, bar2, barChart2 } = toRefs(allEcharts)
const initAllData = async (val) => {
    store.showModal()
    if(val){
        // state.hours = undefined
        // state.months = undefined
        // state.date = undefined
        state.caseType = undefined
        state.type2 = 0
    }else{
        const [data1] = await Promise.all([
            getCurveList(),
        ])
        if(data1.code==200){
            state.typeOption = data1.data.map(item=>{
                return {
                    label:item,
                    value:item
                }
            })
        }
    }
    const [data1,data2,data3,data4] = await Promise.all([
        getPowerYear({
            name:state.type,
            year:state.year,
            mode:state.type1
        }),
        getPowerDay({
            name:state.type,
            date:state.date==undefined&&val?state.year+'-01-01':state.date,
        }),
        getAcdcPowerData({
            name:state.type,
            year:state.year,
            freq:state.timeType1
        }),
        getAcdcPowerDistribute({
            name:state.type,
            year:state.year,
            months:state.months,
            hours:state.hours,
        })
    ]).catch(()=>{
        store.hiddenModal()
    })
    state.lineData1 = data1.data
    state.lineData2 = data2.data
    state.barData1 = data3.data
    state.barData2 = data4.data.outputs
    state.text = data4.data.max_value
    if(!val){
        state.year = +state.lineData1.time_range[0].split('-')[0]
        state.type = state.lineData1.curve_name
    }
    if(!val||val=='year'){
        state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
            return {
                label:item.name,
                value:item.name,
                values:item.date_time,
            }
        })
    }
    if(state.type=='受入总加'){
        const res = await getHistoryFeedinData(Object.assign({},
            val ? { 
                stime: state.year + '-01-01 00:00:00',
                etime:state.year+'-12-31 00:00:00'
            }:{

            }
        ))
        state.lineData1 = res.data
        state.searchTime = [res.data.time_range[0],res.data.time_range[res.data.time_range.length-1]]
    }
    initAllEcharts()
    store.hiddenModal()
}
const changeTime = async () => {
    store.showModal()
    const res = await getHistoryFeedinData({ 
        stime: state.searchTime[0] + ' 00:00:00',
        etime:state.searchTime[1]+' 00:00:00'
    })
    store.hiddenModal()
    state.lineData1 = res.data
    initLine1()
}
const disabledDate = (current)=>{
    const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year;
}
const disabledDates = (current)=>{
    const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year||date.month() !== state.timeType2-1;
}
const initAllEcharts = () =>{
    initLine1()
    initLine2()
    initBar1()
    initBar2()
}
const changeFeedInType = (val)=>{
    if(state.feedInType==val) return
    state.feedInType = val
    initLine1()
}
const initLine1 = () => {
    if (state.type === '受入总加') {
        const color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
        const option = getLineOption(lineChart1.value,{
            yAxisName:'功率(万千瓦)',
            data:sortByKey(Object.keys(state.lineData1[state.feedInType]).map((item,index)=>{
                return {
                    name:item,
                    data:state.lineData1[state.feedInType][item],
                    color:color[index],
                    areaColor:color[index],
                    showArea:true,
                    stack:true
                }
            }),['天中直流','青豫本侧','灵宝直流'],'name'),
            xAxisData:state.lineData1.time_range.map(item=>item.slice(5, 16)),
            unit:'万千瓦',
            showDataZoom:true,
            showSum:true,
        })
        lineChart1.value.setOption(option,true)
    }else{
        const option = getLineOption(lineChart1.value,{
            yAxisName:'万千瓦',
            data:[
                {
                    name:state.type,
                    color:'rgb(4, 190, 193)',
                    areaColor:'rgb(4, 190, 193)',
                    data:state.lineData1.value,
                    showArea:true
                }
            ],
            xAxisData:state.lineData1.time_range.map(item=>item.slice(5, 16)),
            unit:'万千瓦',
            showDataZoom:state.type1==0? true :false
        })
        lineChart1.value.setOption(option,true)
    }
}
const initLine2 = () => {
    const option = getLineSeries(lineChart2.value,state.lineData2,state.type)
    lineChart2.value.setOption(option)
}
const initBar1 = () => {
    const obj = {
        'spring':'春(3-5)',
        'summer':'夏(6-8)',
        'autumn':'秋(9-11)',
        'winter':'冬(12-2)',
    }
    state.count = state.barData1.value.reduce((pre,cur)=>{
        return pre+cur
    },0)
    const option = getBarOption(
        barChart1.value,
        {
            
            showLegend:true,
            showXAxisSymbol:true,
            showYAxisLine:true,
            yAxisName:'亿千瓦时',
            xAxisName:state.timeType1=="M"?state.timeType3?'小时':state.timeType2?'日':'月':state.timeType1=="H"?'小时':'季度',
            data:[
                {
                    name:'输送统计值',
                    color:'#87CEFA',
                    data:state.barData1.value,
                }
            ],
            xAxisData:state.timeType1=='S'?state.barData1.time_range.map(item=>obj[item]): state.barData1.time_range,
            unit:'亿千瓦时'
        }
    )
    barChart1.value.setOption(option)
}
const initBar2 = () =>{
    const option = getBarOption(
        barChart2.value,
        {
            
            showLegend:false,
            showXAxisSymbol:true,
            showYAxisLine:true,
            yAxisName:'概率值',
            xAxisName:'出力率值',
            data:[
                {
                    name:'',
                    color:'#D8BFD8',
                    data:Object.values(state.barData2),
                }
            ],
            xAxisData:Object.keys(state.barData2),
            unit:''
        }
    )
    barChart2.value.setOption(option)
}
const changeTime1 = () => {
    if(state.type2==1&&state.caseType==undefined){
        message.warning('请选择典型场景')
        return
    }
    store.showModal()
    getPowerDay(Object.assign({
        name:state.type,
    },state.type2==0 ? {
        date:state.date,
    }:{
        scene_date:state.caseList.find(item=>item.value==state.caseType).values,
    })).then(res=>{
        state.lineData2 = res.data
        initLine2()
        store.hiddenModal()
    })
}
const changeType = ()=>{
    initAllData('type')
}
const changeTime2 = ()=>{
    store.showModal()
    getAcdcPowerData(Object.assign({
        name:state.type,
        year:state.year,
        freq:state.timeType1,
    },state.timeType1=='M'?{
        month:state.timeType2,
        day:state.timeType3
    }:state.timeType1=='H'?{
        months:state.timeType2,
    }:{

    })).then(res=>{
        state.barData1 = res.data
        initBar1()
        store.hiddenModal()
    })
}
const changeTimeType1 = ()=>{
    state.timeType2 = undefined
    state.timeType3 = undefined
}
const changeTimeType2 = ()=>{
    state.timeType3 = undefined
    if([1,3,5,7,8,10,12].includes(state.timeType2)){
        state.timeOption3 = Array(31).fill('').map((item,index)=>({label:(index+1)+'日',value:index+1}))
    }else if([4,6,9,11].includes(state.timeType2)){
        state.timeOption3 = Array(30).fill('').map((item,index)=>({label:(index+1)+'日',value:index+1}))
    }else if(state.year % 100 != 0 &&state.year % 400){
        state.timeOption3 = Array(29).fill('').map((item,index)=>({label:(index+1)+'日',value:index+1}))
    }else{
        state.timeOption3 = Array(28).fill('').map((item,index)=>({label:(index+1)+'日',value:index+1}))
    }
}
const changeYear = () =>{
    initAllData('year')
}
const changeType1 = (val)=>{
    store.showModal()
    if(state.type1!=val){
        state.type1=val
        getPowerYear({
            name:state.type,
            year:state.year,
            mode:state.type1
        }).then(res=>{
            state.lineData1 = res.data
            initLine1()
            store.hiddenModal()
        })
    }
}
const changeType2 = (val)=>{
    if(state.type2!=val){
        state.type2=val
    }
}

const changeTime3 = async()=>{
    store.showModal()
    const data = await  getAcdcPowerDistribute({
        name:state.type,
        year:state.year,
        months:state.months,
        hours:state.hours,
    })
    state.barData2 =data.data.outputs
    state.text = data.data.max_value
    initBar2()
    store.hiddenModal()
}
onMounted(async() => {
    lineChart1.value = markRaw(echarts.init(line1.value))
    lineChart2.value = markRaw(echarts.init(line2.value))
    barChart1.value = markRaw(echarts.init(bar1.value))
    barChart2.value = markRaw(echarts.init(bar2.value))
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index3{
        display: flex;
        justify-content: space-between;
        position: relative;
        .index_select{
            position: absolute;
            top: -45px;
            right: 570px;
            &:deep(.ant-select){
                width: 150px;
            }
        }
        .index_select_left{
            right: unset;
            left: 570px;
        }
        &:deep(.ant-select){
            width: 150px;
        }
        .left_content,.right_content{
            width: 915px;
            position: relative;
            height: 100%;
            .select{
                position: absolute;
            }
        }
        .left_content{
            >div{
                padding: 10px 20px;
                >p{
                    color: rgb(236, 253, 253);
                    font-family: 阿里巴巴普惠体 2.0;
                    font-size: 22px;
                    font-weight: 700;
                    line-height: 50px;
                    letter-spacing: 0px;
                    text-align: center;
                }
            }
            .time_select{
                position: absolute;
                left: 150px;
                top: 0;
            }
            .download_btn{
                position: absolute;
                right: 10px;
                top: 105px;
                height: 32px;
                width: 70px;
                font-size: 16px;
                padding: 0;
            }
            .select_content{
                display: flex;
                height: 32px;
                align-items: center;
                &:deep(.ant-select){
                    width: 150px;
                    margin-right: 20px;
                }
                .switch_select{
                    margin-right: 20px;
                }
                button{
                    margin-left: 20px;
                    height: 32px;
                    width: 70px;
                    font-size: 16px;
                    padding: 0;
                }
            }
           
            .line{
                width: 100%;
                height: 325px;
            }
        }
        .right_content{
            .right_top_content{
                .select{
                    display: flex;
                    align-items: center;
                    top: 50px;
                    >div{
                        margin-right: 20px;
                    }
                    &:deep(){
                        .ant-select-disabled .ant-select-selector{
                            background-color: #f5f5f5;
                            .ant-select-selection-placeholder{
                                color: #000;
                            }
                        }
                        .ant-select-multiple{
                            .ant-select-selection-item-content{
                                line-height: 20px;
                            }
                            .ant-select-selection-item{
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                    >p{
                        line-height: 26px;
                        letter-spacing: 0px;
                        font-size: 16px;
                        font-family: 思源黑体;
                        margin-left: 10px!important;
                        color: rgb(255, 255, 255);
                        font-weight: 400;
                        span{
                            color: rgb(0, 212, 238);
                            font-weight: 500;
                            display: inline-block;
                            min-width: 40px;
                        }
                    }
                    button{
                        height: 31px;
                        width: 70px;
                        font-size: 16px;
                        padding: 0;
                    }           
                }
                >div{
                    padding:40px 20px 10px;
                    .line{
                        height: 333px;
                        width: 100%;
                    }
                }
                
            }
            >div:first-child{
                margin-bottom: 10px;
            }
            .right_bottom_content{
                .select_content{
                    top: 50px;
                    display: flex;
                    align-items: center;
                    &:deep(.ant-select-multiple){
                        width: 140px;
                        margin-right: 20px;
                        .ant-select-selection-item-content{
                            line-height: 20px;
                        }
                        .ant-select-selection-item{
                            display: flex;
                            align-items: center;
                        }
                    }
                    button{
                        height: 31px;
                        width: 70px;
                        font-size: 16px;
                        padding: 0;
                    }
                    .icon_list{
                        margin-left: 20px;
                        display: flex;
                        align-items: center;
                        img{
                            width: 35px;
                            height: 27.32px;
                            margin-right: 10px;
                        }
                        line-height: 26px;
                        letter-spacing: 0px;
                        font-size: 18px;
                        font-family: 思源黑体;
                        p{
                            color: rgb(0, 212, 238);
                            font-weight: 500;
                            min-width: 60px;
                        }
                        span{
                            color: rgb(255, 255, 255);
                            font-weight: 400;
                        }
                    }
                }
                >div{
                    padding:40px 20px 10px;
                    .line{
                        height: 333px;
                        width: 100%;
                    }
                }
            }
        }
    }
</style>