<template>
    <div class="main_content2_index1">
        <div class="index_select index_select_left">
            <a-select
                v-model:value="state.year"
                placeholder="年份"
                :options="state.yearOptions"
                @change="changeYear"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="index_select">
            <a-select
                v-model:value="state.partition"
                placeholder="区域选择"
                :options="state.partitionOptions"
                @change="changeMap"
            >
            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="point_bg" >
            <div class="point_content left_content">
                <p class="title">负荷趋势总览</p>
                <div>
                    <p>发供电平衡图</p>
                    <div class="absolute switch_select">
                        <p @click="changeType('全网')" :class="state.type=='全网'?'active':''">全网</p>
                        <p @click="changeType('网供')" :class="state.type=='网供'?'active':''">网供</p>
                    </div>
                    <div class="select_content">
                        <div class="switch_select">
                            <p @click="changeTypes(0)" :class="state.types==0?'active':''">时间模式</p>
                            <p @click="changeTypes(1)" :class="state.types==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-if="state.types==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            :options="state.caseList"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                        <a-range-picker :allowClear="false" v-if="state.types==0" :disabled-date="disabledDate" v-model:value="state.time1" :valueFormat="'YYYY-MM-DD'" />
                        <a-button type="primary" @click="changeTime1">确定</a-button>
                    </div>
                    <div class="bar" ref="line1">
                        
                    </div>
                    <p>负荷分布趋势图</p>
                    <div class="select_content select_contents">
                        <a-select
                            v-model:value="state.months"
                            mode="multiple"
                            :options="state.timeOption1"
                            :max-tag-count="1"
                            :placeholder="'月份选择'"
                        >
                        </a-select>
                        <a-select
                            v-model:value="state.hours"
                            mode="multiple"
                            :options="state.timeOption2"
                            :max-tag-count="1"
                            :placeholder="'时刻选择'"
                        >
                        </a-select>
                        <a-range-picker :allowClear="false" :disabled-date="disabledDate" v-model:value="state.time2" :valueFormat="'YYYY-MM-DD'" />
                        <a-button type="primary" @click="changeTime2">确定</a-button>
                        <div class="icon_list">
                            <img src="@/assets/images/index2/icon.png" alt="">
                            <p>{{ fixInteger(state.text,1) }}</p>
                            <span>万千瓦</span>
                        </div>
                    </div>
                    <div class="bar" ref="bar">
                        
                    </div>
                </div>
            </div>
        </div>
        <div class="right_content">
            <div class="point_bg" >
                <div class="point_content right_top_content">
                    <p class="title">最大负荷分析</p>
                    <div class="select_content">
                        <a-select
                            v-model:value="state.timeType1"
                            placeholder="时间"
                            :options="[
                                {
                                    label:'年',
                                    value:'y'
                                },
                                {
                                    label:'月',
                                    value:'M'
                                },
                                {
                                    label:'日',
                                    value:'D'
                                },
                            ]"
                        >
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                        <a-date-picker v-if="state.timeType1=='M'" v-model:value="state.timeType2" picker="month" />
                        <a-date-picker v-else-if="state.timeType1=='D'" v-model:value="state.timeType3" />
                        <a-button type="primary" @click="changeTime3">确定</a-button>
                    </div>
                    <div class="line1" ref="line2">
                        
                    </div>
                </div>
            </div>
            <div class="point_bg" >
                <div class="point_content right_bottom_content">
                    <p class="title">特性分析</p>
                    <div>
                        <div class="select absolute">
                            <a-input-number v-model:value="state.threshold" :precision="2" :step="0.01" :min="0" :max="1" />
                            <a-button type="primary" @click="changeLoad">确定</a-button>
                        </div>
                        <div class="table_list">
                            <div>
                                <p>月份</p>
                                <p>最大负荷率</p>
                                <p>最大峰谷差(万千瓦)</p>
                            </div>
                            <div>
                                <div v-for="item in state.tableData">
                                    <p>{{ item.time }}</p>
                                    <p>{{ item.value1 }}</p>
                                    <p>{{ item.value2 }}</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <p>尖峰负荷持续时间</p>
                            <div class="line2" ref="line3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <a-modal v-model:open="state.editShow" wrapClassName="edit_modal" :afterClose="()=>state.editShow=false" :centered="true" :footer="null" :closable="false" :maskClosable="false">
		<div class="user-select point_wrap">
            <p>修改负荷</p>
            <div class="form_number">
                <p>曲线类型</p>
                <a-select
                    v-model:value="state.loadType"
                    :options="state.loadOptions"
                    @change="changeLoadType"
                >
                </a-select>
            </div>
            <div class="form_number">
                <p>原值</p>
                <a-input-number :controls="false" v-model:value="state.oldValue"/>
            </div>
            <div class="form_number">
                <p>新值</p>
                <a-input-number :controls="false" v-model:value="state.newValue"/>
            </div>
            <div class="modal_btn">
                <a-button type="primary" @click="changeLoadValue" :disabled="state.newValue==undefined">确定</a-button>
                <a-button @click="state.editShow=false">取消</a-button>
            </div>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
        </div>
    </a-modal>
    <a-modal v-model:open="state.passwordShow" wrapClassName="password_modal" :afterClose="()=>state.passwordShow=false" :centered="true" :footer="null" :closable="false" :maskClosable="false">
		<div class="user-select point_wrap">
            <p>输入密码</p>
            <div class="form_password">
                <a-input-password v-model:value="state.password" placeholder="输入密码" />
            </div>
            <div class="modal_btn">
                <a-button type="primary" @click="login" :disabled="state.password==undefined">确定</a-button>
                <a-button @click="state.passwordShow=false;state.password=undefined">取消</a-button>
            </div>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
            <span class="point"></span>
        </div>
    </a-modal>
</template>
<script setup>
import { inject, onMounted } from '@vue/runtime-core'
import { markRaw, reactive, toRefs,ref } from '@vue/reactivity'
import { getBarSeries} from '@/utils/indexMain2_1'
import { getLineSeries} from '@/utils/indexMain1'
import { getBarOption,getLineOption} from '@/utils/indexMain2'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { fixInteger,getUniqueMonths } from '@/utils/common'
import { getLoadData,getLoadTime,getLoadTrend,getLoadUpdate} from '@/api/index'
import { message } from 'ant-design-vue'
import { init } from 'echarts'
import dayjs from 'dayjs'
const echarts = inject("ec");
const store = dataStore()
const { partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    year:undefined,
    type:'全网',
    type1:'M',
    timeType:'month',
    caseType:undefined,
    types:0,
    time1:undefined,
    time2:undefined,
    timeType1:'y',
    hours:undefined,
    months:undefined,
    caseOptions,
    caseList:[],
    yearOptions,
    timeOption1:[],
    timeOption2:Array(24).fill(0).map((item,index)=>{
        return{
            label:index+'时',
            value:index,
        }
    }),
    lineData1:[],
    lineData2:{},
    timeData:[],
    barData1:[],
    barData2:{},
    tableData:[],
    partitionOptions,
    text:'',
    threshold:0.95,
    lineStart:0,
    lineEnd:100,
    lineType:undefined,
    originalLoadIndex:undefined,
    loadType:'max_load',
    loadOptions:[
        {
            label:'近五年最大负荷',
            value:'max_load',
        },
        {
            label:'午高峰最大负荷',
            value:'noon_peak',
        },
        {
            label:'晚高峰最大负荷',
            value:'night_peak',
        },
        {
            label:'腰荷最小负荷',
            value:'waist_min',
        },
        {
            label:'夜间最小负荷',
            value:'night_min',
        },
    ],
    loadIndex:undefined,
    editShow:false,
    original_value:undefined,
    oldValue:undefined,
    newValue:undefined,
    passwordShow:false,
    password:undefined
})
const allEcharts = reactive({
    line1:undefined,
    lineChart1:undefined,
    line2:undefined,
    lineChart2:undefined,
    line3:undefined,
    lineChart3:undefined,
    bar:undefined,
    barChart:undefined,
})
const { line1, lineChart1, line2, lineChart2, line3, lineChart3, bar, barChart } = toRefs(allEcharts)
const initAllEcharts = () => { 
    state.lineStart = 0
	state.lineEnd = 100
    initLine1('init')
    initLine2()
    initLine3()
    initBar1()
}
const initLine1=(val)=>{
    state.lineType = val
    let obj = {}
    if(val){
        Object.keys(state.lineData1).forEach(item=>{
            const result = []
            state.lineData1[item].forEach((item1,index1)=>{
                if(index1%48==0){
                    result.push(item1)
                }
            })
            obj[item] = result
        })
    }
    const option = getLineSeries(lineChart1.value,val?obj:state.lineData1,val?obj.time_range:state.lineData1.time_range.map(item=>item.slice(5, 16)),true,state.lineStart,state.lineEnd)
    lineChart1.value.setOption(option,true)
    lineChart1.value.off('datazoom')
    lineChart1.value.on('datazoom', (event)=> {
		state.lineStart = event.start
		state.lineEnd = event.end
		if(state.lineType = 'init') initLine1()
	})
}
const initBar1=()=>{
    const option = getBarOption(
        barChart.value,{
        showLegend:true,
        showXAxisSymbol:true,
        showYAxisLine:true,
        yAxisName:'概率值',
        xAxisName:'负荷率值',
        data:[{
            data:state.barData1,
            name:'全网负荷',
            color:'#D8BFD8',
        }],
        xAxisData:['0-0.1','0.1-0.2','0.2-0.3','0.3-0.4','0.4-0.5','0.5-0.6','0.6-0.7','0.7-0.8','0.8-0.9','0.9-1.0'],
        unit:''
    })
    barChart.value.setOption(option)
}
const changeYear = ()=>{
    initAllData('year')
}
const changeLoadType = ()=>{
    state.oldValue = state.lineData2[state.loadType][state.loadIndex]
}
const initLine2=()=>{
    const obj = {
        '近五年最大负荷':'max_load',
        '午高峰最大负荷':'noon_peak',
        '晚高峰最大负荷':'night_peak',
        '腰荷最小负荷':'waist_min',
        '夜间最小负荷':'night_min',
    }
    const option = getLineOption(
        lineChart2.value,
        {
            yAxisName:'万千瓦',
            data:[
                {
                    name:'近五年最大负荷',
                    color:'#CC0000',
                    data:state.lineData2.max_load,
                },
                {
                    name:'午高峰最大负荷',
                    color:'#FFA500',
                    data:state.lineData2.noon_peak,
                },
                {
                    name:'晚高峰最大负荷',
                    color:'#FFFF00',
                    data:state.lineData2.night_peak,
                },
                {
                    name:'腰荷最小负荷',
                    color:'#87CEEB',
                    data:state.lineData2.waist_min                    ,
                },
                {
                    name:'夜间最小负荷',
                    color:'#00008B',
                    data:state.lineData2.night_min,
                }
            ],
            xAxisData:state.type1=='M' ?state.timeData.map(item=>item.slice(0, 7)):state.timeData,
            unit:'万千瓦',
            showDataZoom:state.type1!='M'
        }
    )
    lineChart2.value.setOption(option,true)
    lineChart2.value.off('dblclick')
    lineChart2.value.on('dblclick',async (params)=>{
        state.loadType = obj[params.seriesName]
        state.oldValue = params.value
        state.loadIndex = params.dataIndex
        state.passwordShow = true
    })
    lineChart2.value.off('updateAxisPointer')
	lineChart2.value.on('updateAxisPointer', function (event) {
        const xAxisInfo = event.axesInfo[0]  
		if (xAxisInfo) {
			state.originalLoadIndex = xAxisInfo.value
            state.loadIndex = state.originalLoadIndex
			state.original_value = state.lineData2[state.loadType][xAxisInfo.value]
            state.oldValue = state.original_value
		}else{
            state.originalLoadIndex = undefined
            state.original_value = undefined
        }
	})
    line2.value.addEventListener('dblclick', (params)=>{
        if(state.original_value!=undefined){
            state.oldValue = state.original_value
            state.loadIndex = state.originalLoadIndex
            state.passwordShow = true
        }
    })
}
const login = ()=>{
    if(state.password=='baogongbg'){
        state.passwordShow = false
        state.password = undefined
        state.editShow = true
    }else{
        message.error('密码错误')
    }
}
const changeLoadValue = ()=>{
    state.editShow = false
    store.showModal()
    getLoadUpdate({
        time:state.lineData2[state.loadType+'_time'][state.loadIndex],
        old_value:state.oldValue,
        new_value:state.newValue,
        area:state.partition,
        field_key:state.type=='全网'?'load':'load_grid'
    }).then(res=>{
        store.hiddenModal()
        if(res.code==200){
            state.lineData2[state.loadType][state.loadIndex] = state.newValue
            initLine2()
            message.success(res.msg)
        }
    }).catch(()=>{
        store.hiddenModal()
    })
}
const initLine3=()=>{
    const option = getBarSeries(lineChart3.value,state.barData2)
    lineChart3.value.setOption(option)
}
const disabledDate = (current)=>{
    const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year;
}
const changeMap = ()=>{
    initAllData('area')
}
const changeLoad = async()=>{
    store.showModal()
    const [data1,data2] = await Promise.all([
        getLoadData({
            area:state.partition,
            type:state.type,
            threshold:state.threshold,
            stime:state.year+'-01-01 00:00:00',
            etime:state.year+'-12-31 23:59:59',
            action:'most'
        }),
        getLoadData({
            area:state.partition,
            type:state.type,
            threshold:state.threshold,
            stime:state.year+'-01-01 00:00:00',
            etime:state.year+'-12-31 23:59:59',
            action:'peak'
        })
    ])
    state.tableData = data1.data['时间'].map((item,index)=>{
        return{
            time:item,
            value1:data1.data.max_rate[index],
            value2:data1.data.max_peak_valley_diff[index],
        }
    })
    state.barData2 = data2.data
    initLine3()
    store.hiddenModal()
}
const changeTime1 = async ()=>{
    if(state.types==1&&state.caseType==undefined){
        message.warning('请选择典型场景')
        return
    }
    store.showModal()
    const data = (await getLoadTrend(Object.assign({
        type:state.type,
        area:state.partition,
    },state.types==0?{
        stime:state.time1[0]+' 00:00:00',
        etime:state.time1[1]+' 23:59:59',
    }:{
        scene_date:state.caseList.find(item=>item.value==state.caseType).values
    }))).data
    state.lineData1 = data
    state.lineStart = 0
    state.lineEnd = 5
    initLine1()
    store.hiddenModal()
}
const changeTime2 = async()=>{
    store.showModal()
    const data = (await getLoadData({
        type:state.type,
        area:state.partition,
        action:'count',
        stime:state.time2[0]+' 00:00:00',
        etime:state.time2[1]+' 23:59:59',
        hours:state.hours,
        months:state.months,
    })).data
    state.barData1 = data.Probability
    state.text = data.max_value
    initBar1()
    store.hiddenModal()
}
const changeTime3 = ()=>{

}
const initAllData = async (val) => {
    if(val){
        state.types = 0
        state.caseType=undefined
        state.caseList = []
        if(val=='year'){
            state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
                return {
                    label:item.name,
                    value:item.name,
                    values:item.date_time,
                }
            })
        }
    }
    store.showModal()
    const [data1,data2] = await Promise.all([
        getLoadTrend({
            type:state.type,
            area:state.partition,
        }),
        getLoadData({
            is_all:1,
            type:state.type,
            area:state.partition,
            freq:state.type1
        })
    ]).catch(()=>{
        return store.hiddenModal()
    })
    state.time1 = [data1.data.time_range[0].slice(0,10),data1.data.time_range[data1.data.time_range.length-1].slice(0,10)]
    state.time2 = [data1.data.time_range[0].slice(0,10),data1.data.time_range[data1.data.time_range.length-1].slice(0,10)]
    state.timeOption1 = getUniqueMonths(state.time2).map(item=>{
        return{
            label:item+'月',
            value:item
        }
    })
    state.lineData1 = data1.data
    state.lineData2 = data2.data.max
    state.timeData = data2.data.max['时间']
    state.barData1 = data2.data.count.Probability
    state.text = data2.data.count.max_value
    state.barData2 = data2.data.peak
    if(!val){
        state.year = +data2.data.most['时间'][0].split('-')[0]
        state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
            return {
                label:item.name,
                value:item.name,
                values:item.date_time,
            }
        })
    }
    state.tableData = data2.data.most['时间'].map((item,index)=>{
        return{
            time:item,
            value1:data2.data.most.max_rate[index],
            value2:data2.data.most.max_peak_valley_diff[index],
        }
    })
    initAllEcharts()
    store.hiddenModal()
}
const changeType = (val)=>{
    if(state.type!=val){
        state.type=val
        initAllData('type')
    }
}
const changeTypes = (val)=>{
    if(state.types!=val){
        state.types=val
    }
}
onMounted(async() => {
    lineChart1.value = markRaw(echarts.init(line1.value))
    lineChart2.value = markRaw(echarts.init(line2.value))
    lineChart3.value = markRaw(echarts.init(line3.value))
    barChart.value = markRaw(echarts.init(bar.value))
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index1{
        display: flex;
        justify-content: space-between;
        position: relative;
        .index_select{
            position: absolute;
            top: -45px;
            right: 570px;
            &:deep(.ant-select){
                width: 150px;
            }
        }
        .index_select_left{
            right: unset;
            left: 570px;
        }
        .left_content,.right_content{
            position: relative;
            width: 915px;
            height: 100%;
        }
        .select_content,.select{
            button{
                margin-left: 20px;
                height: 32px;
                width: 70px;
                font-size: 16px;
                padding: 0;
            }
        }
        .left_content{
            &:deep(.ant-picker-range){
                width: 240px;
            }
            .icon_list{
                position: absolute;
                right: 30px;
                display: flex;
                align-items: center;
                img{
                    width: 35px;
                    height: 27.32px;
                    margin-right: 10px;
                }
                line-height: 26px;
                letter-spacing: 0px;
                font-size: 18px;
                font-family: 思源黑体;
                p{
                    color: rgb(0, 212, 238);
                    font-weight: 500;
                    min-width: 60px;
                }
                span{
                    color: rgb(255, 255, 255);
                    font-weight: 400;
                }
            }
            .switch_select{
                top: 60px;
            }
            .select_content{
                display: flex;
                height: 32px;
                align-items: center;
                &:deep(.ant-select){
                    width: 150px;
                    margin-right: 20px;
                }
                .switch_select{
                    margin-right: 20px;
                }
            }
            .select_contents{
                &:deep(.ant-select){
                    width: 100px;
                }
                &:deep(.ant-select-multiple){
                    width: 125px;
                    .ant-select-selection-item-content{
                        line-height: 20px;
                    }
                    .ant-select-selection-item{
                        display: flex;
                        align-items: center;
                    }
                }
            }
            >div{
                padding: 0 20px;
                >p{
                    color: rgb(236, 253, 253);
                    font-family: 阿里巴巴普惠体 2.0;
                    font-size: 22px;
                    font-weight: 700;
                    line-height: 70px;
                    letter-spacing: 0px;
                    text-align: center;
                }
            }
            .bar{
                width: 100%;
                height: 310px;
            }
        }
        .right_content{
             .select_content{
                margin-left: 20px;
                display: flex;
                height: 32px;
                align-items: center;
                &:deep(.ant-select){
                    width: 100px;
                    margin-right: 20px;
                }
            }
            .right_top_content{
                height: 330px;
                .switch_select{
                    right: 45px;
                    top: 50px;
                    z-index: 2;
                   >p{
                        height: 24px;
                        line-height: 24px;
                        min-width: 35px;
                   }
                }
                .line1{
                    height: 253px;
                    width: 100%;
                }
            }
            >div:first-child{
                margin-bottom: 10px;
            }
            .right_bottom_content{
                height: 525px;
                >div{
                    display: grid;
                    grid-template-columns: 0.9fr 1fr;
                    grid-column-gap: 20px;
                    padding: 20px;
                    .select{
                        display: flex;
                        right: 10px;
                        &:deep(.ant-select){
                            width: 110px;
                            margin-right: 8px;
                        }
                        &:deep(.ant-input-number){
                            width: 80px;
                            background: $AntBgColor;
                            border: none;
                            input{
                                height:  30px;
                                font-size: 14px;
                                color: #fff;
                            }
                        }
                        button{
                            margin-left: 8px;
                        }
                    }
                    .table_list{
                        border: 1px solid rgba(29, 191, 220,0.75);
                        >div:first-child{
                            background: linear-gradient(90.00deg, rgba(4, 99, 171, 0) 0.19%,rgb(3, 118, 123) 49.748%,rgba(4, 99, 171, 0) 100%);
                            p{
                                color: rgb(255, 255, 255);
                                font-family: 思源黑体;
                                font-size: 14px;
                                font-weight: 400;
                                line-height: 40px;
                                letter-spacing: 0px;
                            }
                        }
                        >div:last-child{
                            >div:nth-child(2n){
                                background: rgba(0, 146, 152,0.1);
                            }
                        }
                        >div:last-child>div{
                            p{
                                color: rgb(245, 245, 245);
                                font-family: 思源黑体;
                                font-size: 16px;
                                font-weight: 400;
                                line-height: 32px;
                                letter-spacing: 0px;
                            }
                        }
                        >div:first-child,>div:last-child>div{
                            display: grid;
                            grid-template-columns: 1fr 1fr 1.5fr;
                            p{
                                text-align: center;
                            }
                        }
                    }
                    >div:last-child{
                        >p{
                            color: rgb(236, 253, 253);
                            font-family: 阿里巴巴普惠体 2.0;
                            font-size: 22px;
                            font-weight: 700;
                            letter-spacing: 0px;
                            line-height: 50px;
                        }
                        .line2{
                            height: 390px;
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
</style>
<style lang="scss">
    .edit_modal{
        .ant-modal{
            width: auto!important;
        }
        .user-select{
            padding:10px 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            >p{
                width: 100%;
                color: #fff;
                text-align: center;
                font-size: 18px;
                line-height: 32px;
                letter-spacing: 10px;
                margin-bottom: 20px;
            }
            >div{
                display: flex;
                align-items: center;
            }
            .form_number{
                margin-bottom: 20px;
                >p{
                    font-size: 14px;
                    width: 100px;
                }
                .ant-select{
                    width: 200px;
                }
                .ant-input-number{
                    width: 200px;
                    .ant-input-number-input{
                        height: 30px;
                    }
                }
            }
            .modal_btn{
                padding: 0 20px;
                width: 100%;
                justify-content: space-around;
                button{
                    width: 90px;
                    padding-top:0;
                    padding-bottom:0;
                    height: 28px;
                    font-size: 14px;
                    line-height: 28px;
                }
            }
        }
    }
    .password_modal{
        .ant-modal{
            width: auto!important;
        }
        .user-select{
            padding:10px 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            >p{
                width: 100%;
                color: #fff;
                text-align: center;
                font-size: 18px;
                line-height: 32px;
                letter-spacing: 10px;
                margin-bottom: 20px;
            }
            >div{
                display: flex;
                align-items: center;
            }
            .form_password{
                margin-bottom: 20px;
                .ant-input{
                    width: 250px;
                }
            }
            .modal_btn{
                width: 100%;
                justify-content: space-between;
                button{
                    width: 90px;
                    padding-top:0;
                    padding-bottom:0;
                    height: 28px;
                    font-size: 14px;
                    line-height: 28px;
                }
            }
        }
    }
</style>