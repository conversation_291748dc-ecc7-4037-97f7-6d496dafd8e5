<template>
    <div class="main_content2_index4-1">
        <div class="index_select index_select_left">
            <a-select
                v-model:value="state.year"
                placeholder="年份"
                :options="state.yearOptions"
                @change="changeYear"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="index_select">
            <a-select
                v-model:value="state.routeType"
                placeholder="断面类型"
                :options="state.typeOption"
                @change="changeRouteType"
            >
                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
            </a-select>
        </div>
        <div class="point_bg" >
            <div class="point_content left_content">
                <p class="title">关键交流断面</p>
                <div class="scroll">
                    <a-radio-group v-model:value="state.type" @change="changeType">
                        <div v-for="(item,index) in state.typeList" :key="index" >
                            <a-radio :value="item.value">{{ item.label }}</a-radio>
                        </div>
                    </a-radio-group>
                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content middle_content">
                <p class="title">交流断面总览</p>
                <div class="map_image" @click="router.go(-1)">
                    <img src="@/assets/images/index2/back.png" alt="">
                </div>
                <div class="map_info absolute">
                    <p>断面组成</p>
                    <div v-if="state.lineList.length>0">
                        <p v-for="(item,index) in state.lineList" :key="index">{{ item }}</p>
                    </div>
                </div>
                <!-- <div class="map_select absolute" v-if="state.routeType=='zone220_trafo'||state.routeType=='zone220_inf'">
                    <a-select
                        v-model:value="state.partition"
                        :options="state.partitionOptions"
                        @change="changeMap"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div> -->
                <div class="map" ref="map">
                    
                </div>
            </div>
        </div>
        <div class="point_bg" >
            <div class="point_content right_content">
                <p class="title ellipsis">{{ state.typeName }}</p>
                <div>
                    <div class="select_content">
                        <div class="switch_select">
                            <p @click="changeType1(0)" :class="state.type1==0?'active':''">时间模式</p>
                            <p @click="changeType1(1)" :class="state.type1==1?'active':''">场景模式</p>
                        </div>
                        <a-select
                            v-if="state.type1==1"
                            v-model:value="state.caseType"
                            placeholder="典型场景"
                            :options="state.caseList"
                        >  
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                        <a-range-picker :allowClear="false" v-if="state.type1==0" :disabled-date="disabledDate" v-model:value="state.searchTime" :valueFormat="'YYYY-MM-DD'" />
                        <a-button type="primary" @click="changeTime">确定</a-button>
                        <a-button type="primary" @click="state.modalShow=true">断面时刻数据</a-button>
                    </div>
                    <a-button :disabled="state.tableData.length==0" class="download_btn" type="primary" @click="downLoad">下载</a-button>
                    <div class="table_info" v-if="state.tableData.length<=1">
                        <div>
                            <p>断面名称</p>
                            <p>断面限额</p>
                            <p>重载时长</p>
                            <p>越限时长</p>
                        </div>
                        <div>
                            <p>{{state.dataObj.inf_name}}</p>
                            <p>{{state.dataObj.inf_limit}}</p>
                            <p>{{state.dataObj.heavy_hours}}</p>
                            <p>{{state.dataObj.overload_hours}}</p>
                        </div>
                        <div>
                            <p>断面描述</p>
                            <p>最大负载率</p>
                            <p>重载天数</p>
                            <p>越限天数</p>
                        </div>
                         <div>
                            <p>{{state.dataObj.inf_desc}}</p>
                            <p>{{fixInteger(100*state.dataObj.max_power_rate,2)}}%</p>
                            <p>{{state.dataObj.heavy_days}}</p>
                            <p>{{state.dataObj.overload_days}}</p>
                        </div>
                        <!-- <div>
                            <p>断面名称</p>
                            <p>断面描述</p>
                            <p>断面限额</p>
                            <p>最大负载率</p>
                        </div>
                        <div>
                            <p>{{state.dataObj.inf_name}}</p>
                            <p>{{state.dataObj.inf_desc}}</p>
                            <p>{{state.dataObj.inf_limit}}</p>
                            <p>{{fixInteger(100*state.dataObj.max_power_rate,2)}}%</p>
                        </div>
                        <div>
                            <p>重载时长</p>
                            <p>越限时长</p>
                            <p>重载天数</p>
                            <p>越限天数</p>
                        </div>
                         <div>
                            <p>{{state.dataObj.heavy_hours}}</p>
                            <p>{{state.dataObj.overload_hours}}</p>
                            <p>{{state.dataObj.heavy_days}}</p>
                            <p>{{state.dataObj.overload_days}}</p>
                        </div> -->
                    </div>
                    <div class="table_underline" v-else>
                        <div>
                            <p>名称</p>
                            <p>描述</p>
                            <p>限额</p>
                            <p>最大负载率</p>
                            <p>重载/越限时长</p>
                            <p>重载/越限天数</p>
                        </div>
                        <div class="scroll">
                            <div v-for="item in state.tableData">
                                <!-- <p>{{item.inf_name}}</p> -->
                                <a-tooltip>
                                    <template #title>
                                        {{item.inf_name}}
                                    </template>
                                    <p class="ellipsis">{{item.inf_name}}</p>
                                </a-tooltip>
                                <a-tooltip>
                                    <template #title>
                                        {{item.inf_desc}}
                                    </template>
                                    <p class="ellipsis">{{item.inf_desc}}</p>
                                </a-tooltip>
                                <p>{{item.inf_limit}}</p>
                                <p>{{fixInteger(100*item.max_power_rate,2)}}%</p>
                                <p>{{item.heavy_hours}}/{{item.overload_hours}}</p>
                                <p>{{item.heavy_days}}/{{item.overload_days}}</p>
                            </div>
                        </div>
                    </div>
                    <p>负载率时序展示</p>
                    <div class="line" ref="line">
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    <interface-modal :searchTime="state.searchTime" v-if="state.modalShow" @close="state.modalShow = false"></interface-modal>
</template>
<script setup>
import { inject, onMounted, watch } from '@vue/runtime-core'
import { markRaw, reactive, toRefs } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { registerMapData,fixInteger,getMapScaleByBbox,exportExcels,getPrevDays,generateUniqueHexColors } from '@/utils/common'
import { getLineOption} from '@/utils/indexMain2'
import { getMapOption} from '@/utils/indexMain'
import dayjs from 'dayjs'
import { useRoute,useRouter } from 'vue-router'
import { getInterfaceLine,getTideData,getInterfaceNameList,getCaseDataApi,getNetworkData,getInterfaceMap} from '@/api/index'
import { typeList } from 'ant-design-vue/es/message'
const echarts = inject("ec");
const store = dataStore()
const route = useRoute()
const router = useRouter()
const { partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
const state = reactive({
    partition: '全省',
    type:route.query.name,
    typeName:undefined,
    searchTime:undefined,
    year:undefined,
    caseType:undefined,
    level:undefined,
    type1:0,
    mapData: {},
    yearOptions,
    routeType:undefined,
    typeOption:[
        {
            label: '500kV断面',
            value: 'line_inf'
        },
        {
            label: '500kV主变',
            value: 'transformer_inf'
        },
        {
            label: '220kV断面',
            value: 'zone220_inf'
        },
        {
            label: '220kV主变',
            value: 'zone220_trafo'
        },
    ],
    typeList:[],
    lineList:[],
    caseList:[],
    caseOptions,
    lineData:[],
    timeData:[],
    tableData:[],
    colorList:[],
    dataObj:{},
    modalShow:false,
    partitionOptions,
    geoScale:undefined
})
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
    line:undefined,
    lineChart:undefined,
})
const { map, mapChart, mapOption, line, lineChart } = toRefs(allEcharts)
const changeRouteType = ()=>{
    state.type = undefined
    initAllData('type')
}
const changeMap = ()=>{

}
const downLoad = ()=>{
    store.showModal()
    const xlsxData = [
        {
            'sheetName':'数据',
            'title':['断面名称','断面描述','断面限额','最大负载率','重载时长','越限时长','重载天数','越限天数'],
            data:state.tableData.map((item,index)=>[item.inf_name,item.inf_desc,item.inf_limit,fixInteger(100*item.max_power_rate,2),item.heavy_hours,item.overload_hours,item.heavy_days,item.overload_days])
        }
    ]
    exportExcels(xlsxData, '数据'+dayjs(new Date()).format("YYYYMMDD_HHmmss")+'.xlsx')
    store.hiddenModal()
}
const initAllData = async (val) => {
    store.showModal()
    if(!val||val=='type'){
        const [data1] = await Promise.all([
            getInterfaceNameList(
                {
                    inf_type:state.routeType,
                }
            ),
        ]).catch(err=>{
            store.hiddenModal()
        })
        if(data1.code==200){
            state.typeList = data1.data.map(item=>{
                return {
                    value:item.name,
                    label: item.name,
                    desc:item.desc,
                    vlevel:item.vlevel
                }
            })
            if(state.type == undefined){
                state.type = state.typeList[0].value
                state.typeName = state.typeList[0].desc
            }else{
                state.typeName = state.typeList.find(item=>item.value==route.query.name).desc
            }
        }
    }
    const [res1,res2,res3] = await Promise.all([
        getTideData(Object.assign({
            name:state.type
        },val=='year'?{
            "start_time": state.year+'-01-01',
            "end_time": state.year+'-12-31',
        }:state.caseType&&state.type1==1?{
            start_time:state.caseList.find(item=>item.value==state.caseType).values.slice(0,10),
            end_time:state.caseList.find(item=>item.value==state.caseType).values.slice(0,10),
        }:{
            "start_time": state.searchTime[0],
            "end_time": state.searchTime[1],
        })),
        ['type','typeList',undefined].includes(val)?getInterfaceLine({
            name:state.type
        }):undefined,
        ['type','typeList'].includes(val)?undefined:
        getInterfaceMap(Object.assign({
            area:'全省',
        },val=='year'?{
            "start_time": state.year+'-01-01',
            "end_time": state.year+'-12-31',
        }:{
            "start_time": state.searchTime[0],
            "end_time": state.searchTime[1],
        }))
    ])
    if(res1.code==200){
        if(res1.data.length>0){
            state.dataObj = res1.data[0]
            state.tableData = res1.data
            if([undefined,'type','typeList'].includes(val)){
                state.colorList = generateUniqueHexColors(res1.data.length)
            }
            state.lineData = res1.data.map((item,index)=>{
                return {
                    name:item.inf_name,
                    data:item.power_rate_list.map(item=>item*100),
                    color:state.colorList[index]
                }
            })
            state.timeData = res1.data[0].time_list
        }else{
            state.lineData = []
            state.dataObj = {}
            state.tableData = []
            state.timeData =[]
        }
        initLine()
    }
    if(!val){
        if(state.timeData&&state.timeData[0]){
            state.year = +state.timeData[0].slice(0,4)
        }
    }
    if(!['type','typeList'].includes(val)&&res3.code==200){
        state.mapData = res3.data
        initMap()
    }
    if(!val||val=='year'){
        state.caseList = (state.caseOptions[state.year]||[]).filter(item=>item.area_name==state.partition).map(item=>{
            return {
                label:item.name,
                value:item.name,
                values:item.date_time,
            }
        })
    }
    if(['type','typeList',undefined].includes(val)&& res2.code==200){
        state.lineList = res2.data[state.type]?res2.data[state.type]:[]
    }
    store.hiddenModal()
}
const disabledDate = (current)=>{
    const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year;
}
const initLine = () =>{
    const option = getLineOption(lineChart.value,{
        yAxisName:'负载率(%)',
        data:state.lineData,
        xAxisData:state.timeData,
        unit:'%',
        showDataZoom:state.type1==0? true :false,
        legendType:'scroll'
    })
    lineChart.value.setOption(option,true)
}
const changeType = ()=>{
    state.typeName = state.typeList.find(item => item.label==state.type).desc
    initAllData('typeList')
}
const changeType1 = (val)=>{
    if(state.type1!=val){
        state.type1=val
    }
}
const changeTime = ()=>{
    if(state.type1==1&&state.caseType==undefined){
        message.warning('请选择典型场景')
        return
    }
    initAllData('time')
}
const changeYear = () => {
    initAllData('year')
}
const initMap = async(val) => {
    const option = getMapOption({
        partition:state.partition,
        mapData:state.mapData,
        showArea:false,
        showFeedin:true,
        geoScale:state.geoScale,
        partitionData:[],
        zoom:1
    })
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();
        mapOption.value.geo.zoom = _option.series[0].zoom
        mapOption.value.geo.center = _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click',async (params)=>{//点击地图
        if(params.componentSubType=='map'){
            
        }
    })
}
watch(() => yearOptions.value, (v) => {
    if(state.year==undefined&&v.length>0){
        state.year = v[v.length-1].value
    }
},{
    immediate: true
})
onMounted(async() => {
    state.routeType = route.query.type    
    const geoData = await registerMapData(state.partition)
	echarts.registerMap(state.partition, geoData)
	state.geoScale = (0.75 / getMapScaleByBbox(geoData, map.value.clientWidth, map.value.clientHeight).scale / 1)
    mapChart.value = markRaw(echarts.init(map.value))
    lineChart.value = markRaw(echarts.init(line.value))
    store.showModal()
    // state.searchTime = getPrevDays(3)
    state.searchTime = [route.query.stime,route.query.etime]
    initAllData()
})
</script>
<style lang="scss" scoped>
    .main_content2_index4-1{
        display: flex;
        justify-content: space-between;
        position: relative;
        .index_select{
            position: absolute;
            top: -45px;
            right: 570px;
            &:deep(.ant-select){
                width: 150px;
            }
        }
        .index_select_left{
            right: unset;
            left: 570px;
        }
        .left_content{
            position: relative;
            width: 230px;
            height: 100%;
            .scroll{
                padding: 10px;
                height: calc(100% - 50px);
                &:deep(.ant-radio-group){
                    display: flex;
                    flex-direction: column;
                    span{
                        color: rgb(236, 253, 253);
                        line-height: 24px;
                        font-size: 16px;
                    }
                }
            }
        }
        .middle_content{
            position: relative;
            width: 700px;
            height: 100%;
            .map{
                height: calc(100% - 44px);
                width: 100%;
            }
            .map_select{
                right: 10px;
                top: 60px;
                z-index: 2;
                &:deep(.ant-select){
                    width: 120px;
                }
            }
            .map_image{
                position: absolute;
                left: 5px;
                top: 3px;
                &:hover{
                    cursor: pointer;
                }
                img{
                    width: 40px;
                }
            }
            .map_info{
                top: 50px;
                left: 10px;
                border: 1px solid rgb(29, 191, 220);
                background-color: rgb(6, 24, 34);
                width: 200px;
                z-index: 1;
                >p{
                    background: rgba(5, 190, 195, 0.21);
                    color: rgb(254, 255, 255);
                    font-family: 阿里巴巴普惠体 2.0;
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 43px;
                    letter-spacing: 0px;
                    // text-align: center;
                    text-indent: 3em;
                    position: relative;
                }
                >div{
                    padding: 10px;
                    p{
                        color: rgb(236, 253, 253);
                        font-family: 阿里巴巴普惠体 2.0;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 16px;
                    }
                }
            }
        }
        .right_content{
            height: 100%;
            width: 900px;
            position: relative;
            >div{
                padding: 20px;
                >p{
                    color: rgb(236, 253, 253);
                    font-family: 阿里巴巴普惠体 2.0;
                    font-size: 22px;
                    font-weight: 700;
                    line-height: 31px;
                    letter-spacing: 0px;
                    text-align: center;
                }
                .download_btn{
                    position: absolute;
                    right: 10px;
                    height: 24px;
                    font-size: 16px;
                    padding: 0 15px;
                }
                .table_info{
                    border: 2px solid rgba(7, 129, 192, 0.36);
                    border-left: none;
                    border-right: none;
                    margin: 30px 0 80px;
                    padding: 20px;
                    >div{
                        display: grid;
                        grid-template-columns: 2.5fr 1fr 1fr 1fr;
                        >p {
                            height: 50px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 16px;
                            line-height: 16px;
                            text-align: center;
                        }
                    }
                    >div:nth-child(2n+1){
                        >p {
                            line-height: 50px;
                            background: url('@/assets/images/index/linebg.png');
                            background-repeat: no-repeat;
                            background-position: center bottom;
                        }
                    }
                }
                .table_underline{
                    margin: 30px 0 30px;
                    >div:first-child,>div:last-child>div{
                        grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr;
                    }
                    .scroll{
                        height: 250px;
                        >div{
                            .ellipsis{
                                justify-content: start;
                            }
                        }
                    }
                }
                .select_content{
                    display: flex;
                    height: 32px;
                    align-items: center;
                    &:deep(.ant-select){
                        width: 150px;
                        margin-right: 10px;
                    }
                    &:deep(.ant-picker){
                        width: 240px;
                    }
                    .switch_select{
                        margin-right: 10px;
                    }
                    button{
                        margin-left: 10px;
                        height: 32px;
                        min-width: 70px;
                        font-size: 16px;
                        padding: 0 5px;
                    }
                }
                >div:last-child{
                    margin: 10px 0;
                    height:330px;
                }
            }
        }
    }
</style>