<template>
    <div class="main_content1_default">
        <div class="left_content">
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">发供电平衡图</p>
                    <div class="small_content" ref="line1">

                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">新能源日出力曲线</p>
                    <div class="small_content" ref="line2">
                    
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">日旋转备用曲线</p>
                    <div class="small_content" ref="line3">
                    
                    </div>
                </div>
            </div>
        </div>
        <div class="point_bg">
            <div class="middle_content point_content">
                <p class="title">电网潮流示意图</p>
                <div class="map_select absolute">
                    <a-select
                        v-model:value="state.partition"
                        :options="state.partitionOptions"
                        @change="changeMap"
                    >
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <p class="time_text">{{ state.time }}</p>
                <div class="map" ref="map">

                </div>
            </div>
        </div>
        <div class="right_content">
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">区外受入</p>
                    <div class="small_content">
                        <div class="switch_select">
                            <p @click="changeType('dc')" :class="state.feedInType=='dc'?'active':''">区外直流</p>
                            <p @click="changeType('ac')" :class="state.feedInType=='ac'?'active':''">区外交流</p>
                        </div>
                        <div class="line"  ref="line4">

                        </div>
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">断面/设备重过载列表</p>
                    <div class="small_content">
                        <div class="type_select">
                            <a-select
                                v-model:value="state.deviceType"
                                @change="changeDevice"
                            >
                                <a-select-option value="trafo">主变</a-select-option>
                                <a-select-option value="line">线路</a-select-option>
                                <a-select-option value="interface">断面</a-select-option>
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <a-select
                                v-model:value="state.deviceVnkV"
                                @change="changeDevice"
                            >
                                <a-select-option :value="1">全部</a-select-option>
                                <a-select-option :value="2">500kV</a-select-option>
                                <a-select-option :value="3">220kV</a-select-option>
                                <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                            <p class="icon_text" @click="state.tableShow=!state.tableShow">{{state.tableShow?'收起':'详情'}}</p>
                        </div>
                        <div class="bar" ref="bar">

                        </div>
                        <div class="table_underline" v-show="state.tableShow">
                            <div>
                                <p>设备名称</p>
                                <p>潮流功率</p>
                                <p>稳定限额</p>
                                <p>限额利用率</p>
                            </div>
                            <div class="scroll">
                                <div v-for="(item,index) in state.deviceList">
                                    <p>{{ item.name }}</p>
                                    <p>{{ fixInteger(item.power) }}</p>
                                    <p>{{ fixInteger(item.limit) }}</p>
                                    <p>{{ fixInteger(100*item.rate) }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content">
                    <p class="title">电网实时保供能力评价</p>
                    <p class="zhibiao1">
                        供电裕度: <span>{{fixInteger(indicatorsData.power_supply_margin,0)}}</span>
                    </p>
                    <p class="zhibiao2">
                        正备用容量: <span>{{fixInteger(indicatorsData.reserve_low,0)}}</span>
                    </p>
                    <p class="zhibiao3">
                        负备用容量: <span>{{fixInteger(indicatorsData.peak_shaving,0)}}</span>
                    </p>
                    <p class="zhibiao4">
                        无惯量电源渗透率: <span>{{fixInteger(100*indicatorsData.non_inertia_penetration,1)}}</span>%
                    </p>
                    <p class="zhibiao5">
                        外来电依存度: <span>{{fixInteger(100*indicatorsData.feedin_dependence,1)}}</span>%
                    </p>
                    <p class="zhibiao6">
                        爬坡能力: <span>{{fixInteger(indicatorsData.ramp_cap_upward,0)}}</span>
                    </p>
                    <div class="small_content" ref="radar">
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject, onMounted,onUnmounted } from '@vue/runtime-core'
import { markRaw, reactive, toRefs,ref } from '@vue/reactivity'
import { getLineSeries,getRadarSeries,getBarOption} from '@/utils/indexMain1'
import { getLineOption} from '@/utils/indexMain2'
import { getMapServies} from '@/utils/indexMain'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { registerMapCopy,fixInteger } from '@/utils/common'
import { getTideMap,getCapacityIndicator,getLineTrafo,getOutputCapacity,getFeedinData } from '@/api/index'
import { echartsResize } from '../../config/setting.config'
const echarts = inject("ec");
const store = dataStore()
const { partitionOptions } = storeToRefs(store)
const thresholds ={
    power_supply_margin:[-100,0,50,200], 
    feedin_dependence:[0.9,0.6,0.3,0],
    non_inertia_penetration:[0.85,0.6,0.35,0],
    peak_shaving:[0,150,300,450],
    ramp_cap_upward:[0,50,150,300],
    reserve_low:[0,50,150,300]
}
const state = reactive({
    partition: '全省',
    deviceType: 'trafo',
    deviceVnkV: 2,
    partitionOptions,
    deviceList:[],
    trafoList:[],
    lineList:[],
    interfaceList:[],
    lineData:{},
    time:'',
    timer:undefined,
    indicators:undefined,
    tableShow:false,
    feedInType:'dc',
    barData:[],
    feedInData:[],
    feedinMapData:{
        feedin1:0,
        feedin2:0,
        feedin3:0,
        feedin4:0,
        feedin5:0,
    }
})
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
    radar:undefined,
    radarChart:undefined,
    line1:undefined,
    lineChart1:undefined,
    line2:undefined,
    lineChart2:undefined,
    line3:undefined,
    lineChart3:undefined,
    line4:undefined,
    lineChart4:undefined,
    bar:undefined,
    barChart:undefined,
})
const { map, mapChart, mapOption, radar, radarChart, line1, lineChart1, line2, lineChart2, line3, lineChart3, line4, lineChart4,bar, barChart} = toRefs(allEcharts)
const indicatorsData = ref({
    power_supply_margin:1,
    non_inertia_penetration:1,
    reserve_low:1,
    feedin_dependence:1,
    peak_shaving:1,
    ramp_cap_upward:1,
})
const indicatorsValue = ref({})
const initAllEcharts = () => {
    const option1 = getLineSeries(lineChart1.value,state.lineData,state.lineData.time_range.map(item=>item.slice(10, 16)))
    lineChart1.value.setOption(option1,true)
    const option2 = getLineOption(lineChart2.value,{
        yAxisName:'功率(万千瓦)',
        data:[
            {
                name:'分布式光伏',
                color:'#FFFFE0',
                areaColor:'#FFFFE0',
                data:state.lineData.solar_distribute,
                showArea:true,
                stack:true
            },
            {
                name:'集中式光伏',
                color:'#FFD700',
                areaColor:'#FFD700',
                data:state.lineData.solar_centralized,
                showArea:true,
                stack:true
            },
            {
                name:'分布式风电',
                color:'#87CEEB',
                areaColor:'#87CEEB',
                data:state.lineData.wind_distribute,
                showArea:true,
                stack:true
            },
            {
                name:'集中式风电',
                color:'#003366',
                areaColor:'#003366',
                data:state.lineData.wind_centralized,
                showArea:true,
                stack:true
            },
            {
                name:'弃风',
                color:'#808080',
                data:state.lineData.wind_curtailment,
            },
            {
                name:'弃光',
                color:'#8B0000',
                data:state.lineData.solar_curtailment,
            }
        ],
        xAxisData:state.lineData.time_range.map(item=>item.slice(10, 16)),
        unit:'万千瓦',
        showDataZoom:false
    })
    lineChart2.value.setOption(option2)
    if(state.partition == '全省'){
        const option3 = getLineOption(lineChart3.value,{
            yAxisName:'功率(万千瓦)',
            data:[
                {
                    name:'正备用',
                    color:'rgb(24, 145, 172)',
                    areaColor:'rgb(24, 145, 172)',
                    data:state.lineData.reserve_up,
                    showArea:true
                },
                {
                    name:'负备用',
                    color:'rgb(175, 205, 59)',
                    areaColor:'rgb(175, 205, 59)',
                    data:state.lineData.reserve_down,
                    showArea:true
                }
            ],
            xAxisData:state.lineData.time_range.map(item=>item.slice(10, 16)),
            unit:'万千瓦',
            showDataZoom:false
        })
        lineChart3.value.setOption(option3)
    }
    
}
const initFeedInLine = ()=>{
    const color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
    function sortByName(arr, order) {
        return arr.sort((a, b) => {
            const indexA = order.indexOf(a.name);
            const indexB = order.indexOf(b.name);
            return indexA - indexB;
        });
    }
    const option4 = getLineOption(lineChart4.value,{
        yAxisName:'功率(万千瓦)',
        data:sortByName(Object.keys(state.feedInData[state.feedInType]).map((item,index)=>{
            return {
                name:item,
                data:state.feedInData[state.feedInType][item],
                color:color[index],
                areaColor:color[index],
                showArea:true,
                stack:true
            }
        }),['天中直流','青豫本侧','灵宝直流']),
        xAxisData:state.feedInData.time_range.map(item=>item.slice(10, 16)),
        unit:'万千瓦',
        showDataZoom:false,
        showSum:true,
    })
    lineChart4.value.setOption(option4,true)
}
const initRadar = ()=>{
    const option5 = getRadarSeries(indicatorsValue.value,indicatorsData.value,state.partition)
    radarChart.value.setOption(option5)
}
const changeType = (val)=>{
    if(state.feedInType==val) return
    state.feedInType = val
    initFeedInLine()
}
const changeMap = async()=>{
    store.showModal()
    await registerMapCopy(state.partition)
    initAllData()
}
const initBar = ()=>{
    const option = getBarOption(barChart.value,state.deviceType,state.barData)
    barChart.value.setOption(option)
}
const changeDevice = ()=>{
    getLineTrafo({
        area:state.partition,
        ele_name:state.deviceType,
        voltage:state.deviceVnkV
    }).then(res=>{
        state.deviceList = res.data.detail.name?res.data.detail.name.map((item,index)=>{
            return{
                name:item,
                limit:res.data.detail.limit[index],
                power:res.data.detail.power[index],
                rate:res.data.detail.rate[index],
            }
        }):[]
        state.barData = Object.values(res.data.scope)
        initBar()
    })
    
}
const initMap = (val) => {
    const option = getMapServies(state.indicators,state.partition,state.mapData,true,true,true,state.feedinMapData)
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();  
        mapOption.value.geo.zoom= _option.series[0].zoom
        mapOption.value.geo.center= _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapOption.value.series[1].label.fontSize = echartsResize(8)*(1+(_option.series[0].zoom-1.23)*0.3)
        mapChart.value.setOption(mapOption.value)
    })
}
const initAllData = async (val) => {
    const [data1,data2,data3,data4,data5]=await Promise.all([
        getTideMap({
            area:state.partition
        }),
        getOutputCapacity({
            area:state.partition,
        }),
        getLineTrafo({
            area:state.partition,
            ele_name:state.deviceType,
            voltage:state.deviceVnkV
        }),
        val?getCapacityIndicator({
            area:state.partition
        }):undefined,
        val?getFeedinData({
            
        }):undefined,
    ])
    if(data2.code==200){
        state.lineData = data2.data
        state.time = state.lineData.last_time
        initAllEcharts()
    }
    if(data3.code==200){
        state.deviceList =data3.data.detail.name?data3.data.detail.name.map((item,index)=>{
            return{
                name:item,
                limit:data3.data.detail.limit[index],
                power:data3.data.detail.power[index],
                rate:data3.data.detail.rate[index],
            }
        }):[]
        state.barData = Object.values(data3.data.scope)
        initBar()
    }
    if(val&&data4.code==200){
        state.indicators = data4.data
    }
    if(data5&&data5.code==200){
        state.feedInData = data5.data
        const timeIndex = data5.data.time_range.length-1
        state.feedinMapData.feedin1 = data5.data.dc['天中直流'][timeIndex]
        state.feedinMapData.feedin2 = data5.data.dc['灵宝直流'][timeIndex]
        state.feedinMapData.feedin3 = data5.data.dc['青豫本侧'][timeIndex]
        state.feedinMapData.feedin4 = data5.data.ac['长南'][timeIndex]
        state.feedinMapData.feedin5 = data5.data.ac['鄂豫'][timeIndex]
        initFeedInLine()
    }
    if(state.indicators&&data1.code==200){
        state.mapData =data1.data
        initMap(val)
    }
    if(data4&&data4.code==200){
        Object.keys(state.indicators[state.partition]).forEach(item=>{
            if(item=='solar_consump_rate'||item=='wind_consump_rate'||item=='last_time') return
            if(item=='feedin_dependence'||item=='non_inertia_penetration'){
                let temp = (state.indicators[state.partition][item]-thresholds[item][3])/(thresholds[item][0]-thresholds[item][3])
                indicatorsValue.value[item]=1-(temp<=0?0:temp>1 ? 1:temp)
            }else{
                let temp
                temp = (state.indicators[state.partition][item]-thresholds[item][0])/(thresholds[item][3]-thresholds[item][0])
                indicatorsValue.value[item]=temp<=0?0:temp>1 ? 1:temp
            }
        })
        indicatorsData.value = state.indicators[state.partition]
        initRadar()
    }
    refreshData()
    store.hiddenModal()
}
const refreshData = ()=>{
    if(state.timer){
        clearTimeout(state.timer)
        state.timer = undefined
    }
    state.timer = setTimeout(async()=>{
       initAllData(true)
    },30000)
}
const beforeDistory = ()=>{
    clearTimeout(state.timer)
    state.timer = undefined
}
onUnmounted(()=>{
    clearTimeout(state.timer)
    state.timer = undefined
    window.removeEventListener('beforeunload',beforeDistory)
})
window.addEventListener('beforeunload',beforeDistory);
onMounted(async() => {
    store.showModal()
    await registerMapCopy(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    radarChart.value = markRaw(echarts.init(radar.value))
    lineChart1.value = markRaw(echarts.init(line1.value))
    lineChart2.value = markRaw(echarts.init(line2.value))
    lineChart3.value = markRaw(echarts.init(line3.value))
    lineChart4.value = markRaw(echarts.init(line4.value))
    barChart.value = markRaw(echarts.init(bar.value))
    initAllData(true)
})
</script>
<style lang="scss" scoped>
    .main_content1_default{
        display: flex;
        justify-content: space-between;
        &:deep(.ant-select){
            width: 120px;
        }
        .small_content{
            height: 237.3px;
        }
        .table_underline{
            position: absolute;
            top: 80px;
            width: 100%;
            background-color: rgb(3, 22, 29);
            >div:first-child,>div:last-child>div{
                grid-template-columns: 1fr 1fr 1fr 1fr;
            }
            >div:last-child{
                height: 150px;
            }
        }
        .left_content,.right_content{
            width: 540px;
            .point_bg{
                margin-bottom: 10px;
            }
        }
        .middle_content{
            width: 745px;
            .map_select{
                top: 55px;
                left: 10px;
                z-index: 1;
            }
            .time_text{
                position: absolute;
                top: -50px;
                left: 0;
                color: $activeTextColor;
                font-size: 40px;
                line-height: 40px;
                font-weight: bolder;
                letter-spacing: 2px;
                text-align: center;
                width: 100%;
            }
            .map{
                height: 822px;
            }
        }
        .right_content{
            width: 540px;
            .line{
                height: 200px;
            }
            .switch_select{
                z-index: 2;
                padding: 5px 0 0 5px;
            }
            .icon_text{
                font-size: 16px;
                background: #273652;
                border-radius: 5px;
                line-height: 30px;
                padding: 0 8px;
                &:hover{
                    cursor: pointer;
                }
            }
            .type_select{
                display: flex;
                align-items: center;
                padding-left: 5px;
                padding-top: 5px;
                z-index: 2;
                >div{
                    margin-right: 20px;
                }
            }
            .bar{
                height: 200px;
            }
            p[class^="zhibiao"]{
                color: rgb(177, 206, 205);
                span{
                    color:rgb(22, 192, 240) ;
                    font-weight: bolder;
                }
                position: absolute;
                font-size: 14px;
                line-height: 20px;
            }
            p.zhibiao1{
                top: 48px;
                left: 230px;
            }
            p.zhibiao2{
                top: 105px;
                left: 70px;
            }
            p.zhibiao3{
                top: 195px;
                left: 70px;
            }
            p.zhibiao4{
                top: 258px;
                left: 205px;
            }
            p.zhibiao5{
                top: 195px;
                left:365px;
            }
            p.zhibiao6{
                top: 105px;
                left:365px;
            }
        }
    }
</style>