<template>
    <div class="login_bg">
        <div>
            <p>欢迎登录</p>
            <div class="form_content">
                <a-form
                    :model="formState"
                    @finish="onFinish"
                    @finishFailed="onFinishFailed"
                >
                    <a-form-item
                    label="账号"
                    name="username"
                    :rules="[{ required: true, message: '请输入账号!' }]"
                    >
                        <a-input v-model:value="formState.username" placeholder="请输入账号" />
                    </a-form-item>

                    <a-form-item
                        label="密码"
                        name="password"
                        :rules="[{ required: true, message: '请输入密码!' }]"
                    >
                        <a-input-password v-model:value="formState.password" placeholder="请输入密码" />
                    </a-form-item>

                    <a-form-item>
                        <a-button type="primary" html-type="submit">登录</a-button>
                    </a-form-item>
                </a-form>
            </div>
        </div>
    </div>
</template>
<script setup>
import { SearchOutlined,PlayCircleOutlined } from '@ant-design/icons-vue';
import { onMounted} from '@vue/runtime-core'
import { reactive, ref } from '@vue/reactivity'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { dataStore } from '@/store/dataStore'
import { getLogin} from '@/api/index'
import { message } from 'ant-design-vue'
import md5 from 'js-md5'
const router = useRouter()
const store = dataStore()
const { token,end_time } = storeToRefs(store)
const state = reactive({
   
})
const formState = reactive({
    username: '',
    password: '',
});
const onFinish = values => {
    store.showModal()
    getLogin({
        username:values.username,
        password:md5(values.password).toUpperCase()
    }).then(res=>{
        store.hiddenModal()
        if (res.code == 200) {
            token.value = res.data.token
            end_time.value = res.data.expire_time
            sessionStorage.setItem('token', res.data.token)
            sessionStorage.setItem('end_time', res.data.expire_time)
            router.push({
                path:'/dashboard',
            })
		}
    })
};
const onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
};
onMounted(()=>{
    store.hiddenModal()
})
</script>
<style lang="scss" scoped>
   .login_bg{
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        background: url('@/assets/login_bg.png');
        background-size: 100% 100%;
        padding-top: 205px;
        >div{
            height: 335px;
            width: 620px;
            padding: 20px;
            >p{
                font-size: 28px;
                color:rgb(101, 224, 220);
            }
            .form_content{
                padding:60px 60px 60px 40px;
            }
            &:deep(){
                .ant-form-item-label{
                    width: 100px;
                    label{
                        color: #fff;
                        font-size: 16px;
                        height: 32px;
                    }
                }
                .ant-input-password{
                    background-color: rgb(3, 22, 28);
                    border-color: #00FFF7;
                    height: 32px;
                    padding: 4px 11px;
                    span{
                        color: #fff;
                        font-size: 16px;
                        &:hover{
                            color: #fff;
                        }
                    }
                    .ant-input{
                        height: 24px;
                    }
                }
                .ant-input{
                    background-color: rgb(3, 22, 28);
                    border-color: #00FFF7;
                    height: 32px;
                    color: #fff;
                    font-size: 14px;
                    &::placeholder{
                        color: #fff;
                    }
                }
                .ant-form-item-control-input-content{
                    display: flex;
                    justify-content: center;
                    button{
                        margin-top: 20px;
                        width: 200px;
                        font-size: 16px;
                        height: 32px;
                        line-height: 32px;
                        padding: 0;
                        background-color: rgb(0, 255, 247);
                        color: #000;
                    }
                }
            }
        }
   }
</style>