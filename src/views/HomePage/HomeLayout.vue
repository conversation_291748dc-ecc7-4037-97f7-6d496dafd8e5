<template>
    <div class="home-layout">
        <new-home-header />
        <div class="home-body">
            <router-view />
        </div>
    </div>
    
</template>
<script setup>
import NewHomeHeader from '@/components/NewHomeHeader.vue'
</script>
<style scoped lang="scss">
.home-layout{
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(135deg, #07141f 0%, #0e2331 100%);
}
.home-body{
    padding: 16px 16px 24px;
}
</style>

