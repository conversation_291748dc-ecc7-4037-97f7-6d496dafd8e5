<template>
  <div class="main-dashboard">
    <!-- 主要功能按钮区域 -->
    <div class="function-buttons">
      <button
        class="function-btn"
        :class="{ active: activeMain === 'optimize' }"
        @click="setMain('optimize')"
      >资源优化配置</button>
      <button
        class="function-btn"
        :class="{ active: activeMain === 'evaluate' }"
        @click="setMain('evaluate')"
      >配置方案评估</button>
      <button
        class="function-btn"
        :class="{ active: activeMain === 'decision' }"
        @click="setMain('decision')"
      >方案决策推演</button>
    </div>

    <!-- 主要内容区域：动态子组件渲染 -->
    <component :is="currentComponent" />
  </div>
</template>

<script setup>
import { computed, ref, defineAsyncComponent } from 'vue'

// 主级状态（仅三种）
const activeMain = ref('optimize') // optimize | evaluate | decision

const setMain = (key) => {
  activeMain.value = key
}

// 动态组件映射（按需加载，三选一）
const loaders = {
  optimize: () => import('./Dashboard/OptimizeOverview.vue'),
  evaluate: () => import('./Dashboard/EvaluateView.vue'),
  decision: () => import('./Dashboard/DecisionView.vue')
}

const currentComponent = computed(() => {
  const loader = loaders[activeMain.value] || loaders.optimize
  return defineAsyncComponent(loader)
})
</script>

<style lang="scss" scoped>
.main-dashboard {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #03160D 0%, #0F2A1A 100%);
  color: #fff;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

.header-title {
  text-align: center;
  padding: 20px 0;
  background: linear-gradient(90deg, rgba(134, 251, 139, 0.1) 0%, rgba(134, 251, 139, 0.05) 100%);
  border-bottom: 2px solid #86FB8B;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: #86FB8B;
    margin: 0 0 15px 0;
    text-shadow: 0 0 10px rgba(134, 251, 139, 0.5);
  }

  .nav-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;

    .tab-item {
      padding: 8px 20px;
      background: rgba(134, 251, 139, 0.1);
      border: 1px solid #86FB8B;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #86FB8B;
        color: #000;
        font-weight: bold;
      }

      &:hover:not(.active) {
        background: rgba(134, 251, 139, 0.2);
      }
    }
  }
}

.function-buttons {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 20px 0;

  .function-btn {
    padding: 12px 30px;
    background: rgba(134, 251, 139, 0.1);
    border: 2px solid #86FB8B;
    border-radius: 8px;
    color: #86FB8B;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #86FB8B;
      color: #000;
      box-shadow: 0 0 15px rgba(134, 251, 139, 0.5);
    }

    &:hover:not(.active) {
      background: rgba(134, 251, 139, 0.2);
      transform: translateY(-2px);
    }
  }
}


/* 响应式设计 */
@media (max-width: 1200px) {
  .function-buttons {
    flex-wrap: wrap;
    gap: 15px;

    .function-btn {
      font-size: 14px;
      padding: 10px 20px;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(134, 251, 139, 0.1);
}

::-webkit-scrollbar-thumb {
  background: #86FB8B;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(134, 251, 139, 0.8);
}
</style>
