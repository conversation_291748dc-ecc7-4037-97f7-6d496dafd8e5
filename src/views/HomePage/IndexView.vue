<template>
    <div class="point_bg">
        <div class="home_page point_content">
            <div class="case_search flex align-center">
                <a-input v-model:value="state.searchVal" @change="search" placeholder="算例查询">
                    <template #prefix>
                        <search-outlined />
                    </template>
                </a-input>
                <p>共 {{ state.caseData.length }} 项</p>
            </div>
            <div class="case_table">
                <div class="case_table_header">
                    <p>序号</p>
                    <p>算例名称</p>
                    <p>算例年份</p>
                    <p>最高负荷</p>
                    <p>集中式新能源装机</p>
                    <p>时序安排</p>
                    <p>状态</p>
                    <p>操作</p>
                    <p>备注</p>
                </div>
                <div class="case_table_body scroll">
                    <div v-for="(item,index) in state.searchData" :key="index">
                        <p>{{ index +1 }}</p>
                        <p>{{ item.name }}</p>
                        <p>{{ item.year }}</p>
                        <p>{{ item.max_load }} 万千瓦</p>
                        <p>{{ item.new_energy_zhuangji }} 万千瓦</p>
                        <p>{{ item.time_value }}</p>
                        <p>{{ getStatus(item.status) }}</p>
                    <div>
                        <play-circle-outlined :class="getColor(item.status)" @click="goToIndex(item.id,item.status)" />
                        <!-- <edit-outlined /> -->
                    </div>
                        <p>{{ item.comment?item.comment:'暂无备注' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { SearchOutlined,PlayCircleOutlined } from '@ant-design/icons-vue';
import { onMounted} from '@vue/runtime-core'
import { reactive, ref } from '@vue/reactivity'
import { useRouter } from 'vue-router'
import { dataStore } from '@/store/dataStore'
import { getCaseDataApi } from '@/api/index'
const router = useRouter()
const store = dataStore()
const state = reactive({
    caseData:[],
    searchData:[],
    searchVal:undefined
})
const goToIndex = (id,status) => {
    if(status!=4) return
    store.changeCase(id)
    router.push({
        // path:'/Index/FunctionModule1',
        path:'/Index/FunctionModule3-1',
    })
}
const getColor = (status) => {
    if (status == 0) {
        return 'unAnalyze'
    } else if (status == 1) {
        return 'analyzing'
    }else if (status == 2) {
        return 'analyzed'
    }else if (status == 3) {
        return 'analyzeFailed'
    }else if (status == 4) {
        return 'sussess'
    }
}
const getStatus = (status) => {
    if (status == 0) {
        return '未分析'
    } else if (status == 1) {
        return '分析中'
    }else if (status == 2) {
        return '已分析'
    }else if (status == 3) {
        return '分析失败'
    }else if (status == 4) {
        return '已启用'
    }
}
const search = ()=>{
    state.searchData = state.caseData.filter(item=>item.name.includes(state.searchVal))
}
onMounted(()=>{
    store.showModal()
    getCaseDataApi({}).then(res => {
        state.caseData = res.data
        state.searchData = res.data
        store.hiddenModal()
    })
})
</script>
<style lang="scss" scoped>
    .home_page{
        padding: 30px;
        background-color: rgb(2, 4, 24);
        .case_search {
            margin-bottom: 40px;

            &:deep(.ant-input-affix-wrapper){
                width: 280px;
                border-color: rgb(7, 219, 255);
                background-color: rgb(39, 54, 82);
                padding: 5px;

                .ant-input{
                    background-color: rgb(39, 54, 82);
                    color: rgb(7, 219, 255);
                    font-size: 18px;
                    line-height: 32px;
                    &::placeholder {
                        color: #fff;
                    }
                }

                .ant-input-prefix {
                    >span {
                        font-size: 32px;
                        color: rgb(7, 219, 255);
                    }
                }
            }

            >p {
                color: rgb(7, 219, 255);
                font-size: 18px;
                margin-left: 25px;
            }
        }

        .case_table {

            .case_table_header,
            .case_table_body>div {
                display: grid;
                grid-template-columns: 0.5fr 2fr 0.5fr 1fr 1fr 0.5fr 1fr 1fr 1.5fr;
                >div{
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    >span {
                        font-size: 28px;
                        &:hover {
                            cursor: pointer;
                        }
                    }
                    .unAnalyze{
                        color: #ccc;
                    }
                    .analyzing{
                        color: green;
                    }
                    .sussess{
                        color: aquamarine;
                    }
                    .analyzed{
                        color: rgb(7, 219, 255);
                    }
                    .analyzeFailed{
                        color: red;
                    }
                }
                >p {
                    font-size: 18px;
                    line-height: 50px;
                    text-align: center;
                }
            }

            .case_table_header {
                >p {
                    background: url('@/assets/images/index/linebg.png');
                    background-repeat: no-repeat;
                    background-position: center bottom;
                }
            }

            .case_table_body {
                height: 670px;
                >div:nth-child(2n+2) {
                    background-color: rgb(2, 35, 59);
                }
            }
        }
    }
</style>