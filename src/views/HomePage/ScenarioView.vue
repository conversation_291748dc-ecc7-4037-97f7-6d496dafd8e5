<template>
    <div class="scenario-view">
        <div class="content-wrapper">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 规划安排 -->
                <div class="section planning-section">
                    <h3 class="section-title">规划安排</h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>规划机组类型</th>
                                    <th>装机容量(原有+新增)</th>
                                    <th>成本</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in planningData" :key="index">
                                    <td>{{ item.type }}</td>
                                    <td>
                                        <input v-model="item.capacity" placeholder="XXX" class="input-field" />
                                    </td>
                                    <td>
                                        <input v-model="item.cost" placeholder="XXX" class="input-field" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 场景设置 -->
                <div class="section scenario-section">
                    <h3 class="section-title">场景设置</h3>
                    <div class="form-group">
                        <label>场景名称:</label>
                        <input v-model="scenarioName" placeholder="请输入场景名称" class="input-field" />
                    </div>
                    <div class="form-group">
                        <label>场景备注:</label>
                        <textarea v-model="scenarioRemark" placeholder="请输入场景备注" class="textarea-field"></textarea>
                    </div>
                </div>
            </div>

                        <!-- 右侧面板 -->
            <div class="right-panel">
                <h3 class="section-title">场景参数</h3>
                
                <!-- 场景参数 -->
                <div class="params-grid">
                    <div class="param-item" v-for="(param, index) in scenarioParams" :key="index">
                        <label>{{ param.label }}</label>
                        <div class="param-input">
                            <input v-model="param.value" placeholder="XXX" class="input-field" />
                            <div class="unit-box">%</div>
                        </div>
                    </div>
                </div>

                <!-- 备用约束 -->
                <div class="constraint-grid">
                    <div class="constraint-item" v-for="(constraint, index) in constraints" :key="index">
                        <label>{{ constraint.label }}</label>
                        <div class="toggle-group">
                            <button 
                                :class="['toggle-btn', { active: constraint.value === 'on' }]"
                                @click="constraint.value = 'on'"
                            >
                                开
                            </button>
                            <button 
                                :class="['toggle-btn', { active: constraint.value === 'off' }]"
                                @click="constraint.value = 'off'"
                            >
                                关
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 约束条件 -->
                <div class="condition-section">
                    <h4 class="subsection-title">约束条件</h4>
                    <div class="condition-table">
                        <div class="table-header">
                            <div class="header-cell">变量</div>
                            <div class="header-cell">条件</div>
                            <div class="header-cell">数值</div>
                        </div>
                        <div class="table-body">
                            <div class="condition-row">
                                <select v-model="constraintVariable" class="select-field">
                                    <option value="curtailment">弃光率</option>
                                    <option value="curtailment_wind">弃风率</option>
                                    <option value="efficiency">效率</option>
                                </select>
                                <select v-model="constraintCondition" class="select-field">
                                    <option value="<=">≤</option>
                                    <option value=">=">≥</option>
                                    <option value="=">=</option>
                                </select>
                                <input v-model="constraintValue" placeholder="XXX" class="input-field" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 优化目标 -->
                <div class="target-section">
                    <h4 class="subsection-title">优化目标</h4>
                    <select v-model="optimizationTarget" class="select-field full-width">
                        <option value="">请选择优化目标</option>
                        <option value="cost">成本最小化</option>
                        <option value="efficiency">效率最大化</option>
                        <option value="reliability">可靠性最大化</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-actions">
            <button class="btn btn-reset" @click="resetForm">重置</button>
            <button class="btn btn-save" @click="saveForm">保存</button>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 规划安排数据
const planningData = reactive([
    { type: '煤电', capacity: '', cost: '' },
    { type: '风电', capacity: '', cost: '' },
    { type: '光伏', capacity: '', cost: '' },
    { type: '储能', capacity: '', cost: '' }
])

// 场景设置
const scenarioName = ref('')
const scenarioRemark = ref('')

// 场景参数
const scenarioParams = reactive([
    { label: '负荷上备用率', value: '' },
    { label: '负荷下备用率', value: '' },
    { label: '风电备用系数', value: '' },
    { label: '光伏备用系数', value: '' },
    { label: '事故备用率', value: '' }
])

// 备用约束
const constraints = reactive([
    { label: '备用约束:', value: 'on' },
    { label: '线路和主变约束:', value: 'on' },
    { label: '机组爬坡约束:', value: 'on' },
    { label: '忽略网格约束:', value: 'off' },
    { label: '机组启停约束:', value: 'off' }
])

// 约束条件
const constraintVariable = ref('curtailment')
const constraintCondition = ref('<=')
const constraintValue = ref('')

// 优化目标
const optimizationTarget = ref('')

// 重置表单
const resetForm = () => {
    planningData.forEach(item => {
        item.capacity = ''
        item.cost = ''
    })
    scenarioName.value = ''
    scenarioRemark.value = ''
    scenarioParams.forEach(param => param.value = '')
    constraints.forEach(constraint => constraint.value = 'off')
    constraintValue.value = ''
    optimizationTarget.value = ''
}

// 保存表单
const saveForm = () => {
    console.log('保存数据:', {
        planningData,
        scenarioName: scenarioName.value,
        scenarioRemark: scenarioRemark.value,
        scenarioParams,
        constraints,
        constraintVariable: constraintVariable.value,
        constraintCondition: constraintCondition.value,
        constraintValue: constraintValue.value,
        optimizationTarget: optimizationTarget.value
    })
}
</script>

<style scoped lang="scss">
.scenario-view {
    padding: 20px;
    min-height: calc(100vh - 140px);
}

.content-wrapper {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: stretch;
}

.left-panel {
    flex: 1;
    max-width: 45%;
    background: #0A1A12;
    border: 1px solid rgba(134,251,139,.85);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-panel {
    flex: 1;
    max-width: 55%;
    background: #0A1A12;
    border: 1px solid rgba(134,251,139,.85);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section {
    margin-bottom: 0;
}

.section-title {
    color: #86FB8B;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    text-align: center;
}

// 规划安排表格
.data-table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
        padding: 12px 8px;
        text-align: center;
        border-bottom: 1px solid rgba(134, 251, 139, 0.35);
    }
    
    th {
        background: rgba(134, 251, 139, 0.16);
        color: #86FB8B;
        font-weight: 600;
    }
    
    td {
        color: #ffffff;
    }
}

// 表单组件
.form-group {
    margin-bottom: 20px;
    
    label {
        display: block;
        color: #86FB8B;
        margin-bottom: 8px;
        font-weight: 500;
    }
}

.input-field, .textarea-field, .select-field {
    width: 100%;
    padding: 10px 12px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid #86FB8B;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
    
    &::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
    
    &:focus {
        outline: none;
        border-color: #86FB8B;
        box-shadow: 0 0 8px rgba(134, 251, 139, 0.3);
    }
}

.textarea-field {
    min-height: 80px;
    resize: vertical;
}

// 场景参数网格
.params-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

.param-item {
    label {
        display: block;
        color: #86FB8B;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 500;
    }
}

.param-input {
    display: flex;
    align-items: stretch;
    gap: 0;
    
    .input-field {
        flex: 1;
        min-width: 60px;
        max-width: 80px;
        height: 32px;
        border: 1px solid #86FB8B;
        border-radius: 4px 0 0 4px;
        border-right: none;
        background: rgba(0, 0, 0, 0.3);
        color: #ffffff;
        padding: 0 8px;
        font-size: 14px;
        text-align: left;
        
        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        &:focus {
            outline: none;
            border-color: #86FB8B;
            box-shadow: 0 0 8px rgba(134, 251, 139, 0.3);
        }
    }
    
    .unit-box {
        width: 32px;
        height: 32px;
        background: #86FB8B;
        color: #092C18;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 4px 4px 0;
        font-weight: 600;
        font-size: 12px;
        flex-shrink: 0;
        border: 1px solid #86FB8B;
        border-left: none;
    }
}

// 约束条件网格
.constraint-grid {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
}

.constraint-item {
    flex: 1;
    min-width: 0;
    
    label {
        display: block;
        color: #86FB8B;
        margin-bottom: 8px;
        font-size: 14px;
        text-align: left;
    }
}

.toggle-group {
    display: flex;
    gap: 0;
}

.toggle-btn {
    flex: 1;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid #86FB8B;
    color: #86FB8B;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:first-child {
        border-radius: 4px 0 0 4px;
        border-right: none;
    }
    
    &:last-child {
        border-radius: 0 4px 4px 0;
        border-left: none;
    }
    
    &.active {
        background: #86FB8B;
        color: #092C18;
    }
    
    &:hover:not(.active) {
        background: rgba(134, 251, 139, 0.2);
    }
}

// 约束条件表格
.condition-section, .target-section {
    .subsection-title {
        color: #86FB8B;
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 15px 0;
    }
}

.condition-table {
    .table-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
        
        .header-cell {
            background: rgba(134, 251, 139, 0.16);
            color: #86FB8B;
            padding: 10px;
            text-align: center;
            font-weight: 600;
            border: 1px solid #86FB8B;
            border-radius: 4px;
        }
    }
    
    .table-body {
        .condition-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            align-items: end;
        }
    }
}

// 底部按钮
.bottom-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    padding: 20px 0;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.btn-reset {
        background: rgba(134, 251, 139, 0.1);
        color: #86FB8B;
        border: 1px solid #86FB8B;
        
        &:hover {
            background: rgba(134, 251, 139, 0.2);
        }
    }
    
    &.btn-save {
        background: #86FB8B;
        color: #092C18;
        
        &:hover {
            background: #6FEE78;
            box-shadow: 0 0 12px rgba(134, 251, 139, 0.4);
        }
    }
}

.full-width {
    width: 100%;
}
</style>

