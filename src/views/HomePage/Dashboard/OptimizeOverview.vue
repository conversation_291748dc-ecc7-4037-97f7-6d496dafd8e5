<template>
  <div class="main-content">
    <!-- 左侧：参数与典型场景输入 -->
    <div class="left-panel">
      <div class="data-settings">
        <h3 class="section-title">模型参数设置</h3>
        <div class="data-grid">
          <div class="data-item">
            <div class="data-label">煤电单位成本</div>
            <div class="data-row"><span class="data-key">投资</span><span class="data-input"/></div>
            <div class="data-row"><span class="data-key">运维</span><span class="data-input"/></div>
          </div>
          <div class="data-item">
            <div class="data-label">风电单位成本</div>
            <div class="data-row"><span class="data-key">投资</span><span class="data-input"/></div>
            <div class="data-row"><span class="data-key">运维</span><span class="data-input"/></div>
          </div>
          <div class="data-item">
            <div class="data-label">光伏单位成本</div>
            <div class="data-row"><span class="data-key">投资</span><span class="data-input"/></div>
            <div class="data-row"><span class="data-key">运维</span><span class="data-input"/></div>
          </div>
          <div class="data-item">
            <div class="data-label">储能单位成本</div>
            <div class="data-row"><span class="data-key">投资</span><span class="data-input"/></div>
            <div class="data-row"><span class="data-key">运维</span><span class="data-input"/></div>
          </div>
        </div>
      </div>

      <!-- 典型场景输入区域 -->
      <div class="trend-section">
        <h3 class="section-title">典型场景输入</h3>
        <div class="scenario-input">
          <span>典型场景个数：</span>
          <input class="scenario-input__field" type="number" v-model.number="scenarioCount" min="1" max="10" />
        </div>
        <div class="trend-charts">
          <div class="chart-item">
            <div class="chart-label">风电</div>
            <div class="chart-container" ref="chart1"></div>
          </div>
          <div class="chart-item">
            <div class="chart-label">光伏</div>
            <div class="chart-container" ref="chart2"></div>
          </div>
          <div class="chart-item">
            <div class="chart-label">储能</div>
            <div class="chart-container" ref="chart3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置方案区域 -->
    <div class="right-panel">
      <div class="config-section">
        <h3 class="section-title">资源配置方案</h3>
        <div class="config-table">
          <table>
            <thead>
              <tr>
                <th>规划机组类型</th>
                <th>装机容量（原有装机+新增装机）</th>
                <th>成本</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>燃煤</td>
                <td>XXX</td>
                <td>XXX</td>
              </tr>
              <tr>
                <td>风电</td>
                <td>XXX</td>
                <td>XXX</td>
              </tr>
              <tr>
                <td>光伏</td>
                <td>XXX</td>
                <td>XXX</td>
              </tr>
              <tr>
                <td>储能</td>
                <td>XXX</td>
                <td>XXX</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- KPI 与图表并排 -->
        <div class="analytics-row">
          <div class="kpi-column">
            <div class="kpi-btn">
              <div class="kpi-title">新能源装机占比</div>
              <div class="kpi-value">XXX</div>
            </div>
            <div class="kpi-btn">
              <div class="kpi-title">总成本</div>
              <div class="kpi-value">XXX</div>
            </div>
            <div class="kpi-btn">
              <div class="kpi-title">预期碳排放指标</div>
              <div class="kpi-value">XXX</div>
            </div>
            <div class="kpi-btn">
              <div class="kpi-title">预期可靠性指标R</div>
              <div class="kpi-value">XXX</div>
            </div>
          </div>

          <!-- 装机计划图表 -->
          <div class="statistics-section">
            <h3 class="section-title">装机计划图表</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color purple"></span>
                计划装机量
              </span>
              <span class="legend-item">
                <span class="legend-color green"></span>
                现有装机量
              </span>
            </div>
            <div class="statistics-chart" ref="statisticsChart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

// 图表引用
const chart1 = ref(null)
const chart2 = ref(null)
const chart3 = ref(null)
const statisticsChart = ref(null)

// 典型场景个数
const scenarioCount = ref(6)

// 颜色与实例缓存
const colorList = ['#86FB8B', '#52D273', '#1FAA59', '#0E8A3A', '#D4FFDF', '#A3F7B5', '#68D391']
let trendCharts = []
let statisticsChartInst = null
let resizeHandler = null

const buildTrendOption = (seriesData) => ({
  grid: {
    left: '10%',
    right: '6%',
    top: '8%',
    bottom: '18%'
  },
  xAxis: {
    type: 'category',
    data: ['5', '10', '15', '20'],
    axisLine: { lineStyle: { color: '#86FB8B' } },
    axisTick: { show: false },
    axisLabel: { color: '#CFF' }
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 1,
    axisLine: { lineStyle: { color: '#86FB8B' } },
    axisTick: { show: false },
    axisLabel: { color: '#CFF' },
    splitLine: { lineStyle: { color: '#86FB8B', opacity: 0.2 } }
  },
  series: seriesData.map((data, idx) => ({
    data,
    type: 'line',
    smooth: true,
    symbol: 'none',
    lineStyle: { color: colorList[idx % colorList.length], width: 1.6, opacity: 0.9 }
  }))
})

const generateSeries = (count) => {
  return Array.from({ length: count }, () => [
    0.4 + Math.random() * 0.5,
    0.4 + Math.random() * 0.5,
    0.4 + Math.random() * 0.5,
    0.4 + Math.random() * 0.5
  ])
}

// 初始化趋势图表（多折线）
const initTrendChart = (container, seriesData) => {
  const chart = echarts.init(container)
  chart.setOption(buildTrendOption(seriesData))
  return chart
}

// 初始化统计图表（堆叠柱状）
const initStatisticsChart = (container) => {
  const chart = echarts.init(container)
  const option = {
    grid: { left: '12%', right: '6%', top: '10%', bottom: '12%' },
    xAxis: {
      type: 'category',
      data: ['火电', '风电', '光伏', '储能'],
      axisLine: { lineStyle: { color: '#86FB8B' } },
      axisTick: { show: false },
      axisLabel: { color: '#CFF' }
    },
    yAxis: {
      type: 'value',
      max: 8000,
      axisLine: { lineStyle: { color: '#86FB8B' } },
      axisTick: { show: false },
      axisLabel: { color: '#CFF' },
      splitLine: { lineStyle: { color: '#86FB8B', opacity: 0.2 } }
    },
    series: [
      {
        name: '计划装机量',
        type: 'bar',
        stack: 'total',
        data: [6000, 2000, 2400, 300],
        itemStyle: { color: '#86FB8B' },
        barWidth: 24
      },
      {
        name: '现有装机量',
        type: 'bar',
        stack: 'total',
        data: [1000, 800, 1200, 50],
        itemStyle: { color: '#1FAA59' },
        barWidth: 24
      }
    ]
  }
  chart.setOption(option)
  return chart
}

onMounted(() => {
  trendCharts = [
    chart1.value && initTrendChart(chart1.value, generateSeries(scenarioCount.value)),
    chart2.value && initTrendChart(chart2.value, generateSeries(scenarioCount.value)),
    chart3.value && initTrendChart(chart3.value, generateSeries(scenarioCount.value))
  ].filter(Boolean)

  if (statisticsChart.value) {
    statisticsChartInst = initStatisticsChart(statisticsChart.value)
  }

  resizeHandler = () => {
    trendCharts.forEach((c) => c && c.resize())
    statisticsChartInst && statisticsChartInst.resize()
  }
  window.addEventListener('resize', resizeHandler)
})

watch(scenarioCount, (n) => {
  const datasets = [generateSeries(n), generateSeries(n), generateSeries(n)]
  trendCharts.forEach((c, idx) => c && c.setOption(buildTrendOption(datasets[idx]), true))
})

onBeforeUnmount(() => {
  if (resizeHandler) window.removeEventListener('resize', resizeHandler)
  trendCharts.forEach((c) => c && c.dispose())
  if (statisticsChartInst) statisticsChartInst.dispose()
})
</script>

<style lang="scss" scoped>
.main-content {
  display: grid;
  grid-template-columns: 1.05fr 1fr;
  gap: 16px;
  padding: 16px;
  height: calc(100vh - 200px);
  min-height: 0;
  overflow: hidden;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
}

.right-panel {
  display: flex;
  min-height: 0;
}

.section-title {
  font-size: 18px;
  color: #86FB8B;
  margin: 0 0 12px 0;
  padding: 10px 12px;
  background: rgba(134, 251, 139, 0.08);
  border: 1px solid #86FB8B;
  border-radius: 6px;
  text-align: center;
  box-shadow: inset 0 0 12px rgba(134, 251, 139, 0.18);
}

.data-settings {
  background: #092C18;
  border: 1px solid #86FB8B;
  border-radius: 10px;
  padding: 16px;
  box-shadow: inset 0 0 0 1px rgba(134, 251, 139, 0.12), 0 0 18px rgba(9, 44, 24, 0.6);

  .data-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;

    .data-item {
      display: grid;
      grid-template-rows: auto auto auto;
      gap: 8px;
      padding: 12px;
      background: linear-gradient(180deg, rgba(134, 251, 139, 0.08), rgba(9, 44, 24, 0.5));
      border: 1px solid #86FB8B;
      border-radius: 8px;
      box-shadow: inset 0 0 14px rgba(134, 251, 139, 0.12);

      .data-label {
        font-size: 14px;
        color: #E8FFFF;
        text-align: center;
      }

      .data-row {
        display: grid;
        grid-template-columns: 48px 1fr;
        gap: 8px;
        align-items: center;

        .data-key {
          color: #86FB8B;
          font-size: 12px;
          text-align: left;
        }

        .data-input {
          height: 26px;
          background: rgba(134, 251, 139, 0.12);
          border: 1px solid #86FB8B;
          border-radius: 4px;
          display: block;
        }
      }
    }
  }
}

.trend-section {
  flex: 1;
  background: #092C18;
  border: 1px solid #86FB8B;
  border-radius: 10px;
  padding: 16px;
  box-shadow: inset 0 0 0 1px rgba(134, 251, 139, 0.12), 0 0 18px rgba(9, 44, 24, 0.6);
  display: flex;
  flex-direction: column;
  min-height: 0;

  .scenario-input {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    margin-bottom: 8px;
    color: #CFF;
    font-size: 12px;

    .scenario-input__field {
      width: 80px;
      height: 26px;
      padding: 0 8px;
      color: #d4ffdf;
      background: rgba(134, 251, 139, 0.08);
      border: 1px solid #86FB8B;
      border-radius: 4px;
      outline: none;
    }
  }

  .trend-charts {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
    min-height: 0;

    .chart-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .chart-label {
        font-size: 12px;
        color: #86FB8B;
        margin-bottom: 6px;
        text-align: center;
      }

      .chart-container {
        flex: 1;
        background: rgba(9, 44, 24, 0.5);
        border: 1px solid #86FB8B;
        border-radius: 6px;
        min-height: 86px;
      }
    }
  }
}

.config-section {
  width: 100%;
  background: #092C18;
  border: 1px solid #86FB8B;
  border-radius: 10px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: inset 0 0 0 1px rgba(134, 251, 139, 0.12), 0 0 18px rgba(9, 44, 24, 0.6);
  min-height: 0;
}

.config-table {
  background: rgba(9, 44, 24, 0.5);
  border: 1px solid #86FB8B;
  border-radius: 6px;
  overflow: hidden;

  table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 10px;
      text-align: center;
      border-bottom: 1px solid rgba(134, 251, 139, 0.35);
      font-size: 14px;
    }

    th {
      background: rgba(134, 251, 139, 0.16);
      color: #86FB8B;
      font-weight: 600;
    }

    td {
      color: #E8FFFF;
    }

    tr:last-child td {
      border-bottom: none;
    }
  }
}

.analytics-row {
  display: grid;
  grid-template-columns: 340px 1fr;
  gap: 16px;
}

.kpi-column {
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  gap: 12px;

  .kpi-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 12px;
    background: linear-gradient(180deg, rgba(134, 251, 139, 0.14), rgba(9, 44, 24, 0.5));
    border: 1px solid #86FB8B;
    border-radius: 10px;
    color: #CFF;
    text-align: center;
    box-shadow: inset 0 0 12px rgba(134, 251, 139, 0.16);

    .kpi-title {
      font-size: 14px;
      margin-bottom: 6px;
    }

    .kpi-value {
      font-size: 18px;
      color: #86FB8B;
      font-weight: 600;
    }
  }
}

.statistics-section {
  display: flex;
  flex-direction: column;
  min-height: 0;

  .chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 12px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #E8FFFF;

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;

        &.purple { background: #8B2FE3; }
        &.green { background: #2EE683; }
      }
    }
  }

  .statistics-chart {
    flex: 1;
    background: rgba(9, 44, 24, 0.5);
    border: 1px solid #86FB8B;
    border-radius: 6px;
    min-height: 220px;
    overflow: hidden;
  }
}

@media (max-width: 1400px) {
  .data-settings .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    height: auto;
  }
  .analytics-row {
    grid-template-columns: 1fr;
  }
}
</style>


