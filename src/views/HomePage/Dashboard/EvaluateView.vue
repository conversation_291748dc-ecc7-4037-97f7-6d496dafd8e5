<template>
  <div class="evaluate-view">
    <div class="two-col">
      <div class="left-col">
        <div class="panel pie-panel">
          <div class="panel-title">现有装机结构</div>
          <div class="pie-wrap">
            <div ref="pieRef" class="pie"></div>
            <div class="legend">
              <span><i class="c c1"></i>火电机组</span>
              <span><i class="c c2"></i>风电机组</span>
              <span><i class="c c3"></i>光伏机组</span>
              <span><i class="c c4"></i>储能机组</span>
            </div>
          </div>
        </div>
        <div class="panel bar-panel">
          <div class="panel-title">待评价资源配置方案</div>
          <div ref="barRef" class="bar"></div>
        </div>
      </div>
      <div class="right-col">
        <div class="panel result-panel">
          <div class="panel-title">配置方案评价结果</div>
          <div class="result-grid">
            <div class="result-cell">
              <div class="cell-title">规划机组类型</div>
              <div class="cell-body"></div>
            </div>
            <div class="result-cell">
              <div class="cell-title">可再生能源渗透水平指标</div>
              <div class="cell-body"></div>
            </div>
            <div class="result-cell">
              <div class="cell-title">可再生能源消纳能力指标</div>
              <div class="cell-body"></div>
            </div>
            <div class="result-cell">
              <div class="cell-title">可靠性与保供能力指标</div>
              <div class="cell-body"></div>
            </div>
            <div class="result-cell">
              <div class="cell-title">资源配置方案</div>
              <div class="cell-body"></div>
            </div>
            <div class="result-cell">
              <div class="cell-title">评价结果</div>
              <div class="cell-body"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const pieRef = ref(null)
const barRef = ref(null)

let pieInst = null
let barInst = null
let resizeHandler = null

onMounted(() => {
  if (pieRef.value) pieInst = initPie()
  if (barRef.value) barInst = initBar()
  resizeHandler = () => { pieInst && pieInst.resize(); barInst && barInst.resize() }
  window.addEventListener('resize', resizeHandler)
})

onBeforeUnmount(() => {
  if (resizeHandler) window.removeEventListener('resize', resizeHandler)
  pieInst && pieInst.dispose(); pieInst = null
  barInst && barInst.dispose(); barInst = null
})

function initPie(){
  const chart = echarts.init(pieRef.value)
  chart.setOption({
    tooltip: { trigger: 'item' },
    series:[{
      type:'pie',
      radius:['44%','70%'],
      label:{ color:'#c7ffef' },
      itemStyle:{ borderColor:'#021515', borderWidth:2 },
      data:[
        { value:6900, name:'火电机组' },
        { value:2178, name:'风电机组' },
        { value:3731, name:'光伏机组' },
        { value:69, name:'储能机组' },
      ]
    }],
    color:['#86FB8B','#52D273','#1FAA59','#0E8A3A']
  })
  return chart
}

function initBar(){
  const chart = echarts.init(barRef.value)
  chart.setOption({
    grid:{ left:48, right:20, top:36, bottom:30 },
    tooltip:{ trigger:'axis' },
    xAxis:{ type:'category', data:['S1','S2','S3'], axisLabel:{ color:'#d4ffdf' }, axisLine:{ lineStyle:{ color:'#86FB8B' } }, axisTick:{ show:false } },
    yAxis:{ type:'value', axisLabel:{ color:'#d4ffdf' }, axisLine:{ show:true, lineStyle:{ color:'#86FB8B' } }, axisTick:{ show:false }, splitLine:{ lineStyle:{ color:'rgba(134,251,139,.18)' } } },
    legend:{ data:['火电机组','风电机组','光伏机组','储能机组'], textStyle:{ color:'#d4ffdf' } },
    series:[
      { name:'火电机组', type:'bar', stack:'sum', data:[1800,1750,1600], itemStyle:{ color:'#86FB8B' }, barWidth:22 },
      { name:'风电机组', type:'bar', stack:'sum', data:[700,650,620], itemStyle:{ color:'#52D273' }, barWidth:22 },
      { name:'光伏机组', type:'bar', stack:'sum', data:[1200,1100,900], itemStyle:{ color:'#1FAA59' }, barWidth:22 },
      { name:'储能机组', type:'bar', stack:'sum', data:[100,120,140], itemStyle:{ color:'#0E8A3A' }, barWidth:22 },
    ]
  })
  return chart
}
</script>

<style scoped lang="scss">
.evaluate-view{ padding: 12px 16px 16px; height: calc(100vh - 140px); box-sizing: border-box; }
.panel{ background: #0A1A12; border:1px solid rgba(134,251,139,.85); border-radius: 10px; padding: 12px; color:#d1ffe6; box-shadow: inset 0 0 0 1px rgba(134,251,139,.08), 0 0 12px rgba(6,23,17,.45); display:flex; flex-direction:column; }
.panel-title{ color:#7BEA80; font-weight:700; margin-bottom:10px; text-align:center; background:rgba(123,234,128,.08); border:1px solid rgba(134,251,139,.85); border-radius:6px; padding:8px; box-shadow: inset 0 0 8px rgba(134,251,139,.12); }
.two-col{ display:grid; grid-template-columns: 45% 55%; gap:16px; height:100%; }
.left-col{ display:grid; grid-template-rows: 1fr 1fr; gap:16px; height:100%; }
.right-col{ display:flex; height:100%; }
.pie{ flex:1; min-height: 240px; background: rgba(6,23,17,.65); border:1px solid rgba(134,251,139,.75); border-radius:6px; }
.pie-wrap{ position:relative; }
.legend{ position:absolute; right:8px; top:50%; transform:translateY(-50%); display:flex; flex-direction:column; gap:10px; font-size:12px; background: rgba(6,23,17,.65); border:1px dashed rgba(134,251,139,.28); padding:8px 10px; border-radius:6px; }
.legend .c{ display:inline-block; width:10px; height:10px; margin-right:6px; border-radius:2px; }
.legend .c1{ background:#86FB8B; }.legend .c2{ background:#52D273; }.legend .c3{ background:#1FAA59; }.legend .c4{ background:#0E8A3A; }
.table-header, .table-row{ display:grid; grid-template-columns: 1fr 1fr 1fr 1fr; }
.table-header{ background: rgba(134,251,139,.10); padding:8px; border-radius:4px; margin-bottom:6px; }
.table-row{ padding:8px; border-bottom:1px dashed rgba(0,255,247,.2); }
.rank-row{ display:grid; grid-template-columns: auto 1fr auto 1fr; gap:8px; align-items:center; margin-top:10px; }
.rank-box{ min-height: 28px; border:1px solid #86FB8B; border-radius:4px; display:flex; align-items:center; justify-content:center; gap:8px; }
.rank-box .chip{ border:1px solid #86FB8B; color:#092C18; background:#86FB8B; border-radius:12px; padding:2px 10px; font-size:12px; }
.rank-box .chip.first{ box-shadow: 0 0 10px rgba(0,255,247,.5); }
.rank-box .sep{ opacity:.6; }
.final{ text-align:right; color:#d4ffdf; margin-top:16px; font-weight:700; }
.bar{ flex:1; min-height: 240px; background: rgba(6,23,17,.65); border:1px solid rgba(134,251,139,.75); border-radius:6px; }
.kpi-panel{ display:flex; flex-direction:column; }
.kpi-cards{ display:grid; grid-template-columns: repeat(3, 1fr); gap:10px; margin-bottom:10px; }
.kpi-card{ background: rgba(134,251,139,.08); border:1px solid #86FB8B; border-radius:6px; padding:8px 10px; display:flex; align-items:center; justify-content:space-between; }
.kpi-label{ color:#c7ffef; font-size:12px; }
.kpi-value{ color:#fff; font-weight:700; }
.kpi-placeholder{ flex:1; background: rgba(0,0,0,.25); border:1px dashed rgba(0,255,247,.25); border-radius:6px; }

/* 右侧 3x2 小模块网格 */
.result-panel{ flex:1; display:flex; flex-direction:column; height:100%; }
.result-grid{ flex:1; display:grid; grid-template-columns: repeat(3,1fr); grid-template-rows: repeat(2, 1fr); gap:12px; min-height:0; }
.result-cell{ display:flex; flex-direction:column; }
.cell-title{ text-align:center; color:#d1ffe6; font-weight:600; background:rgba(123,234,128,.08); border:1px solid rgba(134,251,139,.85); border-bottom:none; padding:6px; border-radius:6px 6px 0 0; }
.cell-body{ flex:1; background: rgba(6,23,17,.6); border:1px dashed rgba(134,251,139,.25); border-radius:0 0 6px 6px; }
</style>


