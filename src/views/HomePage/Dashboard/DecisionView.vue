<template>
  <div class="decision-view">
    <div class="panel pie-panel">
      <div class="panel-title">现有装机结构</div>
      <div ref="pieRef" class="pie"></div>
      <div class="legend">
        <span><i class="c c1"></i>火电机组</span>
        <span><i class="c c2"></i>风电机组</span>
        <span><i class="c c3"></i>光伏机组</span>
        <span><i class="c c4"></i>储能机组</span>
      </div>
    </div>
    <div class="panel settings-panel">
      <div class="panel-title">模型推演设置</div>
      <div class="switch-cards">
        <div class="switch-card">
          <div class="switch-card-title">是否考虑负荷变化</div>
          <div class="options">
            <div class="opt" :class="{ active: toggles.load }" @click="setToggle('load', true)"><span class="tick">✓</span>是</div>
            <div class="opt" :class="{ active: !toggles.load }" @click="setToggle('load', false)"><span class="tick">✓</span>否</div>
          </div>
        </div>
        <div class="switch-card">
          <div class="switch-card-title">是否考虑新能源入市变化</div>
          <div class="options">
            <div class="opt" :class="{ active: toggles.market }" @click="setToggle('market', true)"><span class="tick">✓</span>是</div>
            <div class="opt" :class="{ active: !toggles.market }" @click="setToggle('market', false)"><span class="tick">✓</span>否</div>
          </div>
        </div>
        <div class="switch-card">
          <div class="switch-card-title">是否考虑技术成本变化</div>
          <div class="options">
            <div class="opt" :class="{ active: toggles.cost }" @click="setToggle('cost', true)"><span class="tick">✓</span>是</div>
            <div class="opt" :class="{ active: !toggles.cost }" @click="setToggle('cost', false)"><span class="tick">✓</span>否</div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel bar-panel">
      <div class="panel-title">待推演资源配置方案</div>
      <div ref="barRef" class="bar"></div>
    </div>
    <div class="panel result-panel">
      <div class="panel-title">配置方案评价结果</div>
      <div class="table-header"><div></div><div>S1</div><div>S2</div><div>S3</div></div>
      <div class="table-row"><div>投资运营成本（亿元）</div><div>XXX</div><div>XXX</div><div>XXX</div></div>
      <div class="table-row"><div>失负荷风险（万千瓦）</div><div>XXX</div><div>XXX</div><div>XXX</div></div>
      <div class="rank-row">
        <div>经济性排序：</div><div class="rank-box"></div>
        <div>可行性排序：</div><div class="rank-box"></div>
      </div>
      <div class="final">《XX方案指标综合最优》</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const pieRef = ref(null)
const barRef = ref(null)

const toggles = reactive({ load: true, market: true, cost: true })
function setToggle(key, value){ toggles[key] = value }

let pieInst = null
let barInst = null
let resizeHandler = null

onMounted(() => {
  if (pieRef.value) pieInst = initPie()
  if (barRef.value) barInst = initBar()
  resizeHandler = () => { pieInst && pieInst.resize(); barInst && barInst.resize() }
  window.addEventListener('resize', resizeHandler)
})

onBeforeUnmount(() => {
  if (resizeHandler) window.removeEventListener('resize', resizeHandler)
  pieInst && pieInst.dispose(); pieInst = null
  barInst && barInst.dispose(); barInst = null
})

function initPie(){
  const chart = echarts.init(pieRef.value)
  chart.setOption({
    tooltip:{ trigger:'item' },
    series:[{ type:'pie', radius:['44%','70%'], label:{ color:'#c7ffef' }, itemStyle:{ borderColor:'#021515', borderWidth:2 }, data:[
      { value:6900, name:'火电机组' },
      { value:2178, name:'风电机组' },
      { value:3731, name:'光伏机组' },
      { value:69, name:'储能机组' },
    ]}],
    color:['#86FB8B','#52D273','#1FAA59','#0E8A3A']
  })
  return chart
}

function initBar(){
  const chart = echarts.init(barRef.value)
  chart.setOption({
    grid:{ left:48, right:20, top:36, bottom:30 },
    tooltip:{ trigger:'axis' },
    xAxis:{ type:'category', data:['S1','S2','S3'], axisLabel:{ color:'#d4ffdf' }, axisLine:{ lineStyle:{ color:'#86FB8B' } }, axisTick:{ show:false } },
    yAxis:{ type:'value', axisLabel:{ color:'#d4ffdf' }, axisLine:{ show:true, lineStyle:{ color:'#86FB8B' } }, axisTick:{ show:false }, splitLine:{ lineStyle:{ color:'rgba(134,251,139,.18)' } } },
    legend:{ data:['火电机组','风电机组','光伏机组','储能机组'], textStyle:{ color:'#d4ffdf' } },
    series:[
      { name:'火电机组', type:'bar', stack:'sum', data:[1800,1750,1600], itemStyle:{ color:'#86FB8B' }, barWidth:22 },
      { name:'风电机组', type:'bar', stack:'sum', data:[700,650,620], itemStyle:{ color:'#52D273' }, barWidth:22 },
      { name:'光伏机组', type:'bar', stack:'sum', data:[1200,1100,900], itemStyle:{ color:'#1FAA59' }, barWidth:22 },
      { name:'储能机组', type:'bar', stack:'sum', data:[100,120,140], itemStyle:{ color:'#0E8A3A' }, barWidth:22 },
    ]
  })
  return chart
}
</script>

<style scoped lang="scss">
.decision-view{ padding: 12px 16px 16px; display:grid; grid-template-columns: 1fr 1fr; grid-template-rows: auto auto; gap:16px; align-items:stretch; }
.panel{ background: #0A1A12; border:1px solid rgba(134,251,139,.85); border-radius: 10px; padding: 12px; color:#d1ffe6; box-shadow: inset 0 0 0 1px rgba(134,251,139,.08), 0 0 12px rgba(6,23,17,.45); }
.panel-title{ color:#7BEA80; font-weight:700; margin-bottom:10px; text-align:center; background:rgba(123,234,128,.08); border:1px solid rgba(134,251,139,.85); border-radius:6px; padding:8px; box-shadow: inset 0 0 8px rgba(134,251,139,.12); }
/* 让右侧两块面板在各自网格行内拉伸占满高度 */
.settings-panel, .result-panel{ height:100%; display:flex; flex-direction:column; }

/* 设置卡片：是/否上下结构 */
.switch-cards{ display:grid; grid-template-columns: repeat(3, 1fr); gap:12px; margin-bottom: 8px; }
.switch-card{ background: rgba(123,234,128,.06); border:1px solid rgba(134,251,139,.85); border-radius:8px; padding:10px; box-shadow: inset 0 0 8px rgba(134,251,139,.12); display:flex; flex-direction:column; }
.switch-card-title{ color:#d4ffdf; font-weight:600; margin-bottom:8px; text-align:center; }
.options{ display:flex; flex-direction:column; gap:8px; }
.opt{ display:flex; align-items:center; justify-content:center; gap:6px; padding:8px 10px; border:1px solid rgba(134,251,139,.85); border-radius:6px; color:#86FB8B; background:transparent; cursor:pointer; transition:.2s; }
.opt .tick{ opacity:0; }
.opt.active{ background:#86FB8B; color:#092C18; }
.opt.active .tick{ opacity:1; }

.result-panel .table-header, .result-panel .table-row{ display:grid; grid-template-columns: 1fr 1fr 1fr 1fr; }
.result-panel .table-header{ background: rgba(123,234,128,.10); padding:8px; border-radius:4px; margin-bottom:6px; }
.result-panel .table-row{ padding:8px; border-bottom:1px dashed rgba(0,255,247,.2); }
.rank-row{ display:grid; grid-template-columns: auto 1fr auto 1fr; gap:8px; align-items:center; margin-top:10px; }
.rank-box{ height: 28px; border:1px solid #86FB8B; border-radius:4px; }
.final{ text-align:right; color:#fff; margin-top:16px; font-weight:700; }

/* 左侧图表样式 */
.pie{ height: 240px; }
.legend{ display:flex; gap:16px; justify-content:center; margin-top:8px; font-size:12px; }
.legend .c{ display:inline-block; width:10px; height:10px; margin-right:6px; border-radius:2px; }
.legend .c1{ background:#86FB8B; }.legend .c2{ background:#52D273; }.legend .c3{ background:#1FAA59; }.legend .c4{ background:#0E8A3A; }
.bar{ height: 260px; background: rgba(6,23,17,.6); border:1px solid rgba(134,251,139,.75); border-radius:6px; }

/* 网格定位：左上饼图、右上设置、左下柱图、右下结果，使左右总高度对齐 */
.pie-panel{ grid-column: 1 / 2; grid-row: 1 / 2; }
.settings-panel{ grid-column: 2 / 3; grid-row: 1 / 2; }
.bar-panel{ grid-column: 1 / 2; grid-row: 2 / 3; }
.result-panel{ grid-column: 2 / 3; grid-row: 2 / 3; display:flex; flex-direction:column; }

@media (max-width: 1200px){
  .decision-view{ grid-template-columns: 1fr; grid-template-rows:auto; }
  .pie-panel,.settings-panel,.bar-panel,.result-panel{ grid-column:auto; grid-row:auto; }
}
</style>


