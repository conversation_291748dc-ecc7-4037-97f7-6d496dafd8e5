<template>
    <div class="main_content3_index2 point_bg">
        <div class="point_content flex justify-between">
            <div class="left_content relative">
                <!-- <div class="text_title">
                        <p>{{calTitle1}}</p>
                        <p>{{calTitle2}}</p>
                </div> -->
                <div class="map_kanban" >
                    <p class="relative">电力平衡简表
                        <right-square-outlined  v-if="!state.initialShow&&state.isCalculate" @click="state.initialShow=true;" />
                        <left-square-outlined v-else-if="state.initialShow&&state.isCalculate" @click="state.initialShow=false" />
                    </p>
                    <div>
                        <div class="bottom_white">
                            <p>{{state.partition=='全省'?'全省':'分区'}}负荷</p>
                            <span>{{fixInteger(load,0)}}</span>
                            <div class="slider_btn">
                                <caret-right-outlined v-if="state.sliderName!='load'" @click="state.sliderName='load';closeOtherModal()" />
                                <caret-left-outlined v-else @click="state.sliderName=''" />
                            </div>
                        </div>
                        <div>
                            <p>燃煤机组</p>
                            <span>{{coal>7800? 7800 :fixInteger(coal,0)}}</span>
                            <span class="span_small" v-if="coal>7800">(+{{ fixInteger(+coal-7800,0)}})</span>
                        </div>
                        <div>
                            <p>燃气机组</p>
                            <span>{{fixInteger(gas,0)}}</span>
                            <div class="slider_btn" v-if="gasRange.max!=0">
                                <caret-right-outlined v-if="state.sliderName!='gas'" @click="state.sliderName='gas';closeOtherModal()" />
                                <caret-left-outlined v-else @click="state.sliderName=''" />
                            </div>
                        </div>
                        <div>
                            <p>水电机组</p>
                            <span>{{fixInteger(hydro_output,0)}}</span>
                        </div>
                        <div>
                            <p>{{state.partition=='全省'?'区外直流':'区外受电'}}</p>
                            <span>{{state.partition=='全省'?fixInteger(feedin1+feedin2+feedin3+feedin4,fixInteger):fixInteger(feedin,0)}}</span>
                            <div class="slider_btn" v-if="state.partition=='全省'">
                                <caret-right-outlined v-if="state.sliderName!='feedin'" @click="state.sliderName='feedin';closeOtherModal()" />
                                <caret-left-outlined v-else @click="state.sliderName=''" />
                            </div>
                            <!-- <div class="check_btn" v-if="state.partition=='全省'">
                                <a-checkbox :disabled="!state.mapChecked1" v-model:checked="state.isShowFeedin">
                                </a-checkbox>
                            </div> -->
                        </div>
                        <div>
                            <p>风电出力</p>
                            <span>{{fixInteger(wind,0)}}</span>
                            <div class="slider_btn" v-if="windRange.max!=0">
                                <caret-right-outlined v-if="state.sliderName!='wind'" @click="state.sliderName='wind';closeOtherModal()" />
                                <caret-left-outlined v-else @click="state.sliderName=''" />
                            </div>
                        </div>
                        <div class="bottom_white">
                            <p>光伏出力</p>
                            <span>{{fixInteger(solar,0)}}</span>
                            <div class="slider_btn" v-if="solarRange.max!=0">
                                <caret-right-outlined v-if="state.sliderName!='solar'" @click="state.sliderName='solar';closeOtherModal()" />
                                <caret-left-outlined v-else @click="state.sliderName=''" />
                            </div>
                        </div>
                        <div>
                            <p>供电裕度</p>
                            <span>{{fixInteger(indicatorsData.power_supply_margin,0)}}</span>
                        </div>
                    </div>
                </div>
                <div class="load_line" v-show="state.partition=='全省'">
                    <p>{{'当日负荷曲线'}}</p>
                    <div ref="line">
    
                    </div>
                </div>
                <!-- <div class="load_line" v-show="state.partition=='全省'">
                    <p>{{'当日新能源曲线'}}</p>
                    <div ref="lines">
    
                    </div>
                </div> -->
                <div class="map_kanban_initial absolute" v-if="state.initialShow">
                    <p>原值</p>
                    <div>
                        <div class="bottom_white">
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.load:state.contrastType=='base'? baseCaseData.initialData.load :referCaseData.initialData.load,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.coal:state.contrastType=='base'? baseCaseData.initialData.coal :referCaseData.initialData.coal,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.gas:state.contrastType=='base'? baseCaseData.initialData.gas :referCaseData.initialData.gas,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.hydro_output:state.contrastType=='base'? baseCaseData.initialData.hydro_output :referCaseData.initialData.hydro_output,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.feedin:state.contrastType=='base'? baseCaseData.initialData.feedin :referCaseData.initialData.feedin,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.wind:state.contrastType=='base'? baseCaseData.initialData.wind :referCaseData.initialData.wind,0)}}</span>
                        </div>
                        <div class="bottom_white">
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.solar:state.contrastType=='base'? baseCaseData.initialData.solar :referCaseData.initialData.solar,0)}}</span>
                        </div>
                        <div>
                            <span>{{fixInteger(state.contrastType=='pre'? preCaseData.initialData.power_supply_margin:state.contrastType=='base'? baseCaseData.initialData.power_supply_margin :referCaseData.initialData.power_supply_margin,0)}}</span>
                        </div>
                    </div>
                </div>
                <div class="map_slider load_slider" v-if="state.sliderName=='load'">
                    <close-circle-outlined @click="state.sliderName=undefined"/>
                    <div class="map_slider_text1">
                        {{loadRange.min}}
                    </div>
                    <div class="map_slider_text2">
                        {{loadRange.max}}
                    </div>
                    <a-slider @afterChange="sliderChange" :tooltipOpen="true" v-model:value="load"  :step="1" :min="loadRange.min" :max="loadRange.max">
                        <template>
                            <p>{{ label }}</p>
                        </template>
                    </a-slider>
                </div> 
                <div class="map_slider gas_slider"  v-if="state.sliderName=='gas'">
                    <close-circle-outlined @click="state.sliderName=undefined"/>
                    <div class="map_slider_text1">
                        {{gasRange.min}}
                    </div>
                    <div class="map_slider_text2">
                        {{gasRange.max}}
                    </div>
                    <a-slider @afterChange="sliderChange" :tooltipOpen="true"  v-model:value="gas"  :step="1" :min="gasRange.min" :max="gasRange.max">
                        <template>
                            <p>{{ label }}</p>
                        </template>
                    </a-slider>
                </div> 
                <div class="map_slider_list feedin_slider"  v-if="state.sliderName=='feedin'">
                    <close-circle-outlined @click="state.sliderName=undefined"/>
                    <div>
                        <p>中天</p>
                        <div>
                            <div class="map_slider_text1">
                                {{feedinRange1.min}}
                            </div>
                            <div class="map_slider_text2">
                                {{feedinRange1.max}}
                            </div>
                            <a-slider @afterChange="sliderChange"  :tooltipOpen="true"  v-model:value="feedin1"  :step="1" :min="feedinRange1.min" :max="feedinRange1.max">
                                <template>
                                    <p>{{ label }}</p>
                                </template>
                            </a-slider>
                        </div>
                    </div>
                    <div>
                        <p>灵宝</p>
                        <div>
                            <div class="map_slider_text1">
                                {{feedinRange2.min}}
                            </div>
                            <div class="map_slider_text2">
                                {{feedinRange2.max}}
                            </div>
                            <a-slider @afterChange="sliderChange"  :tooltipOpen="true"  v-model:value="feedin2"  :step="1" :min="feedinRange2.min" :max="feedinRange2.max">
                                <template>
                                    <p>{{ label }}</p>
                                </template>
                            </a-slider>
                        </div>
                    </div>
                    <!-- <div>
                        <p>陕豫</p>
                        <div>
                            <div class="map_slider_text1">
                                {{feedinRange3.min}}
                            </div>
                            <div class="map_slider_text2">
                                {{feedinRange3.max}}
                            </div>
                            <a-slider @afterChange="sliderChange"  :tooltipOpen="true"  v-model:value="feedin3"  :step="1" :min="feedinRange3.min" :max="feedinRange3.max">
                                <template>
                                    <p>{{ label }}</p>
                                </template>
                            </a-slider>
                        </div>
                    </div> -->
                    <div>
                        <p>青豫</p>
                        <div>
                            <div class="map_slider_text1">
                                {{feedinRange3.min}}
                            </div>
                            <div class="map_slider_text2">
                                {{feedinRange3.max}}
                            </div>
                            <a-slider @afterChange="sliderChange"  :tooltipOpen="true"  v-model:value="feedin3"  :step="1" :min="feedinRange3.min" :max="feedinRange3.max">
                                <template>
                                    <p>{{ label }}</p>
                                </template>
                            </a-slider>
                        </div>
                    </div>
                </div> 
                <div class="map_slider wind_slider" v-if="state.sliderName=='wind'">
                    <close-circle-outlined @click="state.sliderName=undefined"/>
                    <div class="map_slider_text1">
                        {{windRange.min}}
                    </div>
                    <div class="map_slider_text2">
                        {{windRange.max}}
                    </div>
                    <a-slider @afterChange="sliderChange" :tooltipOpen="true"  v-model:value="wind"  :step="1" :min="windRange.min" :max="windRange.max">
                        <template>
                            <p>{{ label }}</p>
                        </template>
                    </a-slider>
                </div> 
                <div class="map_slider solar_slider" v-if="state.sliderName=='solar'">
                    <close-circle-outlined @click="state.sliderName=undefined"/>
                    <div class="map_slider_text1">
                        {{solarRange.min}}
                    </div>
                    <div class="map_slider_text2">
                        {{solarRange.max}}
                    </div>
                    <a-slider @afterChange="sliderChange" :tooltipOpen="true"  v-model:value="solar"  :step="1" :min="solarRange.min" :max="solarRange.max">
                        <template>
                            <p>{{ label }}</p>
                        </template>
                    </a-slider>
                </div> 
                <div class="time_select">
                    <a-date-picker :showNow="false" @change="changeTime" v-model:value="state.time" picker="date" :disabled-date="disabledDate" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" :show-time="{ format: 'HH' }" :allowClear="false"/>
                </div>
                <div class="map_select absolute">
                    <a-select
                        v-model:value="state.contrastType"
                    >
                        <a-select-option value="base">基础场景</a-select-option>
                        <a-select-option value="pre">上个场景</a-select-option>
                        <a-select-option :disabled="!state.isSetReference" value="reference">基准场景</a-select-option>
                        <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                    </a-select>
                </div>
                <a-button :disabled="!state.isSliderChange" @click="start_cal" class="linear_btn1 absolute">
                    {{ state.cal_text }}
                </a-button>
                <a-button @click="reset" class="linear_btn2 absolute">
                    重置
                </a-button>
                <a-button @click="setReference" class="linear_btn3 absolute">
                    设为基准场景
                </a-button>
            </div>
            <div class="middle_content relative">
                <p class="absolute" v-if="state.partition!='全省'">{{ state.partition }}分区</p>
                <div class="map" ref="map">

                </div>
                <img @click="changeUrl('/index/FunctionModule3-1')" src="@/assets/images/calculate/map_out.png" alt="">
                <div class="map_checked">
                    <div>
                        <a-checkbox v-model:checked="state.mapChecked1">
                            主网潮流
                        </a-checkbox>
                    </div>
                    <div>
                        <!-- <a-checkbox v-model:checked="state.mapChecked2" :disabled="!state.mapChecked1">
                            供电裕度
                        </a-checkbox> -->
                         <a-radio-group v-model:value="state.mapType" button-style="solid">
                            <a-radio-button :value="1">供电裕度</a-radio-button>
                            <a-radio-button :value="2">新能源消纳</a-radio-button>
                        </a-radio-group>
                    </div>
                </div>
                <div class="map_checked map_checked_bottom">
                    <div>
                        <a-checkbox v-model:checked="state.mapChecked3">
                            动态潮流
                        </a-checkbox>
                    </div>
                </div>
                <div @contextmenu="(e)=>e.preventDefault()" class="map_calculate absolute point_bg" :style="{'left':(state.calculateLeft)+'px','top':(state.calculateTop)+'px'}" v-if="state.calculateShow">
                    <div class="point_content">
                        <close-circle-outlined @click="state.calculateShow=false;state.equipmentId=undefined" />
                        <p>设备故障推演</p>
                        <div class="map_calculate_select">
                            <a-select
                                :getPopupContainer="triggerNode=>{return triggerNode.parentNode}"
                                v-model:value="state.equipmentId"
                                :options="state.equipmentOptions"
                                placeholder="选择故障设备"
                            >
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                            </a-select>
                        </div>
                        <a-button @click="startN_1">N-1故障</a-button>
                    </div>
                </div>
            </div>
            <div class="right_content">
                <p>推演结果</p>
                <div class="partition relative" v-show="state.partition!='全省'">
                    <p>500kV主变降压功率分析</p>
                    <div class="bg_line"></div>
                    <div class="fenqu_table">
                        <div>
                            <div>名称</div>
                            <div>降压功率<br>（万千瓦）</div>
                            <div>运行限额<br>（万千瓦）</div>
                            <div>限额利用率<br>（%）</div>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.contrastPartitionList" :key="index">
                                <div>
                                    <p v-for="(items,indexs) in item.nameList">{{ items }}</p>
                                </div>
                                <div>
                                    <div v-for="(items,indexs) in item.valueList">
                                        <p>{{ fixInteger(items,1) }}</p>
                                        <img v-if="items<item.valueLists[indexs]" src="@/assets/images/calculate/top.png" alt="">
                                        <img v-if="items>item.valueLists[indexs]" src="@/assets/images/calculate/bottom.png" alt="">
                                        <p v-if="items!=item.valueLists[indexs]">{{items==item.valueLists[indexs]?'':fixInteger(item.valueLists[indexs],1)}}</p>
                                    </div>
                                </div>
                                <div>
                                    {{fixInteger(item.value2,0)}}
                                </div>
                                <div>
                                    {{fixInteger(item.value3,0)}}
                                    <img v-if="item.change3&&item.value3<item.value3s" src="@/assets/images/calculate/top.png" alt="">
                                    <img v-if="item.change3&&item.value3>item.value3s" src="@/assets/images/calculate/bottom.png" alt="">
                                    {{item.change3==0?'':fixInteger(item.value3s,0)}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <p>分区受电能力推演结果</p>
                    <div class="bg_line"></div>
                    <div class="fenquBar">
                        <div class="grid">
                            <p>供电裕度（万千瓦）</p>
                            <p>主变受电能力（万千瓦）</p>
                        </div>
                        <div ref="bar">
                            
                        </div>                
                    </div>
                    
                </div>
                <div class="province relative" v-show="state.partition=='全省'">
                    <div class="zhibiao1">
                        供电裕度<br>
                        <p :class="getColor('power_supply_margin')">{{fixInteger(indicatorsData.power_supply_margin,0)}}</p>
                    </div>
                    <div class="zhibiao2">
                        正备用容量<br>
                        <p :class="getColor('reserve_low')">{{fixInteger(indicatorsData.reserve_low,0)}}</p>
                    </div>
                    <div class="zhibiao3">
                        负备用容量<br>
                        <p :class="getColor('peak_shaving')">{{fixInteger(indicatorsData.peak_shaving,0)}}</p>
                    </div>
                    <div class="zhibiao4">
                        无惯量电源渗透率<br>
                        <p :class="getColor('non_inertia_penetration')">{{fixInteger(100*indicatorsData.non_inertia_penetration,1)+'%'}}</p>
                    </div>
                    <div class="zhibiao5">
                        外来电依存度<br>
                        <p :class="getColor('feedin_dependence')">{{fixInteger(100*indicatorsData.feedin_dependence,1)+'%'}}</p>
                    </div>
                    <div class="zhibiao6">
                        爬坡能力<br>
                        <p :class="getColor('ramp_cap_upward')">{{fixInteger(indicatorsData.ramp_cap_upward,0)}}</p>
                    </div>
                    <div class="radar" ref="radar">
                        
                    </div>
                    <div class="interface_list">
                        <p>关键断面供电/输电能力</p>
                        <div class="bg_line"></div>
                        <div>
                            <div>
                                <p>序号</p>
                                <p>断面名称</p>
                                <p>供电/输电能力</p>
                                <p>能力变化</p>
                            </div>
                            <div class="scroll">
                                <div v-for="(item,index) in state.contrastInterfaceData" :key="index">
                                    <p>{{ index+1 }}</p>
                                    <p>{{ item.name}}</p>
                                    <p>{{ fixInteger(item.power)+'/'+fixInteger(item.limit) }}</p>
                                    <p>{{ fixInteger(item.change) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="margin_list">
                        <p>分区供电裕度</p>
                        <div class="bg_line"></div>
                        <div>
                            <div>
                                <p>序号</p>
                                <p>分区名称</p>
                                <p>供电裕度</p>
                                <p>裕度变化</p>
                            </div>
                            <div class="scroll">
                                <div v-for="(item,index) in state.contrastPartitionData" :key="index">
                                    <p>{{index+1}}</p>
                                    <p>{{item.name}}</p>
                                    <p :class="item.value==0?'pointer red':item.value==2?'green':'yellow'">{{fixInteger(item.values)}}</p>
                                    <p>{{fixInteger(item.change)}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="line_list">
                        <p>500kV线路负载率列表</p>
                        <div class="bg_line"></div>
                        <div>
                            <div>
                                <p>序号</p>
                                <p>线路名称</p>
                                <p>负载率(%)</p>
                                <p>变化幅度(%)</p>
                            </div>
                            <div class="scroll">
                                <div v-for="(item,index) in state.contrastLineData" :key="index">
                                    <p>{{index+1}}</p>
                                    <p>{{item.name}}</p>
                                    <p>{{fixInteger(item.p_rate)}}</p>
                                    <p>{{fixInteger(item.change)}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import {registerMap,fixInteger} from '@/utils/common'
import { inject, onMounted } from '@vue/runtime-core'
import { markRaw, reactive, ref,toRefs } from '@vue/reactivity'
import { LoadingOutlined,CaretRightOutlined,CaretLeftOutlined,CloseCircleOutlined,LeftSquareOutlined,RightSquareOutlined } from '@ant-design/icons-vue';
import { handleData,getContrastPartitionData,geCalLineData,getContrastPartitionList,getCalMapServies,getCalMapOption,getCalRadarOption,getPartitionBar,getLoadOption,getCalInterfaceData} from '@/utils/calculate'
import { getCaseDataApi,getTimeSequence,getTrafoCapability,getDeducePowerData,getAllIndicator,getNetworkData,getBalanceData,getLaodratioTimestep } from '@/api/index'
import { h, watch } from 'vue';
import { echartsResize } from '../../config/setting.config'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
const router = useRouter()
const echarts = inject("ec");
const store = dataStore()
const  { caseId1,timeStep1,partition,loadData1,timeData1,year1 } = storeToRefs(store)
const state = reactive({
    timestep: timeStep1,
    year:year1,
    time:timeData1.value[timeStep1.value],
    timeData:timeData1,
    case_id:caseId1,
    cal_text:'开始推演',
    partition:partition,
    // partition:'全省',
    mapData:{},
    calculateShow:false,
    calculateLeft:undefined,
	calculateTop:undefined,
    equipmentId:undefined,
    calEquipmentId:[],
    conEquipmentId:[],
    mapChecked1:true,
    mapChecked2:true,
    mapChecked3:true,
    mapType:1,
    isSliderChange:false,
    isCalculate:false,
    partitionValue:[],
    sliderName:undefined,
    initialShow:false,
    contrastType:'pre',
    isSetReference:false,
    isFirstCal: true,
    interfaceList:[],
    lineList:[],
    contrastRadarData:[],
    contrastLineData:[],
    contrastInterfaceData:[],
    contrastPartitionList:[],
    contrastPartitionData:[],
    partitionData:[],
    isShowFeedin:true,
    partitionElectrify:undefined,
    trafo_details:{}
})
const indicatorsData = ref({
    power_supply_margin:1,
    non_inertia_penetration:1,
    reserve_low:1,
    feedin_dependence:1,
    peak_shaving:1,
    ramp_cap_upward:1,
})
const indicatorsRange = ref({
    power_supply_margin:[0,0,0,0,0,0,0,0],
    non_inertia_penetration:[0,0,0,0,0,0],
    reserve_low:[0,0,0,0,0,0],
    feedin_dependence:[0,0,0,0,0,0],
    peak_shaving:[0,0,0,0,0,0],
    ramp_cap_upward:[0,0,0,0,0,0],
})
const indicatorsValue = ref({})
const allEcharts = reactive({
    map:undefined,
    mapChart:undefined,
    mapOption:{},
    radar:undefined,
    radarChart:undefined,
    bar:undefined,
    barChart:undefined,
    line:undefined,
    lineChart:undefined
})
const {map,mapChart,mapOption,radar,radarChart,bar,barChart,line,lineChart} =  toRefs(allEcharts)
const BoundaryCondition = reactive({
    feedin:undefined,
    feedin1:undefined,
    feedin2:undefined,
    feedin3:undefined,
    feedin4:undefined,
    feedin5:undefined,
    feedin6:undefined,
    load:undefined,
    wind:undefined,
    solar:undefined,
    gas:undefined,
    coal:undefined,
    hydro_output: undefined,
})
const referCaseData = reactive({
    initialData:{},
    indicatorsValue:{},
    indicatorsData:{},
    partitionValue:[],
    mapDataDefault:{},
    partitionData: [],
    partitionElectrify:undefined,
})
const preCaseData = reactive({
    initialData:{},
    indicatorsValue:{},
    indicatorsData:{},
    partitionValue:[],
    mapDataDefault:{},
    partitionData:[],
    partitionElectrify:undefined,
})
const baseCaseData = reactive({
    initialData:{},
    indicatorsValue:{},
    indicatorsData:{},
    partitionValue:[],
    mapDataDefault:{},
    partitionData:[],
    partitionElectrify:undefined,
})
const cal_initialData = reactive({

})
const {feedin,load,wind,solar,gas,coal,hydro_output,feedin1,feedin2,feedin3,feedin4,feedin5,feedin6} =  toRefs(BoundaryCondition)
const BoundaryRange = reactive({
    loadRange :{
    },
    gasRange :{
    },
    windRange :{
    },
    solarRange :{
    },
    feedinRange1 :{
    },
    feedinRange2 :{
    },
    feedinRange3 :{
    },
    feedinRange4 :{
    },
    feedinRanges :{
    },
})
const {loadRange,gasRange,windRange,solarRange,feedinRange1,feedinRange2,feedinRange3,feedinRange4,feedinRanges} =  toRefs(BoundaryRange)
const getColor = (val)=>{
    if(val=='non_inertia_penetration'||val=='feedin_dependence'){
        return indicatorsData.value[val]>indicatorsRange.value[val][1]?'red':indicatorsData.value[val]<indicatorsRange.value[val][2]?'green':'yellow'
    }else{
        return indicatorsData.value[val]<indicatorsRange.value[val][1]?'red':indicatorsData.value[val]>indicatorsRange.value[val][2]?'green':'yellow'
    }
}
const changeUrl = (url) => {
  router.push({
    path:url,
  })
}
const disabledDate = (current)=>{
    const date = dayjs(current);
    return date.year() !== +state.year;
}
const closeOtherModal = ()=>{
    state.initialShow = false
    state.calculateShow = false
}
const startN_1 = async() =>{
	state.calculateShow = false
	await initAllData('N_1')
	state.conEquipmentId = state.calEquipmentId
	state.isCalculate = true
	state.isSliderChange = false
}
const changeTime = () => {
    state.timestep = timeData1.value.findIndex(item=>item==state.time)
    initAllData()
}
const sliderChange = (val)=>{
    if (['feedin'].includes(state.sliderName)) {
		initMap(true)
	}
    if(state.sliderName){
        state.isSliderChange = true
    }
    state.sliderName = undefined
}
const calculateN_1 = async (val) => {
    if (val) {
        const {qs_fenqu_psm_value,qs_fenqu_psm_color,graph_data,chart_dict,baogongliuwei_nominal,thresholds,trafo_details} = (await getDeducePowerData({
            calculate_flag:true,
            case_id: state.case_id,
            timestep:state.timestep,
            device_name:state.calEquipmentId,
            feedin: BoundaryCondition.feedin,
            filter_name: state.partition,
            gas: BoundaryCondition.gas,
            load: BoundaryCondition.load,
            wind: BoundaryCondition.wind,
            solar: BoundaryCondition.solar,
            phase_shifter_arg: {},
            area: state.partition,
            dc_feedin: state.partition == '全省' ? {
                "中天直流": BoundaryCondition.feedin1,
                "灵宝直流": BoundaryCondition.feedin2,
                "青豫直流": BoundaryCondition.feedin3,
                "长南特高压": BoundaryCondition.feedin4,
                "南荆特高压": BoundaryCondition.feedin5,
                "豫武特高压": BoundaryCondition.feedin6,
            }:undefined
        })).data
        state.mapData = graph_data
        indicatorsData.value = baogongliuwei_nominal
        if (state.partition == '全省') {
            state.partitionValue=Object.keys(qs_fenqu_psm_color).map(item=>{
                return{
                    name:item,
                    value:qs_fenqu_psm_color[item],
                    values:qs_fenqu_psm_value[item],
                }
            })
            state.interfaceList = handleData(chart_dict.interface)
            state.lineList = handleData(chart_dict.line)
            if(state.contrastType=='pre'){  
                state.contrastInterfaceData = getCalInterfaceData(state.interfaceList, true,preCaseData.interfaceList)
                state.contrastLineData = geCalLineData(state.lineList, true,preCaseData.lineList)
                state.contrastPartitionData = getContrastPartitionData(state.partitionValue,true,preCaseData.partitionValue)
            }else if(state.contrastType=='reference'){
                state.contrastPartitionData = getContrastPartitionData(state.partitionValue,true,referCaseData.partitionValue)
                state.contrastInterfaceData = getCalInterfaceData(state.interfaceList,true,referCaseData.interfaceList)
                state.contrastLineData = geCalLineData(state.lineList, true,referCaseData.lineList)
            } else if (state.contrastType == 'base') {
                state.contrastPartitionData = getContrastPartitionData(state.partitionValue,true,baseCaseData.partitionValue)
                state.contrastInterfaceData = getCalInterfaceData(state.interfaceList,true,baseCaseData.interfaceList)
                state.contrastLineData = geCalLineData(state.lineList, true,baseCaseData.lineList)
            }
        } else {
            state.trafo_details = trafo_details
            state.partitionValue = [
                {
                    name:state.partition,
                    value:baogongliuwei_nominal.power_supply_margin> thresholds.power_supply_margin[2]? 2 : baogongliuwei_nominal.power_supply_margin< thresholds.power_supply_margin[1]?0:1,
                    values:baogongliuwei_nominal.power_supply_margin,
                }
            ]
        }
        state.isFirstCal = false
        cal_initialData.load = BoundaryCondition.load
        cal_initialData.gas = BoundaryCondition.gas
        cal_initialData.feedin = BoundaryCondition.feedin
        cal_initialData.wind = BoundaryCondition.wind
        cal_initialData.solar =BoundaryCondition.solar
        cal_initialData.coal =BoundaryCondition.coal
        cal_initialData.hydro_output =BoundaryCondition.hydro_output
    } else {
        const {data} = await  getNetworkData({
                case_id:state.case_id,
                time_no:state.timestep,
                area:state.partition
        })
        state.mapData = data
        baseCaseData.mapDataDefault = data
        if(state.partition == '全省'){
            const res = await getLaodratioTimestep({
                case_id:state.case_id,
                time_no:state.timestep,
                area:state.partition
            })
            state.interfaceList = handleData(res.data.interface)
            state.lineList = handleData(res.data.line)
            state.contrastLineData = geCalLineData(state.lineList,false)
            state.contrastInterfaceData = getCalInterfaceData(state.interfaceList, false)
            baseCaseData.interfaceList = [...state.interfaceList]
            baseCaseData.lineList = [...state.lineList]
        }else{
            state.trafo_details = (await getTrafoCapability({
                case_id:state.case_id,
                time_no:state.timestep,
                area:state.partition
            })).data
        }
    }
    if (state.partition == '全省') {
        if(state.contrastRadarData.length==2){
            state.contrastRadarData=[]
            if(state.contrastType=='pre'){
                state.contrastRadarData.push({
                    canonicalValue:{...preCaseData.indicatorsValue},
                    standardValue:{...preCaseData.indicatorsData},
                })
            }else if(state.contrastType=='reference'){
                state.contrastRadarData.push({
                    canonicalValue:{...referCaseData.indicatorsValue},
                    standardValue:{...referCaseData.indicatorsData},
                })
            }else if(state.contrastType=='base'){
                state.contrastRadarData.push({
                    canonicalValue:{...baseCaseData.indicatorsValue},
                    standardValue:{...baseCaseData.indicatorsData},
                })
            }
        }
        state.contrastRadarData.push({
            canonicalValue:{...indicatorsValue.value},
            standardValue:{...indicatorsData.value},
        })
        initRadar()
    } else {
        state.partitionData = Object.keys(state.trafo_details).map(item=>{
            if(item!='power_recev_cap'){
                return{
                    name:item,
                    nameList:state.trafo_details[item]['distri_name'].map(item=>item.replace(/500kV/g,"")	,),
                    valueList:state.trafo_details[item]['distri_power'],
                    value1:+(state.trafo_details[item]['total_power']),
                    value2:+(state.trafo_details[item]['total_limit']),
                    value3:+(100*state.trafo_details[item]['total_limit_usage'])
                }
            }else{
                state.partitionElectrify = +state.trafo_details[item]
            }
        }).filter(item=>item)

        baseCaseData.partitionElectrify= state.partitionElectrify
        baseCaseData.partitionData= state.partitionData
        
        if(val){
            if(state.contrastType=='pre'){
                state.contrastPartitionList = getContrastPartitionList(preCaseData.partitionData,state.partitionData,true)
            }else if(state.contrastType=='reference'){
                state.contrastPartitionList = getContrastPartitionList(referCaseData.partitionData,state.partitionData,true)
            }else if(state.contrastType=='base'){
                state.contrastPartitionList = getContrastPartitionList(baseCaseData.partitionData,state.partitionData,true)
            }
        }else{
            state.contrastPartitionList = getContrastPartitionList([],state.partitionData,false)
        }
        initBar(val)
    }
}
const initAllData = async (val) => {
    store.showModal()
    state.sliderName = undefined
    if (val) {
        state.cal_text = '正在推演'
        state.calEquipmentId = state.conEquipmentId ?  (state.equipmentId!==undefined) ? [...state.conEquipmentId,state.equipmentId]:[...state.conEquipmentId]: (state.equipmentId!==undefined) ? [state.equipmentId]:[]
        preCaseData.indicatorsData = {...indicatorsData.value}
        preCaseData.indicatorsValue = { ...indicatorsValue.value }
        preCaseData.partitionValue = [...state.partitionValue]
        preCaseData.lineList = [...state.lineList]
        preCaseData.interfaceList= [...state.interfaceList]
        preCaseData.mapDataDefault= {...state.mapData}
        if(state.partition!='全省'){
            preCaseData.partitionElectrify= state.partitionElectrify
            preCaseData.partitionData= state.partitionData
        }
        if(!state.isFirstCal){
            preCaseData.initialData.load = cal_initialData.load
            preCaseData.initialData.gas = cal_initialData.gas
            preCaseData.initialData.feedin = cal_initialData.feedin
            preCaseData.initialData.wind = cal_initialData.wind
            preCaseData.initialData.solar =cal_initialData.solar
            preCaseData.initialData.coal =cal_initialData.coal
            preCaseData.initialData.hydro_output =cal_initialData.hydro_output
        }
    } else {
        closeOtherModal()
        state.contrastRadarData = []
        state.equipmentId = undefined
        state.calEquipmentId = []
        state.conEquipmentId = []
        state.contrastType = 'pre'
        state.isCalculate = false
        state.isSetReference = false
        state.isSliderChange = false
        state.isFirstCal = true

        const {input_range,original} = (await getBalanceData({
            case_id: state.case_id,
            time_no: state.timestep,
            area:state.partition
        })).data
        const {indicators,thresholds,psm_color,new_consump_rate} = (await getAllIndicator({
            case_id: state.case_id,
            time_no: state.timestep,
            area:state.partition
        })).data

        BoundaryCondition.load = +original.load
        BoundaryCondition.gas = +original.gas_output
        BoundaryCondition.wind = +original.wind_output
        BoundaryCondition.solar = +original.solar_output
        BoundaryCondition.coal = +original.coal_output
        BoundaryCondition.hydro_output = +original.hydro_output
        
        BoundaryRange.loadRange={
            min:input_range.load[0],
            max:input_range.load[1],
            step:1,
        }
        BoundaryRange.solarRange={
            min:input_range.solar_output[0],
            max:input_range.solar_output[1],
            step:1,
        }
        BoundaryRange.windRange={
            min:input_range.wind_output[0],
            max:input_range.wind_output[1],
            step:1,
        }
        BoundaryRange.gasRange={
            min:input_range.gas_output[0],
            max:input_range.gas_output[1],
            step:1,
        }
        BoundaryCondition.feedin = +original.feedin
        if (state.partition == '全省') {

            state.partitionValue=Object.keys(psm_color).map(item=>{
                return{
                    name:item,
                    value:psm_color[item],
                    values:indicators[item].power_supply_margin,
                }
            })
            state.partitionEnergyValue = Object.keys(new_consump_rate).map(item=>{
                return{
                    name:item,
                    value:new_consump_rate[item].newenergy_proportion,
                    values:new_consump_rate[item],
                }
            })
            state.contrastPartitionData = getContrastPartitionData(state.partitionValue,false)
            baseCaseData.partitionValue = [...state.partitionValue]

            BoundaryCondition.feedin1=+original.dc_feedin['中天直流']
            BoundaryCondition.feedin2=+original.dc_feedin['灵宝直流']
            BoundaryCondition.feedin3=+original.dc_feedin['青豫直流']
            BoundaryCondition.feedin4=+original.dc_feedin['长南特高压']
            BoundaryCondition.feedin5=+original.dc_feedin['南荆特高压']
            BoundaryCondition.feedin6=+original.dc_feedin['豫武特高压']
            
            BoundaryRange.feedinRange1 = {
				min:input_range.dc_feedin['中天直流'][0],
				max:input_range.dc_feedin['中天直流'][1],
				step:10,
			}
            BoundaryRange.feedinRange2={
				min:input_range.dc_feedin['灵宝直流'][0],
				max:input_range.dc_feedin['灵宝直流'][1],
				step:10,
			}
            BoundaryRange.feedinRange3={
				min:input_range.dc_feedin['青豫直流'][0],
				max:input_range.dc_feedin['青豫直流'][1],
				step:10,
			}
            Object.keys(indicators[state.partition]).forEach(item=>{
                if(item=='feedin_dependence'||item=='non_inertia_penetration'){
                    let temp = (indicators[state.partition][item]-thresholds[state.partition][item][3])/(thresholds[state.partition][item][0]-thresholds[state.partition][item][3])
                    indicatorsValue.value[item]=1-(temp<0?0:temp>1 ? 1:temp)
                }else{
                    let temp
                    temp = (indicators[state.partition][item]-thresholds[state.partition][item][0])/(thresholds[state.partition][item][3]-thresholds[state.partition][item][0])
                    indicatorsValue.value[item]=temp<0?0:temp>1 ? 1:temp
                }
            })
            indicatorsData.value = indicators[state.partition]
            Object.keys(thresholds[state.partition]).forEach((item,index)=>{
            indicatorsRange.value[item]=[...thresholds[state.partition][item]].concat(
                item=='feedin_dependence'||item=='non_inertia_penetration'?
                [(100*Math.abs(thresholds[state.partition][item][0]-thresholds[state.partition][item][1])/(thresholds[state.partition][item][0]-thresholds[state.partition][item][3])),
                (100*Math.abs(thresholds[state.partition][item][0]-thresholds[state.partition][item][2])/(thresholds[state.partition][item][0]-thresholds[state.partition][item][3])),
                10*(thresholds[state.partition][item][0]-thresholds[state.partition][item][1]),
                10*(thresholds[state.partition][item][1]-thresholds[state.partition][item][2]),
                10*(thresholds[state.partition][item][2]-thresholds[state.partition][item][3]),
                ]
                :[
                (100*Math.abs(thresholds[state.partition][item][1]-thresholds[state.partition][item][0])/(thresholds[state.partition][item][3]-thresholds[state.partition][item][0])),
                (100*Math.abs(thresholds[state.partition][item][2]-thresholds[state.partition][item][0])/(thresholds[state.partition][item][3]-thresholds[state.partition][item][0])),
                10*(thresholds[state.partition][item][3]-thresholds[state.partition][item][2]),
                10*(thresholds[state.partition][item][2]-thresholds[state.partition][item][1]),
                10*(thresholds[state.partition][item][1]-thresholds[state.partition][item][0]),
                ]
                )
            })

            initLine()
        }else{
            indicatorsData.value = indicators
            state.partitionValue = [
                {
                    name:state.partition,
                    value:psm_color,
                    values:indicators.power_supply_margin,
                }
            ]
            state.partitionEnergyValue =[
                {
                    name:state.partition,
                    value:new_consump_rate.newenergy_proportion,
                    values:new_consump_rate,
                }
            ]
        }
        preCaseData.initialData.load = BoundaryCondition.load
        preCaseData.initialData.gas = BoundaryCondition.gas
        preCaseData.initialData.feedin =state.partition == '全省'?(BoundaryCondition.feedin1+BoundaryCondition.feedin2+BoundaryCondition.feedin3): BoundaryCondition.feedin
        preCaseData.initialData.wind = BoundaryCondition.wind
        preCaseData.initialData.solar =BoundaryCondition.solar
        preCaseData.initialData.coal =BoundaryCondition.coal
        preCaseData.initialData.hydro_output =BoundaryCondition.hydro_output
        preCaseData.initialData.power_supply_margin = indicatorsData.value.power_supply_margin

        baseCaseData.indicatorsData = { ...indicatorsData.value }
        baseCaseData.indicatorsValue = {...indicatorsValue.value}
        baseCaseData.initialData.load = BoundaryCondition.load
        baseCaseData.initialData.gas = BoundaryCondition.gas
        baseCaseData.initialData.feedin = state.partition == '全省'?(BoundaryCondition.feedin1+BoundaryCondition.feedin2+BoundaryCondition.feedin3+BoundaryCondition.feedin4): BoundaryCondition.feedin
        baseCaseData.initialData.wind = BoundaryCondition.wind
        baseCaseData.initialData.solar =BoundaryCondition.solar
        baseCaseData.initialData.coal =BoundaryCondition.coal
        baseCaseData.initialData.hydro_output =BoundaryCondition.hydro_output
        baseCaseData.initialData.power_supply_margin = indicatorsData.value.power_supply_margin

    }
    await calculateN_1(val)
    initMap(val)
    store.hiddenModal()
    state.cal_text = '开始推演'
}
const initMap = (val) => {
    const option =getCalMapServies(state.partitionValue,state.partitionEnergyValue,state.partition,state.mapData,BoundaryCondition,state.isShowFeedin
    ,state.mapChecked1,state.mapType,state.mapChecked3,state.calEquipmentId)
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
        // if(mapOption.value.series[0].zoom>=4&&state.partition=='全省'){
        //     option.series[1].label.show = true
        // }else{
        //     option.series[1].label.show = false
        // }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();  
        mapOption.value.geo.zoom= _option.series[0].zoom
        mapOption.value.geo.center= _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapOption.value.series[1].label.fontSize = echartsResize(9)*(1+(_option.series[0].zoom-1.2)*0.3)
        // if(_option.series[0].zoom>4&&state.partition=='全省'){
        //     mapOption.value.series[1].label.show = true
        // }else{
        //     mapOption.value.series[1].label.show = false
        // }
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('dblclick')
    mapChart.value.on('dblclick',async(params)=>{
        if(params.componentSubType=='map'){
            if(params.name==state.partition){
                state.partition='全省'
            }else{
                state.partition=params.name
            }
            await registerMap(state.partition)
            initAllData()
        }
    })
    mapChart.value.off('click')
    mapChart.value.on('click',async (params)=>{//点击地图
        state.sliderName = undefined
        if(params.componentSubType=='map'){
            return
        }
        if((params.componentSubType=='lines'&&params.componentIndex==1)||(params.componentSubType=='scatter'&&[2].includes(params.componentIndex)&&Object.keys(params.data.info).length>0)){
            state.calculateLeft = params.event.offsetX -250/2
            state.calculateTop = params.event.offsetY - 128/2
            if (params.componentSubType == 'lines') {
                state.equipmentOptions=[
                    {
                        label:params.name,
                        value:params.data.id
                    }
                ]
                state.equipmentId = params.data.id
            } else {
                state.equipmentOptions=Object.keys(params.data.info).map(item=>{
					return{
						label:item,
						value:params.data.info[item]
					}
				})
				state.equipmentId = state.equipmentOptions[0].value
            }
            state.calculateShow = true
        }else if((params.componentIndex==3||params.componentIndex==5)&&state.conEquipmentId&&state.conEquipmentId.length!=0){
			if((Object.values(params.data.info).filter(item=>!state.conEquipmentId.includes(item))).length==0){
				return
			}else{
				state.equipmentOptions=Object.keys(params.data.info).map(item=>{
					return{
						label:item,
						value:params.data.info[item]
					}
				}).filter(items=>!state.conEquipmentId.includes(items.value))
				state.equipmentId = state.equipmentOptions[0].value
				state.calculateShow = true
			}
		}
    })
}
const initBar = (val)=>{
    const option = val? getPartitionBar([state.contrastType=='pre'?preCaseData.initialData.power_supply_margin:state.contrastType=='base'? baseCaseData.initialData.power_supply_margin:referCaseData.initialData.power_supply_margin,indicatorsData.value.power_supply_margin]
    ,[state.contrastType=='pre'?preCaseData.partitionElectrify:state.contrastType=='base'? baseCaseData.partitionElectrify: referCaseData.partitionElectrify,state.partitionElectrify])
    :getPartitionBar([indicatorsData.value.power_supply_margin],[state.partitionElectrify])
    barChart.value.setOption(option)
}
const start_cal = async ()=>{
    state.equipmentId = undefined
    await initAllData('cal_slider')
    state.isCalculate = true
    state.isSliderChange = false
}
const reset = async () =>{
    await initAllData()
}
const setReference = ()=>{
    referCaseData.initialData.load = BoundaryCondition.load
    referCaseData.initialData.gas = BoundaryCondition.gas
    referCaseData.initialData.feedin = BoundaryCondition.feedin
    referCaseData.initialData.wind = BoundaryCondition.wind
    referCaseData.initialData.solar =BoundaryCondition.solar
    referCaseData.initialData.coal =BoundaryCondition.coal
    referCaseData.initialData.hydro_output =BoundaryCondition.hydro_output
    referCaseData.initialData.power_supply_margin = indicatorsData.value.power_supply_margin
    if(state.partition!='全省'){
        referCaseData.partitionElectrify= state.partitionElectrify
        referCaseData.partitionData= state.partitionData
    }
    referCaseData.indicatorsValue = {...indicatorsValue.value}
    referCaseData.indicatorsData = {...indicatorsData.value}
    referCaseData.partitionValue =  [...state.partitionValue]
    referCaseData.interfaceList = [...state.interfaceList]
    referCaseData.lineList = [...state.lineList]
    referCaseData.mapDataDefault = {...state.mapData}
    state.isSetReference = true
    message.success('设置基准场景成功')
}
const initLine = ()=>{
    const option = getLoadOption(state.timestep,loadData1.value)
    lineChart.value.setOption(option)
}
const initRadar = ()=>{
    const option = getCalRadarOption(state.contrastRadarData,indicatorsRange.value,state.partition)
    radarChart.value.setOption(option,true)
}
onMounted(async () => {
    await registerMap(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    radarChart.value = markRaw(echarts.init(radar.value))
    barChart.value = markRaw(echarts.init(bar.value))
    lineChart.value = markRaw(echarts.init(line.value))
    if(state.case_id){
        
    }else{
        const res = (await getCaseDataApi()).data
        state.case_id = res[0].id ? res[0].id : undefined
        state.year =res[0].year
        const res1 = await getTimeSequence({
            case_id: state.case_id,
            area: state.partition,
        })
        store.loadData1 = res1.data.value['load_data']
        state.timeData = res1.data.time
        state.time = state.timeData[state.timestep]
    }
    initAllData()
})
watch(() => [state.mapChecked1, state.mapType, state.mapChecked3, state.isShowFeedin], ([v1, v2, v3, v4]) => {
    initMap(true)
})
</script>
<style lang="scss" scoped>
.main_content3_index2{
    .map_slider_list{
        padding: 5px 20px;
        >div{
            display:flex;
            align-items: center;
            >p{
                width: 35px;
                text-align-last:justify;
            }
            >div{
                width: 264px;
                position: relative;
            }
        }
    }
    .map_slider{
        width: 264px;
        height: 50px;
    }
    .map_slider,.map_slider_list{
        position: absolute;
        z-index: 2;
        top: 150px;
        left: 50px;
        border: 1px solid $borderColor;
        background: $bgColor;
        >span:first-child{
            position: absolute;
            right: 2px;
            top: 2px;
            font-size: 14px;
            color: #4ec48c;
            &:hover{
                cursor: pointer;
            }
        }
        .map_slider_text1,.map_slider_text2{
            top: 17px;
            position: absolute;
            font-size: 12px;
            color: $activeTextColor;
            width: 34px;
        }
        .map_slider_text1{
            left:0px;
            text-align: right;
        }
        .map_slider_text2{
            right: 0px;
            text-align: left;
        }
        &:deep(.ant-slider){
            margin:20px 44px;
            
            .ant-slider-step,.ant-slider-rail,.ant-slider-track{
                height: 7px;
                border-radius: 3px;
            }
            .ant-slider-rail{
                background-color: #f5f5f5;
            }
            .ant-slider-handle{
                height: 13px;
                width: 13px;
                margin-top: 0px;
                border-color: orange;
                background: orange;
                box-shadow: none;
                border-radius: 100%;
            }
            .ant-slider-handle::after{
                content: none;
            }
            .ant-slider-track{
                background: $baseColor;
            }
        }
    }
    .load_slider{
        // top: 160px;
        top: 70px;
        left: 259px;
    }
    .gas_slider{
        // top: 230px;
        top: 140px;
        left: 259px;
    }
    .bg_line{
        height: 3px;
        background: linear-gradient(to right, #14a2ff 0%, #00ffff 50%, $baseColor 100%);
        border-radius: 100%;
    }
    &:deep(.ant-select){
        width: 180px;
    }
    .wind_slider{
        // top: 329px;
        top: 239px;
        left: 259px;
    }
    .feedin_slider{
        // top: 241px;
        top: 149px;
        left: 259px;
    }
    .solar_slider{
        // top: 360px;
        top: 270px;
        left: 259px;
    }
    .left_content{
        padding-left: 20px;
        width: 270px;
        .text_title{
            margin:20px 0;
            width:240px;
            border: 1px solid $borderColor;
            font-size:30px;
            line-height: 38px;
            text-align: center;
            font-weight: bolder;
            // height: 72px;
        }
        .load_line{
            margin-top: 20px;
            >p{
                font-size: 18px;
                text-align: center;
                line-height: 30px;
                font-weight: bolder;

            }
            width: 240px;
            >div{
                height: 120px;
                width: 240px;
            }
        }
        [class^="map_kanban"]{
            width: 240px;
            border: 1px solid $borderColor;
            padding: 7.5px;
            .bottom_white{
                margin-bottom: 3px;
                padding-bottom: 3px;
                box-sizing: content-box;
                border-bottom: 1px dashed #fff;
            }
            >p{
                font-size: 23px;
                line-height: 40px;
                font-weight: bolder;
                text-align: center;
                border-bottom: 2px solid #fff;
                padding-bottom: 4.5px;
                margin-bottom: 4.5px;
                span{
                    color:$borderColor;
                    &:hover{
                        cursor: pointer;
                    }
                    position: absolute;
                    right:0px;
                    top: 8px;
                    z-index:1;
                    font-size: 26px;
                }
            }
            >div:nth-child(2){
                padding: 0 5px;
                >div{
                    display: flex;
                    line-height:32px;
                    align-items: center;
                    position: relative;
                    p{
                        font-size: 21px;
                        margin-right: 15px;
                    }
                    span{
                        font-size: 21px;
                        font-weight: bolder;
                        color: $activeTextColor;
                    }
                    .span_small{
                        font-size:16px
                    }
                    .arrow{
                        font-weight:normal;
                        color: #fff;
                    }
                    >div{
                        position: absolute;
                        right: -9px;
                        top: 3px;
                        font-size: 20px;
                        &:deep(.ant-checkbox){
                            .ant-checkbox-inner {
                                width: 20px;
                                height: 20px;
                                font-size: 20px;
                                // 设置对勾的 大小 和 位置
                                &::after {
                                    top: 8px;
                                    left: 3px;
                                    width: 8px;
                                    height: 16px;
                                }
                            }
                        }
                    }
                }
                .check_btn{
                    right: 20px;
                    top: -5px;
                }
                .slider_btn{
                    span{
                        color:$borderColor;
                        &:hover{
                            cursor: pointer;
                        }
                        font-size: 24px;
                        position: relative;
                        z-index:1
                    }
                }
            }
        }
        .map_kanban{
            margin-top: 20px;
        }
        .map_kanban_initial{
            // top:140px;
            top:42px;
            left:259px;
            width:100px;
            z-index:2;
            background-color: $bgColor;
            >p{
                font-size: 18px;
                line-height: 18px;
                font-weight: bolder;
                text-align: center;
                border-bottom: 2px solid #fff;
            }
        }
        .map_select{
            left:50px;
            bottom: 165px;
        }
        .time_select{
            position: absolute;
            left:50px;
            bottom: 205px;
            width: 180px
        }
        button{
            left:50px;
            bottom: 30px;
            background: linear-gradient(to right, #14a2ff 0%, #00ffff 50%, $baseColor 100%);
            color: #041B8C;
            width: 180px;
            font-size: 18px;
            letter-spacing: 2px;
            border-radius: 10px;
            height: 36px;
            font-weight: bolder;
        }
        >.linear_btn2{
            bottom:75px
        }
        >.linear_btn1{
            bottom:120px
        }
    }
    .middle_content{
        width: 1130px;
        >img{
            position: absolute;
            z-index: 2;
            right: 0;
            bottom:10px;
            width:50px;
            &:hover{
                cursor: pointer;
            }
        }
        >p:first-child{
            font-size: 50px;
            font-weight: bolder;
            text-align: center;
            width: 100%;
            letter-spacing: 10px;
        }
        .map{
            width: 100%;
            height: 866px;
            position: relative;
            z-index: 1;
        }
        .map_checked{
            position: absolute;
            left: 15px;
            bottom: 128px;
            display: flex;
            align-items: center;
            z-index: 2;
            &:deep(*){
                .ant-checkbox-wrapper{
                    span{
                        color: #fff;
                        font-size: 16px;
                    }
                }
                .ant-radio-button-wrapper{
                    line-height: 22px;
                    font-size: 12px;
                    height: 24px;
                }
            }
        }   
        .map_checked_bottom{
            left: 110px;
            bottom: 5px;
        }
        .map_calculate{
            z-index: 2;
        }
        .map_calculate>div{
            width: 220px;
            padding: 5px 0 10px;
            display: flex;
            background-color: rgb(14, 46, 82);
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            >p{
                color: rgb(229, 242, 255);
                font-size: 16px;
                line-height: 32px;
                font-weight: 500;
            }
            >span:first-child{
                position: absolute;
                color: rgb(229, 242, 255);
                right: 5px;
                top: 8px;
                font-size: 14px;
            }
            .map_calculate_select{
                .ant-select-selector {
                    width: 150px;
                }
                .ant-select-selection-search-input{
                    height: 30px;
                }
            }
            button{
                background: linear-gradient(to right, #14a2ff 0%, #00ffff 50%, $baseColor 100%);
                color: #041B8C;
                width: 180px;
                font-size: 18px;
                letter-spacing: 2px;
                border-radius: 10px;
                height: 36px;
                font-weight: bolder;
                margin: 10px 10px 0;
            }
        }
    }
    .right_content{
        width: 440px;
        >p:first-child{
            font-size: 30px;
            font-weight: bolder;
            letter-spacing: 10px;
            text-align: center;
            line-height: 50px;
            -webkit-background-clip: text;
            background-image: linear-gradient(to right, #14a2ff 0%, #00ffff 50%, $baseColor 100%);
            color: transparent;
        }
        .partition{
            >p{
                font-size: 18px;
                line-height: 36px;
                text-align: center;
                color: #D4FFE5;
                font-weight: bolder;
            }
            .fenquBar{
                >div:first-child{
                    grid-template-columns: 1fr 1fr;
                    p{
                        text-align: center;
                        font-size: 16px;
                        line-height: 32px;
                    }
                }
                >div:last-child{
                    height: 200px;
                    width: 440px;
                }
            }
            .fenqu_table{
                >div:first-child,>div:last-child>div{
                    display: grid;
                    grid-template-columns: 1.2fr 1.1fr 0.7fr 1fr;
                    >div{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                        text-align: center;
                        font-size: 14px;
                        >div{
                            display: flex;
                            justify-content: center;
                            p{
                                min-width: 50px;
                            }
                            img{
                                width: 25px;
                                object-fit: contain;
                            }
                        }
                    }
                    border-bottom: 1px solid rgba($color: #fff, $alpha: 0.3);
                }
                >div:first-child{
                    >div{
                        height: 48px;
                        font-weight: bolder;
                    }
                }
                >div:last-child{
                    height: 355px;
                    >div{
                        font-size: 16px;
                        line-height: 36px;
                        p{
                            font-size: 14px;
                        }
                        img{
                            width: 25px;
                        }
                        
                        >div:last-child{
                            flex-direction: row;
                        }
                    }
                    
                }
            }
        }
        .province{
            .radar{
                height: 305px;
                width: 440px;
            }
            div[class^="zhibiao"]{
                color: #fff;
                position: absolute;
                text-align: center;
                font-size: 14px;
                line-height: 18px;
            }
            .zhibiao1{
                top: 3px;
                left: 188px;
            }
            .zhibiao2{
                top: 77px;
                left: 49px;
            }
            .zhibiao3{
                top: 192px;
                left: 49px;
            }
            .zhibiao4{
                top: 270px;
                left: 164px;
            }
            .zhibiao5{
                top: 192px;
                right: 33px;
            }
            .zhibiao6{
                top: 77px;
                right: 62px;
            }
            .margin_list,.line_list,.interface_list{
                >p{
                    text-align: center;
                    font-size: 18px;
                    line-height: 36px;
                    color: #D4FFE5;
                    font-weight: bolder;
                    // -webkit-background-clip: text;
                    // color: transparent;
                    // background-image: linear-gradient(to right, #14a2ff 0%, #00ffff 50%, $baseColor 100%);
                }
                >div{
                    >div:first-child,>div:last-child>div{
                        display: grid;
                        grid-template-columns:0.5fr 1.5fr 1fr 1fr;
                        >p{
                            font-size:14px;
                            text-align:center;
                            line-height: 32px;
                            color:#fff;
                        }
                    }
                    >div:first-child{
                        >p{
                            font-weight: bolder;
                            font-size:16px;
                        }
                    }
                    >div:last-child{
                        height:90px;
                    }
                }
            }
            .interface_list{
                margin-top: 20px;
                >div{
                    >div:last-child{
                        height:100px;
                    }
                }
            }
        }
    }
}
</style>