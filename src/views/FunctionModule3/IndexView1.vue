<template>
    <div class="main_content3_index1">
        <div class="top_content">
            <div class="point_bg">
                <div class="point_content top_left_content">
                    <p class="title">供电情况分析</p>
                    <div class="grid_title">
                        <p>装机容量比</p>
                        <p>发电量占比</p>
                    </div>
                    <div class="pie_content" ref="pie1">

                    </div>
                    <div class="info_list" v-if="state.partition=='全省'">
                        <p>最高调度负荷<span>{{fixInteger(infoData.max_load_p.value)}}万千瓦</span></p>
                        <p>最大日峰谷差<span>{{fixInteger(infoData.max_load_peak_valley.value)}}万千瓦</span></p>
                        <p>最大供电缺口<span>{{fixInteger(infoData.supply_slack.value)}}万千瓦</span></p>
                        <p>最大调峰缺口<span>{{fixInteger(infoData.peak_slack.value)}}万千瓦</span></p>
                    </div>
                    <div class="table_underline" v-if="state.partition=='全省'">
                        <div>
                            <p>序号</p>
                            <p>分区名称</p>
                            <p>最低/平均裕度(万千瓦)</p>
                            <p>缺电/紧张时长(小时)</p>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.partitionMarginList" :key="index">
                                <p>{{ index+1 }}</p>
                                <p>{{ item.name }}</p>
                                <p>{{ fixInteger(item.min_psm,0)+'/'+fixInteger(item.avg_psm,0) }}</p>
                                <p><span :class="item.load_cutial_hours>50?'red':''">{{ item.load_cutial_hours }}</span>/<span>{{ item.power_tight_hours }}</span></p>
                            </div>
                        </div>
                    </div>
                    <div v-show="state.partition!='全省'" class="partition_info">
                        <div class="info_list">
                            <p>负荷最大缺口<span>{{ fixInteger(partitionData.max_val) }}万千瓦</span></p>
                            <p>缺电总时长<span>{{ fixInteger(partitionData.hours) }}小时</span></p>
                        </div>
                        <div class="lines" ref="lines">
                        
                        </div>
                    
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content top_middle_content">
                    <p class="map_title">
                        {{ state.partition=='全省'?'河南主网电力流地图':state.partition+'分区' }}
                    </p>
                    <div class="map" ref="map">

                    </div>
                    <div class="map_checked">
                        <div>
                            <a-checkbox v-model:checked="state.mapChecked1">
                                设备平均<br>负载率
                            </a-checkbox>
                        </div>
                        <div>
                            <!-- <a-checkbox v-model:checked="state.mapChecked2" :disabled="!state.mapChecked1">
                                供电裕度
                            </a-checkbox> -->
                            <a-radio-group v-model:value="state.mapType">
                                <a-radio :value="1">平均供电裕度</a-radio>
                                <br>
                                <a-radio :value="2">新能源消纳率</a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content top_right_content">
                <p class="title">电网风险分析</p>
                <div v-show="state.partition=='全省'">
                    <p class="underLine">关键断面负载分析</p>
                    <!-- <div class="partition_select absolute">
                        <a-select
                            v-model:value="state.deviceType"
                        >
                            <a-select-option value="trafo">主变</a-select-option>
                            <a-select-option value="line">线路</a-select-option>
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                    </div> -->
                    <div class="table_underline">
                        <div>
                            <p>断面名称</p>
                            <p>平均/最大限额率(%)</p>
                            <p>重载/越限时长(h)</p>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.interfaceList">
                                <p class="pointer" @click="openLineModal(item.name)">{{ item.name }}</p>
                                <p>{{fixInteger(item.avg_ratio*100)}}/{{ fixInteger(item.max_ratio*100) }}</p>
                                <p>{{fixInteger(item.heavy_hours_ratio*100,0)}}/{{ fixInteger(item.over_hours_ratio*100,0) }}</p>
                            </div>
                        </div>
                    </div>
                    <p class="underLine">抽蓄利用小时数分析</p>
                    <div class="line" ref="bar1">

                    </div>
                </div>
                <div class="partition_info relative" v-show="state.partition!='全省'">
                    <p class="underLine">关键主变负载分析</p>
                    <!-- <div class="partition_select absolute">
                        <a-select
                            v-model:value="state.deviceType"
                        >
                            <a-select-option value="trafo">主变</a-select-option>
                            <a-select-option value="line">线路</a-select-option>
                            <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
                        </a-select>
                    </div> -->
                    <div class="table_underline">
                        <div>
                            <p>主变名称</p>
                            <p>平均/最大限额率(%)</p>
                            <p>重载/越限时长(h)</p>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.trafoList">
                                <p>{{ item.name }}</p>
                                <p>{{fixInteger(item.avg_ratio*100)}}/{{ fixInteger(item.max_ratio*100) }}</p>
                                <p>{{fixInteger(item.heavy_hours_ratio,0)}}/{{ fixInteger(item.over_hours_ratio,0) }}</p>
                            </div>
                        </div>
                    </div>
                    <p class="underLine">关键线路负载分析</p>
                    <div class="table_underline">
                        <div>
                            <p>线路名称</p>
                            <p>平均/最大限额率(%)</p>
                            <p>重载/越限时长(h)</p>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.lineList">
                                <p>{{ item.name }}</p>
                                <p>{{fixInteger(item.avg_ratio*100)}}/{{ fixInteger(item.max_ratio*100) }}</p>
                                <p>{{fixInteger(item.heavy_hours_ratio*100,0)}}/{{ fixInteger(item.over_hours_ratio*100,0) }}</p>
                            </div>
                        </div>
                    </div>
                    <!-- <p class="underLine">{{state.partition}}分区弃电量</p> -->
                    <!-- <div class="partition_info_count">
                    <div>
                        <p>重载主变数量（台）/占比</p>
                        <span>xx/xx%</span>
                    </div>
                    <div>
                        <p>重载线路数量（条）/占比</p>
                        <span>xx/xx%</span>
                    </div>
                    </div> -->
                    <!-- <div class="bar" ref="bar2">

                    </div> -->
                </div>
                </div>
            </div>
        </div>
        <div class="bottom_content point_bg">
            <div class="point_content">
                <div class="line_select">
                    <a-range-picker format="YYYY-MM-DD HH:mm" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate" picker="date" :show-time="{ format: 'HH' }" v-model:value="state.searchTime" :allowClear="false">
                        <template #suffixIcon>
                            
                        </template>
                    </a-range-picker>
                    <a-button  @click="changeTime">确定</a-button>
                    <a-button  @click="openCal" :disabled="(!state.timestep&&state.timestep!==0)">查看时刻断面</a-button>
                    <div class="ding" v-show="false"  @click="state.timeListShow=!state.timeListShow">
                        <img src="@/assets/images/index/ding.png" alt="">
                    </div>
                </div>
                <transition name="fade">
                    <div v-if="state.timeListShow" class="timeList absolute">
                        <div>
                            <check-circle-outlined :class="state.timeList.some(item=>item.checked==false)?'active':''" @click="()=>{
                            state.timeList.forEach(item=>{
                                item.checked = true
                            })
                            }" />
                            <close-circle-outlined :class="state.timeList.some(item=>item.checked==true)?'active':''" @click="()=>{
                            state.timeList.forEach(item=>{
                                item.checked = false
                            })
                            }"/>
                            <a-button>下载bpa</a-button>
                            <div class="ding" @click="state.timeListShow=!state.timeListShow">
                                <img src="@/assets/images/index/ding.png" alt="">
                            </div>
                        </div>
                        <div class="scroll">
                            <div v-for="(item,index) in state.timeList" @click="selectTime(item.index)">
                                <a-checkbox @click.stop="" v-model:checked="item.checked"></a-checkbox>
                                <p>{{ item.name }}</p>
                                <p>{{ item.val }}</p>
                            </div>
                        </div>
                    </div>
                </transition>

                <div class="line" ref="line">

                </div>
            </div>
        </div>
        <line-modal :title="state.title" :type="1" v-if="state.lineModalShow" @close="state.lineModalShow=false"></line-modal>
    </div>
</template>
<script setup>
import { inject, onMounted,watch } from '@vue/runtime-core'
import { markRaw, reactive,toRefs,ref } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import {getMapOption,getMapServiesCopy} from '@/utils/indexMain'
import {getBarOption,getPieSeries,getBarSeries,getLineOption,getLineSeries} from '@/utils/indexMain3'
import {registerMap,fixInteger} from '@/utils/common'
import {getCaseDataApi,getAllPsm,getLoadSufficiencey,getAllIndicator,getBalanceData,getLaodratioTimestep,getReloadList,getZhuangjiCapacity,getTimeSequence,getNetworkData,getPieRate,getExtremeData} from '@/api/index'
import { setting } from "@/config/setting.config"
import dayjs from 'dayjs'
const {echartsLength,echartsResize}  = setting
const store = dataStore()
const  { caseId1,timeStep1,searchTime,year1,partition,timeData1 } = storeToRefs(store)
const echarts = inject("ec");
const state = reactive({
    case_id:caseId1,
    timestep:timeStep1,
    searchTime,
    partition:partition,
    timeData:timeData1,
    datazoomRange:'',
    isDatazoom:false,
    lineStart: 0,
    year:year1,
    lineEnd: undefined,
    startIndex:undefined,
    endIndex:undefined,
    isShowTip: false,
    timeListShow: false,
    deviceType: '线路',
    mapChecked1:true,
    mapType:1,
    pieData1:[],
    pieData2:{},
    barData1:{},
    mapData: {},
    lineData: {},
    lineList:[],
    trafoList:[],
    interfaceList:[],
    partitionLineData:[],
    partitionMarginList:[],
    partitionEnergyList:[],
    title:'',
    lineModalShow:false,
    timeList: [
        // {
        //   name: '夏季高峰',
        //   val: '2025年06月28日 22:00:00',
        //   index: 4294,
        //   xAxis:'2025-06-28 22:00:00',
        //   checked:true
        // },
    ]
})
const partitionInfo = ref({
    "wind_output": 42.2,
    "solar_output": 14.2,
    "coal_output": 79,
    "nuclear_output": 0,
    "hydro_output": 0,
    "load": 360.9,
    "feedin": 187.1,
    "gas_output": 0,
    "margin": 0,
})
const partitionData = ref({
    "time_no": 0,
    "max_val": 0,
    "hours": 0,
    "details": {
        "time_no": [],
        "values": []
    }
})
const infoData = ref({
    "max_load_p": {
        "value": 98147.24,
        "time": "2020-06-27 22:00:00"
    },
    "max_wind_P": {
        "value": 14838.18,
        "time": "2020-03-15 20:00:00"
    },
    "max_solar_p": {
        "value": 2801.22,
        "time": "2020-02-25 12:00:00"
    },
    "max_load_peak_valley": {
        "value": 55113.87,
        "unit": "2020-06-27"
    },
    "peak_slack": {
        "value": 392.87,
        "time": "2020-10-16 06:00:00"
    },
    "supply_slack": {
        "value": 170.29,
        "time": "2020-07-12 13:00:00"
    }
})
const allEcharts = reactive({
    line:undefined,
    lineChart:undefined,
    lines:undefined,
    lineCharts:undefined,
    lineOption:{},
    pie1:undefined,
    pieChart1: undefined,
    bar1:undefined,
    barChart1:undefined,
    bar2:undefined,
    barChart2:undefined,
    map:undefined,
    mapChart:undefined,
    mapOption:{},
})
const { line,lineChart,lineOption,lines,lineCharts,pie1,pieChart1,bar1,barChart1,bar2,barChart2,map,mapChart,mapOption} = toRefs(allEcharts)
const router = useRouter()
const disabledDate = (current )=>{
    // const currentYear = dayjs().year();
    const date = dayjs(current);
    return date.year() !== state.year;
}
const changeTime = () => {
    state.startIndex =state.timeData.findIndex(item=>item==state.searchTime[0])
    state.endIndex = state.timeData.findIndex(item => item == state.searchTime[1])
    initLine(1,state.timestep)
}
const openCal = () => {
    router.push({
        path:'/index/FunctionModule3-2'
    })
}
const openLineModal = (name)=>{
    state.title = name
    state.lineModalShow = true
}
const selectTime = (index) => {
    if(state.timestep == index){
        return
    }else{
        state.timestep= index
    }
    if (state.startIndex || state.endIndex) {
        state.searchTime = [state.timeData[0], state.timeData[state.timeData.length - 1]]
        state.startIndex =undefined
        state.endIndex =undefined
    }
    initLine(2,index)
}
const initData = async () => {
    const [res1,res2,res3,res4,res5,res6] = await Promise.all([getZhuangjiCapacity({
        case_id: state.case_id,
        area:state.partition
    }),getPieRate({
        case_id: state.case_id,
        area:state.partition
    }),getTimeSequence({
        case_id: state.case_id,
        area:state.partition
    }),getReloadList({
        case_id: state.case_id,
        area:state.partition
    }),getNetworkData({
        case_id: state.case_id,
        // time_no:state.timestep,
        area:state.partition
    }),getAllPsm({
        case_id: state.case_id,
        area:state.partition
    })])
    state.pieData1 = res1.data
    state.pieData2 = res2.data
    state.timeData = res3.data.time
    state.searchTime = [state.timeData[0], state.timeData[state.timeData.length - 1]]
    if (state.partition == '全省') {
        store.loadData1 = res3.data.value['load_data']
        infoData.value = (await getExtremeData({
        case_id: state.case_id,
        area:state.partition
        })).data
        state.interfaceList = res4.data.interface
    } else {
        state.lineList = res4.data.line
        state.trafoList = res4.data.trafo
        const res = (await getLoadSufficiencey({
        case_id: state.case_id,
        area:state.partition,
        bload_series: true,
        bdetail:true
        })).data
        partitionData.value = res.load_cutailment
        state.partitionLineData = res
    }
    state.lineData = res3.data.value
    state.mapData = res5.data
    state.lineEnd = echartsLength / state.timeData.length
    state.partitionMarginList = Object.keys(res6.data).filter(item => item != '全省').map(items => {
        return Object.assign({ name: items,value:res6.data[items].avg_psm_color, values:res6.data[items].avg_psm}, res6.data[items])
    })
    state.partitionEnergyList = Object.keys(res6.data).filter(item => item != '全省').map(items => {
        return Object.assign({ name: items,value:res6.data[items].new_consump_rate.newenergy_proportion }, res6.data[items])
    })
    initLine(1, state.timestep)
    initAllEcharts()
    initMap()
    // initAllData()
    store.hiddenModal()
}
const initAllEcharts = () => {
    if (state.partition == '全省') {
        const option1 = getBarOption(state.pieData2)
        barChart1.value.setOption(option1)
    } else {
        const option = getLineSeries(state.partitionLineData,state.timeData)
        lineCharts.value.setOption(option)
    }
    const option2 = getPieSeries(state.pieData1,state.pieData2)
    pieChart1.value.setOption(option2)
}
const initMap = (val) => {
    const option = getMapServiesCopy(state.partition,state.mapData,state.mapChecked1,true,true,state.mapType,state.partitionMarginList,state.partitionEnergyList)
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();
        mapOption.value.geo.zoom = _option.series[0].zoom
        mapOption.value.geo.center = _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapOption.value.series[1].label.show = _option.series[0].zoom>1.8
        mapOption.value.series[1].label.fontSize = echartsResize(6)*(1+(_option.series[0].zoom-1.23)*0.3)
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click', async (params) => {
        console.log(mapChart.value.convertFromPixel('geo', [params.event.offsetX, params.event.offsetY]));
        if(params.componentSubType=='map'){
            store.showModal()
            if(params.name==state.partition){
                state.partition='全省'
            }else{
                state.partition=params.name
            }
            await registerMap(state.partition)
            initData()
        }
    })
}
const initAllData = async () => {
    return
    store.showModal()
    if(state.partition != '全省') {
        // const {original} = (await getBalanceData({
        //     case_id: state.case_id,
        //     time_no: state.timestep,
        //     area:state.partition
        // })).data
        // partitionInfo.value = original
        // const {indicators} = (await getAllIndicator({
        //     case_id: state.case_id,
        //     time_no: state.timestep,
        //     area:state.partition
        // })).data
        // partitionInfo.value.margin = indicators.power_supply_margin
    }
    state.mapData = (await getNetworkData({
        case_id: state.case_id,
        // time_no:state.timestep,
        area:state.partition
    })).data
    initMap()
    store.hiddenModal()
}
const drawLine = async(timestep,type,name)=>{
    let line,option,temp,lineData,lineStart,lineEnd
    lineStart = state.lineStart
    lineEnd = state.lineEnd
    lineData=((state.timestep||state.timestep===0)?[
        {
            // name:name?name: state.timeData[state.timestep],
            name: state.timeData[state.timestep],
            xAxis: state.timeData[state.timestep],
            label:{
                distance:echartsResize(0),
                color: '#fff',
            },
            lineStyle:{
                color:"#fff",
                type:"dashed",
            },
        }
    ].concat(state.timeList):state.timeList)
    line=lineChart.value
    option=lineOption.value
    temp=lineOption.value.series.length-1
    //切换下拉1 供电风险2 切换时刻3 特殊工况(暂无)4 悬浮显示5 滑动时间轴6 风险列表悬浮显示7
    if(type==3||type==5||type==7){//切换时刻
        if(type==3){
          console.log('切换时刻');
          initAllData()
        }else if(type==7){
          console.log('风险列表悬浮显示');
        }else{
          console.log('悬浮显示');
        }
        option.series[temp].markLine.data =lineData
        if(state.lineEnd){
            option.dataZoom[0].start = lineStart
            option.dataZoom[0].end = lineEnd
        }
        option.tooltip.showContent = state.isShowTip   
        line.setOption(option);
        
    }else if(type==2){//点击风险列表
        console.log('风险列表');
        option.series[temp].markLine.data =lineData
      // option.tooltip.showContent = state.isShowTip   
        line.setOption(option);
        lineChart.value.dispatchAction({
            type: 'showTip',
            seriesIndex:0,
            dataIndex:state.timestep
        })
    }else{//选中时刻后 拖动时间轴
        console.log('其他');
        option.series[temp].markLine.data =lineData
        option.tooltip.showContent = true
        line.setOption(option,true);
    }
}
const initLine=(type,index,name)=>{
    if(index&&type==2){
        state.lineStart = 100*(index/state.timeData.length)-(echartsLength/state.timeData.length)/2
        state.lineEnd = 100*(index/state.timeData.length)+(echartsLength/state.timeData.length)/2
    }
    lineOption.value = getLineOption(state.lineData,state.timeData,index,name,state.lineStart,state.lineEnd,state.timeList,state.startIndex,state.endIndex)
    drawLine(state.timestep,type,name)
    lineChart.value.off('updateAxisPointer')
    lineChart.value.on('updateAxisPointer', function (event) {
    const xAxisInfo = event.axesInfo[0];  
        if (xAxisInfo) {
            if(state.timestep === xAxisInfo.value){
                if(state.isShowTip == true){
                    drawLine(xAxisInfo.value,5)
                    return
                    }
                    return
            }else if(state.isShowTip == true){
                drawLine(xAxisInfo.value,7)
                return
            }
            state.timestep = xAxisInfo.value
            drawLine(xAxisInfo.value,3)
        }else{
            
        }
    });
    lineChart.value.off('click')
    lineChart.value.on('click',(e)=>{
        if(e.componentType=='markLine'){
            if(e.name.includes('-')) return
            let index = state.timeData.findIndex(item=>item==e.data.value)
            state.timestep = index
            drawLine(index,3,e.name)
        }
    })
    lineChart.value.on('datazoom', (event)=> {
        state.lineStart = event.start
        state.lineEnd = event.end
        if((event.end-event.start)>25){
            if(((event.end-event.start)>=state.datazoomRange)&&state.datazoomRange) return
            state.isDatazoom = true
            initLine(6)
            state.datazoomRange = state.lineEnd-state.lineStart
        }else{
            if(state.isDatazoom){
                initLine(6)
                state.isDatazoom = false
                state.datazoomRange = ''
            }
        }
    })
    lineChart.value.on('mouseover',(e)=>{
        if(e.componentType=='markLine'){
            state.isShowTip = true
            lineChart.value.dispatchAction({
                type: 'showTip',
                seriesIndex:0,
                dataIndex:state.timeData.findIndex(item=>item==e.value)
            })
        }
    })
    lineChart.value.on('mouseout',(e)=>{
        if(e.componentType=='markLine'){
            state.isShowTip = false
            lineChart.value.dispatchAction({
                type: 'hideTip'
            })
        }
    })
}
onMounted(async() => {
    store.showModal()
    await registerMap(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    lineChart.value = markRaw(echarts.init(line.value))
    lineCharts.value = markRaw(echarts.init(lines.value))
    pieChart1.value = markRaw(echarts.init(pie1.value))
    barChart1.value = markRaw(echarts.init(bar1.value))
    // barChart2.value = markRaw(echarts.init(bar2.value))
    if(state.case_id){
        
    }else{
        const res = (await getCaseDataApi()).data
        state.case_id = res[0].id ? res[0].id : undefined
        state.year =res[0].year
    }
    initData()
})
watch(() => [state.mapChecked1, state.mapType], ([v1, v2]) => {
    initMap(true)
})
</script>
<style lang="scss" scoped>
    .main_content3_index1{
        .top_content{
            .top_left_content{
                .table_underline{
                    >div:first-child,>div:last-child>div{
                        grid-template-columns: 1fr 1fr 2fr 2fr;
                    }
                    >div:last-child{
                        height: 175px;
                    }
                }
                .pie_content{
                    height: 200px;
                }
                .partition_info{
                // margin-top: 20px;
                // >div{
                //   padding: 10px;
                //   display: flex;
                //   justify-content: space-around;
                //   p{
                //     font-size: 18px;
                //     line-height: 50px;
                //     height: 50px;
                //     span {
                //       color: $activeTextColor;
                //       font-weight: bolder;
                //       margin-left: 5px;
                //     }
                //   }
                // }
                    .lines{
                        height:260px;
                        width: 534px;
                    }
                }
            }
            .top_right_content{
                .line{
                    height: 250px;
                    width: 532px;
                }
                .partition_info{
                    .table_underline{
                        >div:last-child{
                        height: 185px;
                        }
                    }
                }
                .table_underline{
                    >div:first-child,>div:last-child>div{
                        grid-template-columns: 1fr 1fr 1fr;
                    }
                    >div:last-child{
                        height: 165px;
                        >div{
                            p:first-child{
                                color: #FFFFB5;
                                // text-decoration: underline;
                            }
                        }
                    }
                }
                .partition_select{
                    top: 3px;
                    right: 20px;
                    :deep(.ant-select) {
                        width: 100px;
                    }
                }
                .partition_info_count{
                    display: grid;
                    margin-top: 10px;
                    grid-template-columns: 1fr 1fr;
                    >div{
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        padding-bottom: 10px;
                        &::after {
                            content: '';
                            position: relative;
                            bottom: -5px;
                            height: 3px;
                            display: block;
                            background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
                            border-radius: 100%;
                            width: 80%;
                        }
                        p{
                            font-size: 16px;
                            line-height: 32px;
                        }
                        span{
                            font-size: 26px;
                            color: $activeTextColor;
                            font-weight: bolder;
                            line-height: 32px;
                        }
                    }
                }
                .bar{
                    height: 210px;
                    width: 532px;
                }
            }
            .top_middle_content{
                
            }
        }
    }
</style>