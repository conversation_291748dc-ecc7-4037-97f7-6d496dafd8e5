<template>
    <div class="main_content4_index1">
        <div class="top_content">
            <div class="point_bg">
                <div class="point_content top_left_content">
                    <p class="title">供电情况分析</p>
                    <p class="absolute" @click="state.lineShow=true" >详情</p>
                    <div class="line" ref="line1">

                    </div>
                    <div class="line" ref="line2">

                    </div>
                    <p class="absolute absolute_bottom" @click="state.equipmentShow=true" >初始关停机组</p>
                    <div class="line" ref="line3">

                    </div>
                    <div class="line" ref="line4">

                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content top_middle_content">
                    <p class="map_title">
                        {{ state.partition=='全省'?'河南主网电力流地图':state.partition+'分区' }}
                    </p>
                    <p class="map_time">{{   state.timeData[0]? state.timeData[0].slice(0,10)+'~'+state.timeData[state.timeData.length-1].slice(0,10) :'' }}</p>
                    <div class="map" ref="map">

                    </div>
                    <!-- <div class="mapInfo">
                        <p>气温: <span>xx ℃</span></p>
                        <p>风速: <span>xx m/s</span></p>
                        <p>辐射: <span>xx w/m^2</span></p>
                    </div> -->
                    <div class="map_checked">
                        <div>
                            <a-checkbox v-model:checked="state.mapChecked1">
                                主网潮流
                            </a-checkbox>
                        </div>
                        <div>
                            <!-- <a-checkbox v-model:checked="state.mapChecked2" :disabled="!state.mapChecked1">
                                供电裕度
                            </a-checkbox> -->
                            <a-radio-group v-model:value="state.mapType">
                                <a-radio :value="1">供电裕度</a-radio>
                                <br>
                                <a-radio :value="2">新能源消纳</a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                </div>
            </div>
            <div class="point_bg">
                <div class="point_content top_right_content">
                    <p class="title">电网风险分析</p>
                    <div class="grid">
                        <div>
                            <p class="underLine">新能源消纳率分析</p>
                            <div>
                                <div>
                                    <p>分区</p>
                                    <p>新能源消纳率(%)</p>
                                </div>
                                <div class="scroll">
                                    <div v-for="(item,index) in state.partitionEnergyList">
                                    <p>{{ item.name }}</p>
                                    <p>{{ fixInteger(100*item.value) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <p class="underLine">供电裕度分析</p>
                            <div>
                                <div>
                                    <p>分区</p>
                                    <p>供电裕度/万千瓦</p>
                                </div>
                                <div class="scroll">
                                    <div v-for="(item,index) in state.partitionMarginList">
                                        <p :class="item.values<0?'active':''">{{ item.name }}</p>
                                        <p :class="item.values<0?'active':''">{{ fixInteger(item.values,1) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="underLine">重过载统计分析</p>
                    <div class="flex">
                        <div>
                            <div class="grid"> 
                                <p :class="state.type==0?'active':''" @click="state.type=0" >重载主变统计</p>
                                <p :class="state.type==1?'active':''" @click="state.type=1" >重载线路统计</p>
                            </div>
                            <div v-show="state.type==0" ref="pie1" class="pie" >

                            </div>
                            <div v-show="state.type==1" ref="pie2" class="pie" >

                            </div>
                        </div>
                        <div>
                            <div>
                                <p>关键断面</p>
                                <p>重载/越限时长(h)</p>
                            </div>
                            <div class="scroll">
                                <div v-for="item in state.interfaceList">
                                    <p class="pointer" @click="openLineModal(item.name)">{{ item.name }}</p>
                                    <p>{{ fixInteger(item.heavy_hours_ratio*100,0)+'/'+fixInteger(item.over_hours_ratio*100,0) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom_content point_bg">
            <div class="point_content">
                <div class="line_select">
                    <a-button  @click="openCal" :disabled="(!state.timesteps&&state.timesteps!==0)">查看时刻断面</a-button>
                </div>
                <div class="line" ref="line">

                </div>
            </div>
        </div>
        <result-modal v-if="state.lineShow" @close="state.lineShow=false" ></result-modal>
        <line-modal :title="state.title" :type="2" :showSelect="false" v-if="state.lineModalShow" @close="state.lineModalShow=false"></line-modal>
        <equipment-modal v-if="state.equipmentShow" @close="state.equipmentShow=false"></equipment-modal>
    </div>
</template>
<script setup>
import { inject, onMounted,watch } from '@vue/runtime-core'
import { markRaw, reactive,toRefs,ref } from '@vue/reactivity'
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import {getMapOption,getMapServiesCopy} from '@/utils/indexMain'
import {getLineSeries,getPieSeries,getHeatMapOption} from '@/utils/indexMain4'
import {getLineOption} from '@/utils/indexMain3'
import {registerMap,fixInteger} from '@/utils/common'
import {getCaseDataApi,getCurveSeriesData,getCaseSeriesData,getAllIndicator,getNetworkData,getAllPsm,getTimeSequence,getReloadList} from '@/api/index'
import { setting } from "@/config/setting.config"
import dayjs from 'dayjs'
const {echartsLength,echartsResize}  = setting
const store = dataStore()
const router = useRouter()
const  { caseId2,year2,timeStep2,timeData2 } = storeToRefs(store)
const echarts = inject("ec");
const state = reactive({
    case_id:caseId2,
    partition: '全省',
    type:0,
    year: year2,
    lineStart: 0,
    lineEnd: 100/7,
    isShowTip: false,
    lineShow: false,
    mapChecked1: true,
    timesteps:timeStep2,
    mapType:1,
    mapData: {
        line_data: [],
        point_data:[]
    },
    lineData: {},
    timeData: timeData2,
    timeList:[],
    interfaceList:[],
    partitionMarginList:[],
    partitionEnergyList: [],
    mapList1:[],
    mapList2: [],
    pieData1: [1, 2, 3, 4, 5],
    pieData2: [5, 4, 3, 2, 1],
    lineData1:[],
    lineData2:[],
    lineModalShow:false,
    equipmentShow:false,
    title:''
})
const allEcharts = reactive({
    line:undefined,
    lineChart:undefined,
    lineOption:{},
    line1:undefined,
    lineChart1:undefined,
    line2:undefined,
    lineChart2:undefined,
    line3:undefined,
    lineChart3:undefined,
    line4:undefined,
    lineChart4:undefined,
    pie1:undefined,
    pieChart1:undefined,
    pie2:undefined,
    pieChart2:undefined,
    map:undefined,
    mapChart:undefined,
    mapOption:{},
})
const { line, lineChart, lineOption, line1, lineChart1, line2, lineChart2, line3, lineChart3,line4, lineChart4,pie1, pieChart1,pie2, pieChart2, map, mapChart, mapOption } = toRefs(allEcharts)
const initData = async () => {
    const [res1,res2,res3,res4] = await Promise.all([
        getTimeSequence({
            case_id: state.case_id,
            area: state.partition,
            is_short:2
        }),
        getReloadList({
            case_id: state.case_id,
            area: state.partition,
            is_short:2
        }),
        getCurveSeriesData({
            case_id: state.case_id,
            key_tab: 'gen_state',
            is_short:2
        }),
        getCaseSeriesData({
            case_id: state.case_id,
            ele_name: 'gen',
            series_type: 'maintenance',
            is_short:2
        }),
    ])
    state.lineData2 = Object.keys(res3.data).map(item=>{
        return {
            name: item,
            data: res3.data[item]
        }
    })
    state.lineData1 = Object.keys(res4.data).map(item=>{
        return {
            name: item,
            data: res4.data[item]
        }
    })
    state.interfaceList = res2.data.interface
    let num1=0
    let num2=0
    let num3=0
    let num4=0
    let num5=0
    let num1s=0
    let num2s=0
    let num3s=0
    let num4s=0
    let num5s=0
    res2.data.trafo.forEach(item=>{
        if (item.heavy_hours_ratio*100 == 0) {
            num1++
        } else if (item.heavy_hours_ratio*100 <= 10) {
            num2++
        } else if (item.heavy_hours_ratio*100 <= 30) {
            num3++
        } else if (item.heavy_hours_ratio*100 <= 50) {
            num4++
        } else {
            num5++
        }
    })
    res2.data.line.forEach(item=>{
        if (item.heavy_hours_ratio*100 == 0) {
            num1s++
        } else if (item.heavy_hours_ratio*100 <= 10) {
            num2s++
        } else if (item.heavy_hours_ratio*100 <= 30) {
            num3s++
        } else if (item.heavy_hours_ratio*100 <= 50) {
            num4s++
        } else {
            num5s++
        }
    })
    state.pieData1 = [num1, num2, num3, num4, num5]
    state.pieData2 = [num1s, num2s, num3s, num4s, num5s]
    state.lineData = res1.data.value
    state.timeData = res1.data.time
    store.loadData2 = res1.data.value['load_data']
    initAllEcharts()
    initPie()
} 
const openLineModal = (name)=>{
    state.title = name
    state.lineModalShow = true
}
const initAllEcharts = () => {
    const option1 = getLineSeries(1,state.timeData,state.lineData.load_data,)
    const option2 = getLineSeries(2, state.timeData, state.lineData.solar_output, state.lineData.wind_output)
    const option3 = getHeatMapOption(0, state.timeData,state.lineData1)
    const option4 = getHeatMapOption(1, state.timeData,state.lineData2)
    lineChart1.value.setOption(option1)
    lineChart2.value.setOption(option2)
    lineChart3.value.setOption(option3)
    lineChart4.value.setOption(option4)
}
const initPie = () => {
    const option1 = getPieSeries(1,state.pieData1)
    const option2 = getPieSeries(2,state.pieData2)
    pieChart1.value.setOption(option1)
    pieChart2.value.setOption(option2)
}
const openCal = () => {
    router.push({
        path:'/index/FunctionModule4-2'
    })
}
const initMap = (val) => {
    // const option = getMapOption(state.partition,state.mapData,state.mapChecked1,state.mapType,state.partitionMarginList,state.partitionEnergyList)
    const option = getMapServiesCopy(state.partition,state.mapData,state.mapChecked1,true,true,state.mapType,state.mapList1,state.mapList2)
    if(mapOption.value.series&&val){
        if(option.geo.zoom!=mapOption.value.series[0].zoom){
            option.geo.zoom = mapOption.value.series[0].zoom
            option.series[0].zoom = mapOption.value.series[0].zoom
        }else{

        }
    }else{
        option.geo.center = null
        option.series[0].center = null
    }
    mapOption.value = option
    mapChart.value.setOption(mapOption.value);
    mapChart.value.off('georoam')
    mapChart.value.on('georoam', function (params) {
        let _option = mapChart.value.getOption();
        mapOption.value.geo.zoom = _option.series[0].zoom
        mapOption.value.geo.center = _option.series[0].center
        mapOption.value.series[0].zoom = _option.series[0].zoom
        mapOption.value.series[0].center = _option.series[0].center
        mapOption.value.series[1].label.fontSize = echartsResize(6)*(1+(_option.series[0].zoom-1.23)*0.3)
        mapChart.value.setOption(mapOption.value)
    })
    mapChart.value.off('click')
    mapChart.value.on('click', async (params) => {
        console.log(mapChart.value.convertFromPixel('geo', [params.event.offsetX, params.event.offsetY]));
    })
}
const drawLine = async(timesteps,type,name)=>{
    let line,option,temp,lineData,lineStart,lineEnd
    lineStart = state.lineStart
    lineEnd = state.lineEnd
    lineData=((state.timesteps||state.timesteps===0)?[
        {
            // name:name?name: state.timeData[state.timesteps],
            name: state.timeData[state.timesteps],
            xAxis: state.timeData[state.timesteps],
            label:{
                distance:echartsResize(0),
                color: '#fff',
            },
            lineStyle:{
                color:"#fff",
                type:"dashed",
            },
        }
    ].concat(state.timeList):state.timeList)
    line=lineChart.value
    option=lineOption.value
    temp=lineOption.value.series.length-1
    //切换下拉1 供电风险2 切换时刻3 特殊工况(暂无)4 悬浮显示5 滑动时间轴6 风险列表悬浮显示7
    if(type==3||type==5||type==7){//切换时刻
        if(type==3){
          console.log('切换时刻');
          initAllData()
        }else if(type==7){
          console.log('风险列表悬浮显示');
        }else{
          console.log('悬浮显示');
        }
        option.series[temp].markLine.data =lineData
        if(state.lineEnd){
            option.dataZoom[0].start = lineStart
            option.dataZoom[0].end = lineEnd
        }
        option.tooltip.showContent = state.isShowTip   
        line.setOption(option);
        
    }else if(type==2){//点击风险列表
        console.log('风险列表');
        option.series[temp].markLine.data =lineData
      // option.tooltip.showContent = state.isShowTip   
        line.setOption(option);
        lineChart.value.dispatchAction({
            type: 'showTip',
            seriesIndex:0,
            dataIndex:state.timesteps
        })
    }else{//选中时刻后 拖动时间轴
        console.log('其他');
        option.series[temp].markLine.data =lineData
        option.tooltip.showContent = true
        line.setOption(option,true);
    }
}
const initAllData = async () => {
    const baseArea = {
        "三门峡-新安": {
        },
        "平顶山北": {
        },
        "洛南": {
        },
        "平顶山南": {
        },
        "信阳": {
        },
        "周口": {
        },
        "驻马店": {
        },
        "漯河": {
        },
        "安鹤": {
        },
        "濮阳": {
        },
        "焦作": {
        },
        "新乡": {
        },
        "济源-洛北": {
        },
        "郑州南": {
        },
        "郑州北": {
        },
        "开封": {
        },
        "商丘": {
        },
        "许昌": {
        },
        "南阳":{}
    }
    store.showModal()
    const {indicators,psm_color,new_consump_rate} = (await getAllIndicator({
        case_id: state.case_id,
        time_no: state.timesteps,
        area: state.partition,
        is_short:2
    })).data
    state.partitionMarginList=Object.keys(psm_color).map(item=>{
        return{
            name:item,
            value:psm_color[item],
            values:indicators[item].power_supply_margin,
        }
    }).sort((a, b) => a.values - b.values)
    state.mapList1=Object.keys(baseArea).map(item=>{
        return{
            name:item,
            value:psm_color[item]==undefined?2:psm_color[item],
            values:indicators[item]==undefined?undefined:indicators[item].power_supply_margin,
        }
    })
    state.partitionEnergyList = Object.keys(new_consump_rate).map(item=>{
        return{
            name:item,
            value:new_consump_rate[item].newenergy_proportion,
            values:new_consump_rate[item],
        }
    }).sort((a, b) => b.value - a.value).filter(item => item.name != 'all')
    state.mapList2=Object.keys(baseArea).map(item=>{
        return{
            name:item,
            value: new_consump_rate[item] == undefined ? 1 : new_consump_rate[item].newenergy_proportion,
            values: new_consump_rate[item] == undefined ? undefined : new_consump_rate[item],
            
        }
    })
     const {data} = await  getNetworkData({
        case_id:state.case_id,
        time_no:state.timesteps,
        area: state.partition,
        is_short:2
    })
    state.mapData = data
    initMap()
    store.hiddenModal()
}
const initLine=(type,index,name)=>{
    lineOption.value = getLineOption(state.lineData,state.timeData,index,name,state.lineStart,state.lineEnd,[],undefined,undefined)
    drawLine(state.timesteps,type,name)
    lineChart.value.off('updateAxisPointer')
    lineChart.value.on('updateAxisPointer', function (event) {
    const xAxisInfo = event.axesInfo[0];  
        if (xAxisInfo) {
            if(state.timesteps === xAxisInfo.value){
                if(state.isShowTip == true){
                    drawLine(xAxisInfo.value,5)
                    return
                    }
                    return
            }else if(state.isShowTip == true){
                drawLine(xAxisInfo.value,7)
                return
            }
            state.timesteps = xAxisInfo.value
            drawLine(xAxisInfo.value,3)
        }else{
            
        }
    });
    lineChart.value.off('click')
    lineChart.value.on('click',(e)=>{
        if(e.componentType=='markLine'){
            if(e.name.includes('-')) return
            let index = state.timeData.findIndex(item=>item==e.data.value)
            state.timesteps = index
            drawLine(index,3,e.name)
        }
    })
    lineChart.value.on('datazoom', (event)=> {
        state.lineStart = event.start
        state.lineEnd = event.end
        if((event.end-event.start)>25){
            if(((event.end-event.start)>=state.datazoomRange)&&state.datazoomRange) return
            state.isDatazoom = true
            initLine(6)
            state.datazoomRange = state.lineEnd-state.lineStart
        }else{
            if(state.isDatazoom){
                initLine(6)
                state.isDatazoom = false
                state.datazoomRange = ''
            }
        }
    })
    lineChart.value.on('mouseover',(e)=>{
        if(e.componentType=='markLine'){
            state.isShowTip = true
            lineChart.value.dispatchAction({
                type: 'showTip',
                seriesIndex:0,
                dataIndex:state.timeData.findIndex(item=>item==e.value)
            })
        }
    })
    lineChart.value.on('mouseout',(e)=>{
        if(e.componentType=='markLine'){
            state.isShowTip = false
            lineChart.value.dispatchAction({
                type: 'hideTip'
            })
        }
    })
}
onMounted(async() => {
    store.showModal()
    await registerMap(state.partition)
    mapChart.value = markRaw(echarts.init(map.value))
    lineChart.value = markRaw(echarts.init(line.value))
    lineChart1.value = markRaw(echarts.init(line1.value))
    lineChart2.value = markRaw(echarts.init(line2.value))
    lineChart3.value = markRaw(echarts.init(line3.value))
    lineChart4.value = markRaw(echarts.init(line4.value))
    pieChart1.value = markRaw(echarts.init(pie1.value))
    pieChart2.value = markRaw(echarts.init(pie2.value))
    if(state.case_id){
        
    } else {
        const res = (await getCaseDataApi({ is_short: 2 })).data
        state.case_id = res[0].id ? res[0].id : undefined
        state.year =res[0].year
    }
    await initData()
    await initAllData()
    initLine(1, state.timesteps)
    store.hiddenModal()
})
watch(() => [state.mapChecked1, state.mapType], ([v1, v2]) => {
    initMap(true)
})
</script>
<style lang="scss" scoped>
    .main_content4_index1{
        .top_left_content{
            .absolute{
                color: rgb(36, 191, 227);
                font-family: 思源黑体;
                font-size: 16px;
                font-weight: 700;
                line-height: 16px;
                letter-spacing: 2px;
                text-align: center;
                right: 10px;
                top: 50px;
                text-decoration: underline;
                z-index: 1;
                &:hover{
                    cursor: pointer;
                }
            }
            .absolute_bottom{
                top: 445px;
                letter-spacing: 0px;
            }
            .line{
                height: 135px;
            }
        }
        .top_right_content{
            >.grid{
                .active{
                    color: rgb(242, 160, 2);
                }
                padding: 20px 20px 0;
                grid-template-columns: 1fr 1fr;
                grid-gap: 20px;
                >div{
                    background: rgba(46, 126, 194, 0.06);
                    border: 2px solid;
                    border-image: linear-gradient(45deg, transparent 0%, #05BEC3 50%, transparent 100%) 1; /* 渐变边框 */
                    >div{
                        >div:first-child,>div:last-child>div{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            p{
                                text-align: center;
                                font-size: 14px;
                            }
                        }
                        >div:first-child{
                            >p{
                                position: relative;
                                display: flex;
                                justify-content: center;
                                line-height: 32px;
                                &::after {
                                    position: absolute;
                                    content: '';
                                    height:2px;
                                    display: block;
                                    background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
                                    border-radius: 100%;
                                    width: 70%;
                                    bottom: 0;
                                }
                            }
                        }
                        >div:last-child>div{
                            p{
                                line-height: 28px;
                            }
                        }
                    }
                    .scroll{
                        overflow-y: auto;
                        height: 210px;
                    }
                }
            }
            .flex{
                width: 100%;
                height: 190px;
                margin-top: 5px;
                >div:first-child{
                    width: 300px;
                    >div:first-child{
                        grid-template-columns: 1fr 1fr;
                        border-radius: 2px;
                        background: rgb(39, 54, 82);
                        height: 26px;
                        width: 260px;
                        overflow: hidden;
                        margin: 0 auto;
                        >p{
                            width: 130px;
                            color: rgb(255, 255, 255);
                            font-family: 思源黑体;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 26px;
                            letter-spacing: 0px;
                            text-align: center;
                            &:hover{
                                cursor: pointer;
                            }
                        }
                        .active{
                            color: rgb(3, 22, 29);
                            background: rgb(85, 233, 188);
                        }
                    }
                    .pie{
                        height: 160px;
                        width: 300px;
                    }
                }
                >div:last-child{
                    flex: 1;
                    >div:first-child,>div:last-child>div{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        p{
                            text-align: center;
                            font-size: 12px;
                        }
                    }
                    >div:first-child{
                        >p{
                            position: relative;
                            display: flex;
                            justify-content: center;
                            line-height: 28px;
                            &::after {
                                position: absolute;
                                content: '';
                                height:2px;
                                display: block;
                                background: linear-gradient(90deg, #041620 0%, #05BEC3 52%, #051721 100%);
                                border-radius: 100%;
                                width: 70%;
                                bottom: 0;
                            }
                        }
                    }
                    >div:last-child>div{
                        p{
                            line-height: 24px;
                        }
                        >p:first-child{
                            color: #FFFFB5;
                        }
                    }
                    .scroll{
                        overflow-y: auto;
                        height: 165px;
                    }
                }
            }
        }
        .top_middle_content{
            .mapInfo{
                position: absolute;
                right: 10px;
                bottom: 10px;
                width: 130px;
                height: 68px;
                border-radius: 3px;
                background: rgba(0, 131, 166, 0.22);
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding:5px;
                p{
                    color: rgb(255, 255, 255);
                    font-family: 思源黑体;
                    font-size: 12px;
                    font-weight: 400;
                    letter-spacing: 2px;
                    span{
                        color: rgb(242, 160, 2);
                        font-family: 思源黑体;
                        font-size: 12px;
                        font-weight: 700;
                        letter-spacing: 1px;
                    }
                }
            }
            .map_time{
                position: absolute;
                left: 5px;
                top: 5px;
                color: #fff;
                font-size: 22px;
                line-height: 32px;
                text-align: center;
                font-weight: bolder;
                color: $activeTextColor;
            }
        }
    }
</style>