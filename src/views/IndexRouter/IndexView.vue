<template>
    <!-- <router-view v-slot="{ Component }">
        <keep-alive>
            <component :is="Component" />
        </keep-alive>
    </router-view> -->
    <router-view></router-view>
</template>
<script setup>
    import { dataStore } from '@/store/dataStore'
    import { storeToRefs } from 'pinia'
    import { onUnmounted,reactive,onMounted } from '@vue/runtime-core'
    import { getBaseOptionsApi,getCaseOptionsApi } from '@/api/index'
    const store = dataStore()
    const { caseId1,caseId2,timeData1,timeData2,timeStep1,timeStep2,loadData1,loadData2,year1,year2,partition,partitionOptions,yearOptions,caseOptions } = storeToRefs(store)
    const state = reactive({
        partitionOptions,
        yearOptions,
        caseOptions
    })
    // const beforeDistory = ()=>{
    //     sessionStorage.setItem('caseId1',caseId1.value?caseId1.value:'')
    //     sessionStorage.setItem('caseId2',caseId2.value?caseId2.value:'')
    //     sessionStorage.setItem('timeData1',timeData1.value?JSON.stringify(timeData1.value):'')
    //     sessionStorage.setItem('timeData2',timeData2.value?JSON.stringify(timeData2.value):'')
    //     sessionStorage.setItem('loadData1',loadData1.value?JSON.stringify(loadData1.value):'')
    //     sessionStorage.setItem('loadData2',loadData2.value?JSON.stringify(loadData2.value):'')
    //     sessionStorage.setItem('timeStep1',timeStep1.value?timeStep1.value:'')
    //     sessionStorage.setItem('timeStep2',timeStep2.value?timeStep2.value:'')
    //     sessionStorage.setItem('year1',year1.value?year1.value:'')
    //     sessionStorage.setItem('year2',year2.value?year2.value:'')
    //     sessionStorage.setItem('partition',partition.value?partition.value:'全省')
    //     sessionStorage.setItem('searchTime',searchTime.value?JSON.stringify(searchTime.value):'')
    // }
    // onUnmounted(()=>{
    //     window.removeEventListener('beforeunload',beforeDistory)
    // })
    // window.addEventListener('beforeunload',beforeDistory);
    onMounted(()=>{
        getBaseOptionsApi().then(res=>{
            state.partitionOptions = res.data.powers.map(item=>{
                return{
                    label:item,
                    value:item,
                }
            })
            state.yearOptions = res.data.years.map(item=>{
                return {
                    label:item,
                    value:item
                }
            })
        })
        getCaseOptionsApi().then(res=>{
            state.caseOptions = res.data.scenes
        })
    })
</script>
<style lang="scss" scoped>

</style>