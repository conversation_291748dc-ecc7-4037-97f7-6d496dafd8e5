import request from '@/request/axios'
// 获取所有case
export const getCaseDataApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/case/all',
        params: data
    })
}
export const getLogin = data => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/user/login',
        data
    })
}
export const getBaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/power/year/list',
        params: data
    })
}
export const getCaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/scene/list',
        params: data
    })
}
export const getTideMap = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/power/tide/map',
        params: data
    })
}
export const getInterfaceMap = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/power/interface/map',
        params: data
    })
}
export const getCapacityIndicator = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/power/capacity/indicator',
        params: data
    })
}
export const getLineTrafo = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/power/interface/line/trafo',
        data
    })
}
export const getOutputCapacity = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/power/output/capacity',
        params: data
    })
}
export const getFeedinData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/realtime/feedin/data',
        params: data
    })
}
export const getLoadData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/load/data',
        data
    })
}
export const getLoadTime = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/load/base_time',
        params: data
    })
}
export const getLoadTrend = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/load/trend',
        data
    })
}
export const getLoadUpdate = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/load/update/max',
        data
    })
}
export const getOutputData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/output/data',
        data
    })
}
export const getOutputDistribute = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/output/distribute',
        data
    })
}
export const getUsualMap = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/map',
        data
    })
}
export const getCapacityData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/capacity/data',
        data
    })
}
export const getPowerData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/power/data',
        data
    })
}
export const getHourRate = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/usual/hour/rate',
        data
    })
}
export const getStationData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/station/data',
        data
    })
}
export const getEnergyOutput = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/new_energy/output',
        data
    })
}
export const getCurveList = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/acdc/curve/list',
        params: data
    })
}
export const getPowerYear = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/acdc/send/power/year',
        data
    })
}
export const getPowerDay = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/acdc/send/power/day',
        data
    })
}
export const getAcdcPowerData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/acdc/power/data',
        data
    })
}
export const getAcdcPowerDistribute = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/acdc/power/distribute',
        data
    })
}
export const getTideData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/interface/tide/data',
        data
    })
}
export const getInterfaceNameList = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/interface/name/list',
        params: data
    })
}
export const getInterfaceQuery = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/interface/query',
        data
    })
}
export const getInterfaceArea = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/qs/interface/zone/list',
        params: data
    })
}
export const getInterfaceData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/qs/interface/level/data',
        data
    })
}
export const getInterfaceLine = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/interface/line',
        data
    })
}


export const getZhuangjiCapacity = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/zhuangji/capacity',
        params: data
    })
}

export const getExtremeData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/annual/extreme/data',
        params: data
    })
}
export const getReloadList = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/device/reload/list',
        params: data
    })
}
export const getTimeSequence = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/time/sequence',
        params: data
    })
}
export const getLaodratioTimestep = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/device/laodratio/timestep',
        params: data
    })
}
export const getPieRate = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/energy/xiaona/rate',
        params: data
    })
}
export const getBalanceData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/power/balance/data',
        params: data
    })
}
export const getDeducePowerData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/deduce/power/data',
        data
    })
}
export const getTrafoCapability = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/zone/trafo/capability',
        params: data
    })
}
export const getAllIndicator = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/zone/all/indicator',
        params: data
    })
}
export const getNetworkData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/map/network/data',
        params: data
    })
}
export const getLoadSufficiencey = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/zone/load/sufficiencey',
        params: data
    })
}
export const getAllPsm = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/zone/all/psm',
        params: data
    })
}
export const getDeviceList = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/curve/device/list',
        params: data
    })
}
export const getCurveSeriesData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/curve/series/data',
        params: data
    })
}
export const getCaseSeriesData = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/case/series/data',
        params: data
    })
}
export const getInterfaceApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/interface/detail/data',
        params: data
    })
}
export const getGenListApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/case/initoff/gens',
        params: data
    })
}
export const getDetailResultLineApi = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/curve/device/info',
        data
    })
}
export const getHistoryFeedinData = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/history/feedin/data',
        data
    })
}
export const getInterfaceList = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/interface/tide',
        data
    })
}
export const getInterfaceLists = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/acline/trafo/qs/data',
        params: data
    })
}
export const DownloadData = (data) => {
	return request({
		method: 'POST',
		url: '/api/v1/henan/interface/tide/download',
		data,
		responseType: 'blob'
	})
}
