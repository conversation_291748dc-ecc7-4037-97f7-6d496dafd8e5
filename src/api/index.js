import request from '@/request/axios'
// 获取所有case
export const getCaseDataApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/case/all',
        params: data
    })
}
export const getLogin = data => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/user/login',
        data
    })
}
export const getBaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/power/year/list',
        params: data
    })
}
export const getCaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/scene/list',
        params: data
    })
}