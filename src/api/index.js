import request from '@/request/axios'

// ==================== 原有接口 ====================
// 获取所有case
export const getCaseDataApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/case/all',
        params: data
    })
}

export const getLogin = data => {
    return request({
        method: 'POST',
        url: '/api/v1/henan/user/login',
        data
    })
}

export const getBaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/power/year/list',
        params: data
    })
}

export const getCaseOptionsApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan/history/scene/list',
        params: data
    })
}

// ==================== 河南经研院接口 ====================

/**
 * 获取全年供电数据（全年极值信息）
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回包含最高调度负荷、供电缺口、最大峰谷差、最大调峰缺口等数据
 */
export const getAnnualExtremeDataApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/annual/extreme/data',
        params: data
    })
}

/**
 * 获取装机容量占比
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回装机容量占比数据
 */
export const getZhuangjiCapacityApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/zhuangji/capacity',
        params: data
    })
}

/**
 * 获取全网各分区全年6维指标最小裕度值及时刻
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 算例ID（必填）
 * @param {number} [data.time_no] - 时刻（可选）
 * @param {string} [data.area='全省'] - 区域范围（可选，默认全省）
 * @returns {Promise} 返回6维指标数据
 */
export const getZoneAllIndicatorApi = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan_jyy/zone/all/indicator',
        data
    })
}

/**
 * 获取地图上线以及点的数据（电网拓扑地图及分区）
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 算例ID（必填）
 * @param {number} [data.time_no] - 时刻（可选）
 * @param {string} [data.value_type] - 类型（可选）
 * @param {string} [data.area='全省'] - 区域范围（可选，默认全省）
 * @returns {Promise} 返回地图数据
 */
export const getMapNetworkDataApi = (data) => {
    return request({
        method: 'POST',
        url: '/api/v1/henan_jyy/map/network/data',
        data
    })
}

/**
 * 获取各分区的供电裕度数据
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回供电裕度数据
 */
export const getZoneAllPsmApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/zone/all/psm',
        params: data
    })
}

/**
 * 获取所有典型时刻的供电裕度
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回典型时刻供电裕度数据
 */
export const getTypicalAllPsmApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/typical/all/psm',
        params: data
    })
}

/**
 * 获取所有分区的调节能力
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回调节能力数据
 */
export const getZoneAllReverseApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/zone/all/reverse',
        params: data
    })
}

/**
 * 获取电力平衡分析曲线
 * @param {Object} data - 请求参数
 * @param {string} data.case_id - 用例ID（必填）
 * @returns {Promise} 返回电力平衡分析曲线数据，包含时间、载荷曲线、最小可调出力、最大电力资源
 */
export const getCurveBalanceApi = (data) => {
    return request({
        method: 'GET',
        url: '/api/v1/henan_jyy/curve/balance',
        params: data
    })
}