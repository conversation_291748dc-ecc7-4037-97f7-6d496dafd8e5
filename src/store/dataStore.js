import { defineStore } from 'pinia';
export const dataStore = defineStore({
    id: 'dataState',
    state: () => ({
        caseId1:undefined,
        caseId2:undefined,
        timeStep1:0,
        timeStep2:0,
        timeData1:[],
        timeData2:[],
        loadData1:[],
        loadData2:[],
        year1:'',
        year2:'',
        partition:'全省',
        partitionOptions:[],
        yearOptions:[],
        caseOptions:{},
        token: sessionStorage.getItem('token') ? sessionStorage.getItem('token') : undefined,
        end_time:sessionStorage.getItem('end_time')?sessionStorage.getItem('end_time'):undefined,
        // caseId1:sessionStorage.getItem('caseId1'),
        // caseId2:sessionStorage.getItem('caseId2'),
        // timeStep1:sessionStorage.getItem('timeStep1')?sessionStorage.getItem('timeStep1'):0,
        // timeStep2:sessionStorage.getItem('timeStep2')?sessionStorage.getItem('timeStep2'):0,
        // timeData1:sessionStorage.getItem('timeData1')?JSON.parse(sessionStorage.getItem('timeData1')):[],
        // timeData2:sessionStorage.getItem('timeData2')?JSON.parse(sessionStorage.getItem('timeData2')):[],
        // loadData1:sessionStorage.getItem('loadData1')?JSON.parse(sessionStorage.getItem('loadData1')):[],
        // loadData2:sessionStorage.getItem('loadData2')?JSON.parse(sessionStorage.getItem('loadData2')):[],
        // year1:sessionStorage.getItem('year1')?sessionStorage.getItem('year1'):'',
        // year2:sessionStorage.getItem('year2')?sessionStorage.getItem('year2'):'',
        // partition:sessionStorage.getItem('partition')?sessionStorage.getItem('partition'):'全省',
        loading: false,
    }),
    getters:{
        
    },
    actions: {
        changeTime(val){
            this.searchTime = val
        },
        changeCase(val){
            this.caseId = val;
        },
        hiddenModal(){
            this.loading = false;
        },
        showModal(){
            this.loading = true
        }
    },
});