{"name": "guaranteeplatform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vue/reactivity": "^3.5.18", "@vue/runtime-core": "^3.5.18", "amfe-flexible": "^2.2.1", "ant-design-vue": "^4.0.6", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "dayjs": "^1.11.13", "echarts": "^5.4.2", "file-saver": "^2.0.5", "js-md5": "^0.8.3", "pinia": "^2.0.35", "postcss-pxtorem": "^6.0.0", "vue": "^3.2.47", "vue-router": "^4.1.6", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.1", "@vitejs/plugin-vue-jsx": "^3.0.1", "sass": "^1.62.1", "unplugin-vue-components": "^0.25.2", "vite": "^4.3.2"}}