{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.vue", "src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}