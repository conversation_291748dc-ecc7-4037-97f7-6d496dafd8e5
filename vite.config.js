import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import vueJsx from "@vitejs/plugin-vue-jsx";
import autoprefixer from "autoprefixer"
import postcsspxtorem from "postcss-pxtorem"
export default defineConfig({
    build: {
        assetsDir: "38_assets",
    },
    plugins: [
        vue(),
        vueJsx(),
        Components({
            dirs: ['src/components'],
            resolvers: [AntDesignVueResolver({
                importStyle: false, // css in js
                resolveIcons: true, // css in js
            })]
        })
    ],
    resolve:{
        alias:[
            {find:'@',replacement:resolve(__dirname,'src')},
        ]
    },
    css: {
        preprocessorOptions: {
            scss: {
                additionalData:
                '@import "./src/styles/variable.scss";@import "./src/styles/antd.scss";@import "./src/styles/mixin.scss";@import "./src/styles/common.scss";@import "./src/styles/main.scss";',
            },
        },
        postcss: {
            plugins: [
                autoprefixer({
                    overrideBrowserslist: [
                        "Android 4.1",
                        "iOS 7.1",
                        "Chrome > 31",
                        "ff > 31",
                        "ie >= 8",
                        "last 10 versions", // 所有主流浏览器最近10版本用
                    ],
                    grid: true
                }),
                postcsspxtorem({
                    rootValue: 192, // 设计稿宽度的1/ 10 例如设计稿按照 1920设计 此处就为192
                    propList: ["*",], // 除 border 外所有px 转 rem
                    // selectorBlackList: [".el-"], // 过滤掉.el-开头的class，不进行rem转换
                })
            ],
        },
    },
    server: {
        host: '0.0.0.0',
        port: 7777,
        // open: true,  // 是否自动在浏览器打开
        proxy: {
            '/api': {
                target:'https://henan.bg.tode.ltd',//new
                changeOrigin:true,//虚拟的站点需要更管origin
                rewrite: (path) => path.replace(/^\/api/, '')
            },
            '/test': {
                target:'https://*************',//new
                changeOrigin:true,//虚拟的站点需要更管origin
                rewrite: (path) => path.replace(/^\/test/, '')
            }
        }
    }
})
