import vue from '@vitejs/plugin-vue'
import vueJsx from "@vitejs/plugin-vue-jsx"
import autoprefixer from 'autoprefixer'
import { resolve } from 'path'
import postcsspxtorem from 'postcss-pxtorem'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
export default defineConfig({
    build: {
        assetsDir: "38_assets",
    },
    plugins: [
        vue(),
        vueJsx(),
        Components({
            dirs: ['src/components'],
            resolvers: [AntDesignVueResolver({
                importStyle: false, // css in js
                resolveIcons: true, // css in js
            })]
        })
    ],
    resolve: {
        alias: [
            { find: '@', replacement: resolve(__dirname, 'src') },
        ]
    },
    css: {
        preprocessorOptions: {
            scss: {
                additionalData: `
                    @use "./src/styles/variable.scss" as *;
                    @use "./src/styles/mixin.scss" as *;
                `,
                silenceDeprecations: ['legacy-js-api'],
            },
        },
        postcss: {
            plugins: [
                autoprefixer({
                    overrideBrowserslist: [
                        "Android 4.1",
                        "iOS 7.1",
                        "Chrome > 31",
                        "ff > 31",
                        "last 10 versions"
                    ],
                    grid: false, // 完全禁用 grid 相关的 autoprefixer 处理
                    ignoreUnknownVersions: true
                }),
                postcsspxtorem({
                    rootValue: 192,
                    propList: ["*"],
                    selectorBlackList: [".el-", ".ant-"]
                })
            ],
        },
    },
    server: {
        host: '0.0.0.0',
        port: 7777,
        // open: true,  // 是否自动在浏览器打开
        proxy: {
            '/api': {
                target: 'https://henan.bg.tode.ltd',//new
                changeOrigin: true,//虚拟的站点需要更管origin
                rewrite: (path) => path.replace(/^\/api/, '')
            },
            '/test': {
                target: 'https://*************',//new
                changeOrigin: true,//虚拟的站点需要更管origin
                rewrite: (path) => path.replace(/^\/test/, '')
            }
        }
    }
})
